"""
Django settings for game project.

Generated by 'django-admin startproject' using Django 3.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'mfdqs5&mjpbws@c6wuk646%qs6@3x-p=v3eezke0*w)q_0i(8p'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']
# ['*************', 'localhost', '0.0.0.0:8000', '127.0.0.1',]
INTERNAL_IPS = ['*************']#['************']

# Application definition
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'web',
    #'captcha',
    'debug_toolbar',
]
MIDDLEWARE = [
    # 'django.middleware.cache.UpdateCacheMiddleware',  # redis全站缓存
    'debug_toolbar.middleware.DebugToolbarMiddleware', #测试工具
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    #'django.middleware.gzip.GZipMiddleware',
    # 'django.middleware.cache.FetchFromCacheMiddleware',  # redis全站缓存
]

ROOT_URLCONF = 'game.urls'
#MIDDLEWARE_CLASSES = (
#    'django.middleware.gzip.GZipMiddleware',
#)

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'game.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases
"""
# 使用sqlite数据库
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': str(BASE_DIR / 'db.sqlite3'),
        'CONN_MAX_AGE': 600,  # 持久连接60秒
    }
}
"""
# 默认django使用两个标准的模版加载器
TMPLATE_LOADERS = (
    'django.template.loaders.filesystem.Loader',
    'django.template.loaders.app_directories.Loader',
)

# 使用sqlite数据库 (临时用于启动测试)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': str(BASE_DIR / 'db.sqlite3'),
        'CONN_MAX_AGE': 600,  # 持久连接60秒
    }
}

# 使用mysql数据库 (生产环境)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'mysqldb_zzsc',
#         'HOST': '127.0.0.1',
#         'PORT': 3306,
#         #'HOST': '***********',
#         #'PORT': 3306,
#         'USER': 'mysqldb_zzsc',
#         'PASSWORD': '201357',
#         'CONN_MAX_AGE': 600, # 持久连接60秒
#     }
# }
"""
# 使用mysql数据库
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'mysqldb_zzsc',
        'HOST': '***********',
        'PORT': 3306,
        'USER': 'root',
        'PASSWORD': 'a!8659741',
        'CONN_MAX_AGE': 10, # 持久连接60秒
    }
}
"""
# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/
import os
STATIC_URL = '/static/'
#STATICFILES_DIRS = [
#    (os.path.join(BASE_DIR, '/static'))
#]
STATIC_ROOT = "/static/"
# 配置session存储在前面配置的redis数据库中
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
#2. 设置redis作为django的缓存设置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/10",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            #"PASSWORD": "a!8659741",
        }
    }
}
# Session的cookie保存在浏览器上时的key，即：sessionid＝随机字符串(默认)
SESSION_COOKIE_NAME = 'sessionid'
# Session的cookie失效日期(2周)(默认)
SESSION_COOKIE_AGE = 60 * 60 * 24 * 7 *2
# Session的cookie保存的域名(默认)
SESSION_COOKIE_DOMAIN = None
# 是否Https传输cookie(默认)
SESSION_COOKIE_SECURE = False
# Session的cookie保存的路径(默认)
SESSION_COOKIE_PATH = '/'
# 是否Session的cookie只支持http传输(默认)
SESSION_COOKIE_HTTPONLY = True
# 是否每次请求都保存Session，默认修改之后才保存(默认)
SESSION_SAVE_EVERY_REQUEST = False
# 是否关闭浏览器使得Session过期(默认)
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
# 存储session数据默认使用的模块
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
# session数据的序列化类
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'
