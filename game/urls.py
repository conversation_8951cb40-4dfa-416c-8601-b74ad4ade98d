from django.contrib import admin
from django.urls import path
#from django.conf.urls import url
from django.urls import re_path as url
from web import views
from web import admin_views
from django.conf.urls import include
from django.conf import settings
from django.conf.urls.static import static

from django.urls import re_path, include




urlpatterns = [
                  url(r'^$', admin_views.index),
                  url(r'^forum/', admin_views.forum),
                  url(r'^forum_content/', admin_views.forum_content),
                  url(r'^forum_posting/', admin_views.forum_posting),
                  path('admin/', admin.site.urls),
                  path('wap_xiaoshou/', admin_views.wap_xiaoshou),
                  url(r'^login/', admin_views.login),
                  url(r'^logout/', admin_views.logout),
                  url(r'^change_password/', admin_views.change_password),
                  url(r'^change_security/', admin_views.change_security),
                  url(r'^set_password/', admin_views.set_password),
                  url(r'^index/', admin_views.index),
                  url(r'^register/', admin_views.register),
                  url(r'^login_register/', admin_views.login_register),
                  # url(r'^captcha/', include('captcha.urls')),
                  #path('wap/', views.wap),
                  url(r'^wap/', views.wap),
                  # url(r'^wap_message/', admin_views.wap_message),
                  url(r'^wap_item_choice/', admin_views.wap_item_choice),
                  url(r'^wap_rest/', admin_views.wap_rest),
                  url(r'^wap_is_designer/', admin_views.wap_is_designer),
                  url(r'^wap_auction_area_name/', admin_views.wap_auction_area_name),
                  url(r'^wap_auction_area_name_change/', admin_views.wap_auction_area_name_change),
                  url(r'^wap_ranking_area_name/', admin_views.wap_ranking_area_name),
                  url(r'^wap_ranking_area_name_change/', admin_views.wap_ranking_area_name_change),
                  url(r'^wap_game_area_name/', admin_views.wap_game_area_name),
                  url(r'^wap_game_area_name_change/', admin_views.wap_game_area_name_change),
                  url(r'^wap_tupian_area_name/', admin_views.wap_tupian_area_name),
                  url(r'^wap_tupian_area_name_list/', admin_views.wap_tupian_area_name_list),
                  url(r'^wap_tupian_area_name_list_change/', admin_views.wap_tupian_area_name_list_change),
                  url(r'^wap_img_area_name/', admin_views.wap_img_area_name),
                  url(r'^wap_img/', admin_views.wap_img),
                  url(r'^wap_img_area_name_list/', admin_views.wap_img_area_name_list),
                  url(r'^wap_img_area_name_list_change/', admin_views.wap_img_area_name_list_change),
                  url(r'^wap_type/', admin_views.wap_type),
                  url(r'^wap_type_create/', admin_views.wap_type_create),
                  url(r'^wap_type_check/', admin_views.wap_type_check),
                  url(r'^wap_event/', admin_views.wap_event),
                  url(r'^wap_event_list/', admin_views.wap_event_list),
                  url(r'^wap_event_list_check/', admin_views.wap_event_list_check),
                  url(r'^wap_event_list_check_list/', admin_views.wap_event_list_check_list),
                  url(r'^wap_index/', admin_views.wap_index),
                  url(r'^wap_3g_index/', admin_views.wap_3g_index),
                  url(r'^wap_choose/', admin_views.wap_choose),
                  url(r'^wap_base/', admin_views.wap_base),
                  url(r'^wap_admin/', admin_views.wap_admin),
                  url(r'^wap_player/', admin_views.wap_player),
                  url(r'^wap_area_name/', admin_views.wap_area_name),
                  url(r'^wap_area_name_list_create_map/', admin_views.wap_area_name_list_create_map),
                  url(r'^wap_area_name_list_npc_count/', admin_views.wap_area_name_list_npc_count),
                  url(r'^wap_area_name_create_map/', admin_views.wap_area_name_create_map),
                  url(r'^wap_area_name_change_map/', admin_views.wap_area_name_change_map),
                  url(r'^wap_area_name_list/', admin_views.wap_area_name_list),
                  url(r'^wap_area_name_list_see/', admin_views.wap_area_name_list_see),
                  url(r'^wap_area_name_list_sell/', admin_views.wap_area_name_list_sell),
                  url(r'^wap_area_name_list_skill/', admin_views.wap_area_name_list_skill),
                  url(r'^wap_area_name_list_npc/', admin_views.wap_area_name_list_npc),
                  url(r'^wap_area_name_list_item/', admin_views.wap_area_name_list_item),
                  url(r'^wap_area_name_list_item_check/', admin_views.wap_area_name_list_item_check),
                  url(r'^wap_area_name_list_npc_check/', admin_views.wap_area_name_list_npc_check),
                  url(r'^wap_area_name_list_export/', admin_views.wap_area_name_list_export),
                  url(r'^wap_area_name_list_attribute/', admin_views.wap_area_name_list_attribute),
                  url(r'^wap_area_name_list_operation/', admin_views.wap_area_name_list_operation),
                  url(r'^wap_area_name_list_operation_check/', admin_views.wap_area_name_list_operation_check),
                  url(r'^wap_area_name_list_operation_event/', admin_views.wap_area_name_list_operation_event),
                  url(r'^wap_area_name_list_operation_event_list/', admin_views.wap_area_name_list_operation_event_list),
                  url(r'^wap_area_name_list_event/', admin_views.wap_area_name_list_event),
                  url(r'^wap_area_name_list_event_check/', admin_views.wap_area_name_list_event_check),
                  url(r'^wap_area_name_list_event_check_list/', admin_views.wap_area_name_list_event_check_list),
                  url(r'^wap_attribute/', admin_views.wap_attribute),
                  url(r'^wap_attribute_basic/', admin_views.wap_attribute_basic),
                  url(r'^wap_attribute_def/', admin_views.wap_attribute_def),
                  url(r'^wap_attribute_def_create/', admin_views.wap_attribute_def_create),
                  url(r'^wap_attribute_def_check/', admin_views.wap_attribute_def_check),
                  url(r'^wap_page/', admin_views.wap_page),
                  url(r'^wap_page_list/', admin_views.wap_page_list),
                  url(r'^wap_page_list_export/', admin_views.wap_page_list_export),
                  url(r'^wap_page_list_import/', admin_views.wap_page_list_import),
                  url(r'^wap_page_create/', admin_views.wap_page_create),
                  url(r'^wap_page_list_create/', admin_views.wap_page_list_create),
                  url(r'^wap_page_list_check/', admin_views.wap_page_list_check),
                  url(r'^wap_page_list_event/', admin_views.wap_page_list_event),
                  url(r'^wap_page_list_event_list/', admin_views.wap_page_list_event_list),
                  url(r'^wap_page_special/', admin_views.wap_page_special),
                  url(r'^wap_operation_import/', admin_views.wap_operation_import),
                  url(r'^wap_task/', admin_views.wap_task),
                  url(r'^wap_task_area_name/', admin_views.wap_task_area_name),
                  url(r'^wap_task_attribute/', admin_views.wap_task_attribute),
                  url(r'^wap_task_item/', admin_views.wap_task_item),
                  url(r'^wap_expression/', admin_views.wap_expression),
                  url(r'^wap_expression_create/', admin_views.wap_expression_create),
                  url(r'^wap_expression_check/', admin_views.wap_expression_check),
              ]# + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
              
if settings.DEBUG:
    import debug_toolbar
    urlpatterns.append(path('__debug__/', include(debug_toolbar.urls)))