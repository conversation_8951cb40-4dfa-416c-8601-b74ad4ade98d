from django.db import models
import ujson
import random
import datetime
import re
# IntegerField整型
# NullBooleanField支持Null,True,False三种值

# Create your models here.
from django.db.models import CharField

class BoothArea(models.Model):
    # 摊位区分
    id = models.AutoField(primary_key=True)  
    booth_name = models.CharField(max_length=20, default=0, db_index=True)  # 交易行名
    money_name = models.CharField(max_length=20, default=0)  # 幣種名稱
    money_sx = models.CharField(max_length=20, default=0)  # 幣種屬性
    procedures = models.IntegerField(default=0)#手续费

# 系统属性
class SystemAttribute(models.Model):
    # 系统属性搭载 物品合成
    id = models.AutoField(primary_key=True)  # 战场ID
    area_name = models.CharField(max_length=40, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(SystemAttribute, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
# 游戏C属性
class GameAttributeNew(models.Model):
    """游戏C属性"""
    id = models.AutoField(primary_key=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        # cache.set("GameAttribute_cache_{}".format(self.id), self)
        super(GameAttributeNew, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


# 公共属性
class PublicAttributeNew(models.Model):
    """游戏g属性"""
    id = models.AutoField(primary_key=True)
    area_id = models.CharField(max_length=5, default=0)  # 分区
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        # cache.set("PublicAttribute_cache_{}".format(self.id), self)
        super(PublicAttributeNew, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
        
class Img(models.Model):
    """本地图片"""
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=50, default=0)  # 属性名称
    name = models.CharField(max_length=50, default=0)  # 名称
    value = models.ImageField(upload_to='./web/static/img/')  # 值
    attribute = models.CharField(max_length=50, default=0, db_index=True)  # 属性
    code = models.TextField(default=0)  # 属性
    def __getattr__(self, name):
        _ = name
        return self.value
    # upload_to 上传图片存放的路径
    # 如果这里你需要上传文件可以换成FileField
class ImgAreaName(models.Model):
    """本地图片分类"""
    area_id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=50, default=0, db_index=True)  # 属性名称

class SellGoods(models.Model):
    #NPC出售物品
    id = models.AutoField(primary_key=True)
    npc_id = models.CharField(max_length=10, db_index=True)
    item_id = models.CharField(max_length=10, db_index=True)


class ChongWu(models.Model):
    #宠物类
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=20, default=0)  # 品质区分
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ChongWu, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
class ChatAreaName(models.Model):
    """ 聊天分区 """
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(verbose_name=u'区分聊天块', max_length=20, db_index=True)


def game_set(self, key, value, is_save=False, name=u''):
    # if str(value).isdigit():
    #    value = int(value)
    # else:
    #    pass
    if key in self.__dict__:
        self.__setattr__(key, value)
        return
    try:
        self.json_params[key] = value
    except AttributeError:
        self.json_params = ujson.loads(str(self.params))
        if not self.json_params:
            self.params = u'{}'
            self.json_params = ujson.loads(self.params)
        self.json_params[key] = value
    if is_save:
        self.params = ujson.dumps(self.json_params)
        self.save()
    return self
    
def game_move(self, key, value, is_save=False, name=u''):
    # if str(value).isdigit():
    #    value = int(value)
    # else:
    #    pass
    #if key in self.__dict__:
    #    self.__setattr__(key, value)
    #    return
    if not self.json_params:
        json_paramss = ujson.loads(str(self.params))
    else:
        json_paramss = self.json_params
    try:
        if json_paramss.get(key) is None:
            pass
        else:
            del json_paramss[str(key)]
            self.json_params = json_paramss
    except AttributeError:
        pass
    if is_save:
        self.params = ujson.dumps(self.json_params)
        self.save()
    return self

def game_change(self, key, value, is_save=False, name=u''):
    # now_value = self.get(key, 0)
    if self.get(key) == '' or self.get(key) is None:
        now_value = 0
    elif str(self.get(key)).replace(".", '').isdigit():
        if str(self.get(key)).count(".") == 0:
            now_value = int(self.get(key))
        elif str(self.get(key)).count(".") == 1:
            now_value = float(self.get(key))
        else:
            now_value = self.get(key, 0)
    else:
        now_value = self.get(key, 0)
    if str(value).replace(".", '').isdigit():
        if str(value).count(".") == 0:
            value = int(value)
        elif str(value).count(".") == 1:
            value = float(value)
    # if self.get(key) == '' or self.get(key) is None:
    #    now_value = 0
    # elif str(self.get(key)).isdigit():
    #    now_value = int(self.get(key))
    # else:
    #    now_value = self.get(key, 0)
    t_value = now_value + value
    self.set(key, round(t_value,3))
    if is_save:
        self.params = ujson.dumps(self.json_params)
        self.save()
    return self


def game_get(self, key, default_value=0):
    if key in self.__dict__:
        val = self.__getattribute__(key)

    else:
        try:
            val = self.json_params.get(key, default_value)
        except AttributeError:
            self.json_params = ujson.loads(self.params)
            if not self.json_params:
                self.params = u'{}'
                self.json_params = ujson.loads(self.params)
            val = self.json_params.get(key, default_value)
    if str(val).replace(".", '').isdigit():
        if str(val).count(".") == 0:
            return int(val)
        elif str(val).count(".") == 1:
            val = float(val)
            return round(val, 3)
        else:
            return val
    else:
        return val
class GameObject:
    def __init__(self, obj):
        self.__dict__['obj'] = obj

    def __unicode__(self):
        return self.obj

    # def __getattribute__(self, item):
    # return self.obj.__getattribute__(item)
    def __repr__(self):
        return self.obj.__repr__()

    def __getattr__(self, item):
        if item == '__getattribute__':
            val = self.__getattribute__
        elif item == '__getattr__':
            val = self.__getattr__
        if item.startswith('__'):
            val = self.__getattribute__(item)
        if item in self.obj.__class__.__dict__:
            val = self.obj.__getattribute__(item)
        elif item in self.obj.__dict__:
            val = self.obj.__getattribute__(item)
        else:
            val = self.obj.get(item)
        # if item == u'name':
        # if not val or str(val) == u'0':
        # val = u'无名氏{}'.format(game_get(self.obj, 'id', 0))
        # t_val = str(game_get(self.obj, u'chenghao', u''))
        # if t_val and str(t_val) != u'0':
        # val = t_val + str(val)
        # if not val and item != 'message':
        #    return 0
        # elif str(val).isdigit():
        #    return int(val)
        # else:
        #     return val
        if not val and item != 'message':
            return 0
        elif str(val).replace(".", '').isdigit():
            if str(val).count(".") == 0:
                return int(val)
            elif str(val).count(".") == 1:
                val = float(val)
                return round(val,3)
            else:
                return val
        else:
            return val


class GameMap(models.Model):
    """地图类"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=200)
    map_upper = models.CharField(max_length=20, default=0)  # 上
    map_left = models.CharField(max_length=20, default=0)  # 左
    map_right = models.CharField(max_length=20, default=0)  # 右
    map_lower = models.CharField(max_length=20, default=0)  # 下
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(GameMap, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


class GameMapPlaceNpc(models.Model):
    """地图放置NPC"""
    id = models.AutoField(primary_key=True)
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属地图
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 放置NPC
    npc_code = models.TextField(default=0)  # NPC数量


class GameMapPlaceItem(models.Model):
    """地图放置物品"""
    id = models.AutoField(primary_key=True)
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属地图
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属NPC
    item_id = models.CharField(max_length=20, default=0, db_index=True)  # 放置物品
    item_code = models.TextField(default=0)  # 物品数量


class GameMapPlaceSkill(models.Model):
    """地图NPC技能"""
    id = models.AutoField(primary_key=True)
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属地图
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属NPC
    skill_id = models.CharField(max_length=20, default=0, db_index=True)  # 放置技能


class Parameter(models.Model):
    """参数加密"""
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Parameter, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'场景NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'场景NPC属性修改')


class GameNpc(models.Model):
    """NPC类"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=200)
    is_kill = models.CharField(max_length=20, default=0)
    exp_expression = models.TextField(default='')
    money_expression = models.TextField(default='')
    lingqi_expression = models.TextField(default='')
    is_drop = models.CharField(max_length=20, default=0)
    is_boss = models.CharField(max_length=20, default=0)  # 1为boss
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')
    refresh_time = models.CharField(max_length=50, default=0)  # BOSS刷新时间

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(GameNpc, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'NPC属性修改')
class Skill(models.Model):
    #系统技能
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    lvl = models.CharField(max_length=20)
    desc = models.TextField(default='')
    fight_message = models.CharField(max_length=200, default='')
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Skill, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'NPC属性修改')
class GameMapAreaName(models.Model):
    """地图区域"""
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=200, db_index=True)


class ItemAreaName(models.Model):
    """物品类别"""
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=200, db_index=True)


class ItemType(models.Model):
    """装备类型"""
    id = models.AutoField(primary_key=True)
    type = models.CharField(max_length=20)
    item_areaname = models.CharField(max_length=200, default=0)  # 装备归类
    position = models.IntegerField(default=0)


class Item(models.Model):
    """物品类"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=252)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=20)  # 物品分类
    type = models.CharField(max_length=20)  # 装备类型
    type_id = models.CharField(max_length=20, default=0)  # 装备类型
    duixiang = models.CharField(max_length=20, default=0)  # 装备类型
    pm_money = models.CharField(max_length=30, default=0, db_index=True)  # 出售飘渺币
    money = models.CharField(max_length=30, default=0, db_index=True)  # 出售金币
    bangpai_gongxian = models.CharField(max_length=30, default=0, db_index=True)  # 帮派贡献
    jifen_zb = models.CharField(max_length=30, default=0, db_index=True) 
    bangding = models.CharField(max_length=30, default=0)  # 是否绑定
    biaoshi = models.CharField(max_length=50, default=0)  # 标识
    time = models.CharField(max_length=20, default=0)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Item, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'物品属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'物品属性修改')
# 模板
class PageName(models.Model):
    """模板列表"""
    id = models.AutoField(primary_key=True)
    page_name = models.CharField(max_length=200)


# 操作
class Operation(models.Model):
    """操作数据表"""
    id = models.AutoField(primary_key=True)
    code = models.TextField(verbose_name=u'代码')
    content = models.TextField(verbose_name=u'内容', default='')
    display = models.TextField(verbose_name=u'显示', default='')
    page_name = models.CharField(max_length=200, default='None', db_index=True)
    map_id = models.CharField(max_length=20, default='None')
    npc_id = models.CharField(max_length=20, default='None')
    shibing_id = models.CharField(max_length=20, default='None')
    skill_id = models.CharField(max_length=20, default='None')
    item_id = models.CharField(max_length=20, default='None')
    chongwu_id = models.CharField(max_length=20, default='None')
    zuoqi_id = models.CharField(max_length=20, default='None')  # 事件坐骑
    position = models.IntegerField(default=0, db_index=True)  # 位置
    event = models.CharField(max_length=20, default='None', db_index=True)
    is_input = models.CharField(max_length=20, default=0)  # 1为操作框， 2为输入框， 3为css, 4为js

    class Meta:
        ordering = ['position']


class Expression(models.Model):
    """公共表达式"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, default=0)  # 属性名称
    attribute = models.CharField(max_length=200, default=0, db_index=True)  # 属性
    expression = models.TextField(verbose_name=u'表达式', default='')  # 表达式

    def __getattr__(self, attribute):
        _ = attribute
        return self.expression


class GetValue(object):
    """公共表达式"""

    def __getattr__(self, item):
        if Expression.objects.filter(attribute=item).count() == 0:
            return '0'
        else:
            return Expression.objects.filter(attribute=item).first().expression


class GameAttribute(models.Model):
    """游戏C属性"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, default=0, db_index=True)  # 属性名称
    value = models.TextField(default=0)  # 值

    def __getattr__(self, name):
        _ = name
        return self.value


class GameValue(object):
    """游戏C属性"""

    def __setattr__(self, key, value):
        if not GameAttribute.objects.filter(name=key):
            GameAttribute.objects.create(name=key, value=value)
        else:
            game_value = GameAttribute.objects.get(name=key)
            game_value.value = value
            game_value.save()

    def __getattr__(self, item):
        if not GameAttribute.objects.filter(name=item):
            return 0
        else:
            # val = GameAttribute.objects.filter(name=item).first().value
            val = GameAttribute.objects.get(name=item)
            val = val.value
            if val.isdigit() or val == 0:
                return int(val)
            else:
                return val
# 事件
class Event(models.Model):
    """事件数据表"""
    id = models.AutoField(primary_key=True)
    page_name = models.CharField(max_length=50, default='None')  # 事件模版
    name = models.CharField(max_length=50, default='None')  # 事件名
    operation = models.CharField(max_length=20, default='None', db_index=True)  # 整件操作
    map_id = models.CharField(max_length=20, default='None')  # 事件地图
    npc_id = models.CharField(max_length=20, default='None')  # 事件NPC
    chongwu_id = models.CharField(max_length=20, default='None')  # 事件宠物
    shibing_id = models.CharField(max_length=20, default='None')  # 事件兵种
    zuoqi_id = models.CharField(max_length=20, default='None')  # 事件坐骑
    item_id = models.CharField(max_length=20, default='None')  # 事件物品
    skill_id = models.CharField(max_length=20, default='None')  # 事件技能
    code = models.TextField(verbose_name=u'代码')  # 全局事件代码


# 步骤事件
class EventList(models.Model):
    """事件数据表"""
    id = models.AutoField(primary_key=True)
    event = models.CharField(max_length=20, default='None', db_index=True)  # 所在事件id
    event_all = models.CharField(max_length=20, default='None', db_index=True)  # 所在公共事件id
    display = models.TextField(verbose_name=u'触发条件', default='', null=True, blank=True)  # 触发条件
    execute_display = models.TextField(verbose_name=u'执行条件', default='', null=True, blank=True)  # 执行条件
    content = models.TextField(verbose_name=u'满足提示', default='', null=True, blank=True)  # 满足提示
    not_content = models.TextField(verbose_name=u'不满足提示', default='', null=True, blank=True)  # 不满足提示
    code = models.TextField(verbose_name=u'代码', default='', null=True, blank=True)  # 代码
    params = models.TextField(verbose_name=u'定义属性', default=u'{}')  # 定义属性
    position = models.IntegerField(default=0, db_index=True)  # 位置

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(EventList, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        if value == '0' or value == 0:
            return ''
        else:
            return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


# 公共事件
class EventAll(models.Model):
    """公共事件数据表"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, default=0, db_index=True)
    area_name = models.CharField(max_length=50, default=0, db_index=True)  # 区分人物，系统，物品，场景等事件
    code = models.TextField(verbose_name=u'代码')


# 定义属性
class Attribute(models.Model):
    """定义各项属性"""
    id = models.AutoField(primary_key=True)
    duixiang = models.CharField(max_length=20, db_index=True)  # 归属NPC,MAP,CHONGWU,ITEM
    built_in = models.CharField(max_length=20, default=0)  # 1为内置属性，不可删除, 2为需要在属性里面调值
    name = models.CharField(max_length=100)  # 属性名称
    attribute = models.CharField(max_length=100, db_index=True)  # 属性
    type = models.CharField(max_length=20)  # 类型 1= 整型， 2=布尔值， 3=文本表达式
    value = models.TextField(default=0)  # 值
    position = models.IntegerField(default=0)  # 位置


class Task(models.Model):
    """任务类"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=200, default=0)  # 任务分类区或
    type = models.CharField(max_length=20, default=0)  # 类型：办事/杀怪/寻物
    remove = models.CharField(max_length=20, default=0)  # 是否可放弃
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务NPC
    npc_name = models.CharField(max_length=20, default=0)  # 任务NPC
    display = models.TextField(verbose_name=u'触发条件', default='')  # 触发条件
    execute_display = models.TextField(verbose_name=u'执行条件', default='')  # 执行条件
    content = models.TextField(verbose_name=u'满足提示', default='')  # 满足提示
    not_content = models.TextField(verbose_name=u'不满足提示', default='')  # 不满足提示
    code = models.TextField(verbose_name=u'代码', default='')  # 代码
    submit_code = models.TextField(verbose_name=u'完成代码', default='')  # 完成任务代码
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Task, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'场景NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'场景NPC属性修改')


class TaskAreaName(models.Model):
    """任务类"""
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=200, db_index=True)


class TaskItem(models.Model):
    """任务对应物品或者怪物"""
    id = models.AutoField(primary_key=True)
    task_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务ID
    npc_name = models.CharField(max_length=20, default=0)  # 任务杀怪
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务杀怪
    npc_count = models.TextField(default=0)  # 任务杀怪数量
    item_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务物品
    item_name = models.CharField(max_length=20, default=0)  # 任务物品
    item_count = models.TextField(default=0)  # 任务物品数量

class Forum(models.Model):
    """论坛"""
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=20, default=0)  # 标题
    content = models.TextField(verbose_name=u'内容', default='')  # 内容
    user_name = models.CharField(max_length=200, default=0)  # 发表者帐号
    click = models.CharField(max_length=20, default=0)  # 点击率
    reply = models.CharField(max_length=20, default=0)  # 答复
    time = models.CharField(max_length=50, default=0)  # 时间
    date = models.CharField(max_length=50, default=0)  # 日期
    zhuangtai = models.CharField(max_length=20, default=0)  # 状态 1置顶 2精华 3推荐 4热度占用


class ForumReply(models.Model):
    """论坛回复"""
    id = models.AutoField(primary_key=True)
    title_id = models.CharField(max_length=20, default=0, db_index=True)  # 链接ID
    content = models.TextField(verbose_name=u'内容', default='')  # 内容
    user_name = models.CharField(max_length=50, default=0)  # 发表者帐号
    time = models.CharField(max_length=50, default=0)  # 时间
    date = models.CharField(max_length=50, default=0)  # 日期


class GameImgAreaName(models.Model):
    """游戏图片分类"""
    area_id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=50, default=0, db_index=True)  # 属性名称


# 游戏图片
class GameImg(models.Model):
    """游戏图片"""
    id = models.AutoField(primary_key=True)
    area_name = models.CharField(max_length=50, default=0)  # 属性名称
    name = models.CharField(max_length=50, default=0)  # 名称
    value = models.TextField(default=0)  # 值
    attribute = models.CharField(max_length=50, default=0, db_index=True)  # 属性
    code = models.TextField(default=0)  # 属性

    def __getattr__(self, name):
        _ = name
        return self.value


class ImgValue(object):
    """游戏图片"""
    def __getattr__(self, item):
        if Img.objects.filter(attribute=item).count() > 0:
            val = Img.objects.filter(attribute=item).first().value
            code = Img.objects.filter(attribute=item).first().code
            val = '<img {} src="{}">'.format(code, val)
            return val
        elif GameImg.objects.filter(attribute=item).count() > 0:
            val = GameImg.objects.filter(attribute=item).first().value
            code = GameImg.objects.filter(attribute=item).first().code
            # if val.isdigit() or val == 0:
            #    return int(val)
            # else:
            # val = f'''img src="{val}"/'''
            val = '<img {} src="{}">'.format(code, val)
            return val
        else:
            return 0
    def get(self, key):
        return type(self).__getattr__(self, key)
"""
    def set(self, key, value=None):
        if value:
            return type(self).__setattr__(self, key, value)
        return type(self).__getattr__(self, key)

    def __setattr__(self, key, value):
        if not GameImg.objects.filter(attribute=key):
            GameImg.objects.create(attribute=key, value=value)
        else:
            game_value = GameImg.objects.get(attribute=key)
            game_value.value = value
            game_value.save()
"""

# 交易行分类
class AuctionAreaName(models.Model):
    """交易行分类"""
    id = models.AutoField(primary_key=True)
    auction_name = models.CharField(max_length=50, default=0)  # 交易行名
    money_name = models.CharField(max_length=50, default=0)  # 幣種名稱
    money_sx = models.CharField(max_length=50, default='')  # 幣種屬性
    service_charge = models.CharField(max_length=50, default=0)  # 成交手續%
    money_minute = models.CharField(max_length=50, default=0)  # 上架收費/分
    max_minute = models.CharField(max_length=50, default=0)  # 最大上架時長/分


# 排行榜分类
class RankingAreaName(models.Model):
    """排行榜分类"""
    id = models.AutoField(primary_key=True)
    ranking_name = models.CharField(max_length=50, default=0, db_index=True)  # 排行榜名
    ranking_sx = models.CharField(max_length=50, default=0, db_index=True)  # 排行榜属性
    ranking_expression = models.TextField(default=0)  # 排行榜表达式
    ranking_count = models.CharField(max_length=50, default=0)  # 排行榜显示数量
    ranking_object = models.CharField(max_length=50, default=0)  # 排行榜对象
# 系统坐骑
class ZuoQi(models.Model):
    """坐骑类"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=20, default=0, db_index=True)  # 品质区分
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ZuoQi, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')

# 士兵
class ShiBing(models.Model):
    """系统士兵"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    area_name = models.CharField(max_length=20)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ShiBing, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
#############################################################################################
#############################################################################################


class User(models.Model):
    #用户表，可删
    objects = None
    gender = (
        ('m', '男'),
        ('w', '女'),
    )
    name = models.CharField(max_length=20, db_index=True)
    sid = models.CharField(max_length=200, default='')#加密SID
    come  = models.CharField(max_length=200, default='') # 来源
    ip = models.CharField(max_length=20) # 注册IP检测
    password = models.CharField(max_length=200)
    email = models.EmailField(max_length=20, default='0')
    sex = models.CharField(max_length=32, choices=gender, default='男')
    number = models.CharField(max_length=32, default='0')
    security = models.CharField(max_length=32, default='0')
    c_time = models.DateTimeField(auto_now_add=True)
    is_designer = models.CharField(verbose_name=u'是否设计', max_length=20, default=False)  # 是否设计
    fuwuqi_xianlu = models.CharField(max_length=5, default=0, db_index=True) # 服务器线路
    forum_designer = models.CharField(verbose_name=u'是否设计', max_length=20, default=False)  # 是否版主
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(User, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')


class GameAreaName(models.Model):
    #游戏分区 可删
    area_id = models.AutoField(primary_key=True)
    area_name = models.CharField(verbose_name=u'游戏分区名', max_length=200, default=0)
    start_time = models.CharField(verbose_name=u'开区时间', max_length=200, default=0)
    create_time = models.CharField(verbose_name=u'创建时间', max_length=200, default=0)
    is_neice = models.CharField(verbose_name=u'是否内测', max_length=200, default=False)



class ChatMessage(models.Model):
    #聊天信息 可删
    id = models.AutoField(primary_key=True)
    all_area_name = models.CharField(verbose_name=u'区分全部', max_length=20, default=0, db_index=True)
    area_name = models.CharField(verbose_name=u'区分聊天块', max_length=20, db_index=True)
    area_id = models.IntegerField(default=0,verbose_name=u'区分聊天块',  db_index=True)
    sender_player_id = models.IntegerField(default=0,verbose_name=u'发送人ID',  db_index=True)
    sender_player_name = models.CharField(verbose_name=u'发送人名字', max_length=200)
    message = models.CharField(verbose_name=u'信息内容', max_length=200)
    player_id = models.IntegerField(default=0,verbose_name=u'信息接收玩家',db_index=True)
    bangpai_id = models.IntegerField(default=0, verbose_name=u'信息接收玩家', db_index=True)
    page_name = models.CharField(default=0, verbose_name=u'信息接收玩家', max_length=20, db_index=True)
    team_id = models.IntegerField(default=0, verbose_name=u'信息接收玩家',  db_index=True)
    zhuangtai = models.IntegerField(default=0,  db_index=True) #0为未读


class Player(models.Model):
# 玩家类 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(verbose_name=u'用户名', max_length=200)
    user_id = models.CharField(verbose_name=u'归属用户', default=0, max_length=200)
    area_id = models.CharField(verbose_name=u'归属分区', default=0, max_length=20, db_index=True)
    map_id = models.CharField(verbose_name=u'地图ID', default=0, max_length=20, db_index=True)
    team_id = models.CharField(verbose_name=u'组队ID', max_length=200, default=0)
    fuwuqi_xianlu = models.CharField(max_length=5, default=0, db_index=True) # 服务器线路
    message = models.TextField(default='', verbose_name=u'消息')
    time = models.CharField(default=0, max_length=30, verbose_name=u'最后一次动作')
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')
    is_designer = models.CharField(verbose_name=u'是否设计', max_length=200, default=False)
    is_neice = models.CharField(verbose_name=u'是否内测', max_length=200, default=False)
    ip = models.CharField(verbose_name=u'归属IP', default=0, max_length=20, db_index=True)

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Player, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)

        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')
    
    def move(self, key, value, is_save=False):
        game_move(self, key, value, is_save, u'玩家属性移除')


class AttackInfo(models.Model):
    #被攻击信息 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, db_index=True)  # 被攻击者ID
    attack_player_id = models.CharField(max_length=20)  # 攻击者ID
    time = models.CharField(default=0, max_length=50)  # 被攻击时间


class Team(models.Model):
    #组队类 可删

    class Meta:
        verbose_name = u'组队信息'
        verbose_name_plural = u'组队信息'

    id = models.AutoField(primary_key=True)
    team_name = models.CharField(verbose_name=u'组队名称', max_length=200)
    player_id = models.CharField(verbose_name=u'玩家ID', max_length=20, db_index=True)
    team_id = models.CharField(verbose_name=u'组队ID', max_length=20, default=0, db_index=True)


class InTeam(models.Model):
    #申请组队类 可删

    class Meta:
        verbose_name = u'申请组队信息'
        verbose_name_plural = u'申请组队信息'

    id = models.AutoField(primary_key=True)
    player_id = models.CharField(verbose_name=u'玩家ID', max_length=20, db_index=True)
    team_id = models.CharField(verbose_name=u'组队ID', max_length=20, default=0, db_index=True)



class GameNpcs(models.Model):
    #NPC类，属地图，可删
    id = models.AutoField(primary_key=True)
    ys_id = models.CharField(max_length=20, default=0)
    name = models.CharField(max_length=200, default='')
    desc = models.CharField(max_length=200, default='')
    area_name = models.CharField(max_length=200, default=0)
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 所属分区
    map_id = models.CharField(max_length=20, default=0, db_index=True)
    is_kill = models.CharField(max_length=20, default=0, db_index=True)
    update = models.CharField(max_length=20, default=0, db_index=True)  # 怪物当前是否更新 1为当前已刷新
    attack_npc = models.CharField(max_length=20, default=0, db_index=True)  # 1为攻击点创建怪物
    is_boss = models.CharField(max_length=20, default=0, db_index=True)  # 1为boss
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(GameNpcs, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'场景NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'场景NPC属性修改')



class Pets(models.Model):
    #宠物类玩家类 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    zc_id = models.CharField(max_length=20, default=0, db_index=True)
    chongwu_id = models.CharField(max_length=20, default=0, db_index=True)
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 0为人物，1为怪物
    area_name = models.CharField(max_length=20, default=0)  # 品质区分
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Pets, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


class PetsOut(models.Model):
    #宠物出战 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    pets_id = models.CharField(max_length=20, default=0)
    position = models.IntegerField(default=0, db_index=True)  # 位置


class QuickSkill(models.Model):
    #技能快捷键 可删
    id = models.AutoField(primary_key=True)
    quick = models.CharField(max_length=20)
    name = models.CharField(max_length=30, default=0)
    skill_id = models.CharField(max_length=20, db_index=True)
    item_id = models.CharField(max_length=20, db_index=True)
    player_id = models.CharField(max_length=20, db_index=True)




class Skills(models.Model):
    #玩家身上技能 可删
    id = models.AutoField(primary_key=True)
    lvl = models.CharField(max_length=20)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    pets_id = models.CharField(max_length=20, default=0, db_index=True)
    mount_id = models.CharField(max_length=20, default=0, db_index=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    skill_id = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Skills, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'场景NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'场景NPC属性修改')



class UseEquip(models.Model):
    #玩家装备中物品 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定玩家ID
    pets_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定宠物ID
    type_id = models.CharField(max_length=20, default=0, db_index=True)  # 装备类型
    tz_biaoshi = models.CharField(max_length=20, default=0, db_index=True)  # 装备标识
    item_id = models.CharField(max_length=20, default=0)
    time = models.CharField(max_length=20, default=0)
    ys_item_id = models.CharField(max_length=20, default=0)
    soldier_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定士兵ID
    mount_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定坐骑ID


# 玩家物品类
class ItemPlayer(models.Model):
    #玩家物品类 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=250)
    desc = models.TextField(default='')
    item_id = models.IntegerField(default=0)  # 系统内ID
    area_name = models.CharField(max_length=200, default=0)  # 物品分类
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 所属分区
    type = models.CharField(max_length=20, default='')  # 装备类型
    type_id = models.CharField(max_length=20, default=0)  # 装备类型ID
    bangding = models.CharField(max_length=20, default=0)  # 是否绑定
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属帮派
    suoding = models.CharField(max_length=20, default=0)  # 是否锁定
    duixiang = models.CharField(max_length=20, default=0)  # 使用对象
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 地图ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定玩家ID
    zb_player_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定玩家ID
    pets_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定宠物ID
    count = models.CharField(max_length=30, default=0)  # 数量/整型
    update = models.CharField(max_length=20, default=0)  # 怪物当前是否更新 1为当前地图物品已刷新
    biaoshi = models.CharField(max_length=50, default=0)  # 标识
    time = models.CharField(max_length=20, default=0)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ItemPlayer, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')


# 地图物品类
class ItemMap(models.Model):
    #地图物品类 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=252)
    desc = models.TextField(default='')
    item_id = models.IntegerField(default=0)  # 系统内ID
    area_name = models.CharField(max_length=200, default=0)  # 物品分类
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 所属分区
    type = models.CharField(max_length=20, default='')  # 装备类型
    type_id = models.CharField(max_length=20, default=0)  # 装备类型ID
    bangding = models.CharField(max_length=20, default=0)  # 是否绑定
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 地图ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定玩家ID
    count = models.CharField(max_length=200, default=0)  # 数量/整型
    update = models.CharField(max_length=20, default=0)  # 怪物当前是否更新 1为当前地图物品已刷新
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ItemMap, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')




class PublicAttribute(models.Model):
    #公共属性 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, default=0, db_index=True)  # 属性
    value = models.TextField(default=0)  # 值
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 分区




class TaskPlayer(models.Model):
    #任务对应物品或者怪物 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 玩家ID
    task_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务ID
    zhuangtai = models.CharField(max_length=20, default=0)  # 状态，0未接，1已接，2完成


class TaskItemPlayer(models.Model):
    #任务对应物品或者怪物 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 玩家ID
    task_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务ID
    npc_name = models.CharField(max_length=20, default=0)  # 任务杀怪
    npc_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务杀怪
    npc_count = models.TextField(default=0)  # 任务杀怪数量
    item_id = models.CharField(max_length=20, default=0, db_index=True)  # 任务物品
    item_name = models.CharField(max_length=20, default=0)  # 任务物品
    item_count = models.TextField(default=0)  # 任务物品数量




# 交易行物品
class Auction(models.Model):
    #交易行物品 可删
    id = models.AutoField(primary_key=True)
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 分区
    item_name = models.CharField(max_length=252, default=0)  # 物品名
    item_id = models.CharField(max_length=20, default='', db_index=True)  # 物品ID
    item_count = models.CharField(max_length=20, default=0)  # 物品數量
    item_type = models.CharField(max_length=20, default=0)  # 物品类别
    area_name = models.CharField(max_length=20, default=0)  # 物品分類
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 歸屬玩家
    time = models.CharField(max_length=50, default=0)  # 交易時長
    type = models.CharField(max_length=20, default=0)  # 類型
    auction_name = models.CharField(max_length=50, default=0, db_index=True)  # 交易行名
    money_name = models.CharField(max_length=50, default=0)  # 幣種名稱
    money_sx = models.CharField(max_length=50, default=0)  # 幣種屬性
    new_money = models.CharField(max_length=30, default=0)  # 當前價格
    min_money = models.CharField(max_length=30, default=0)  # 最低加價
    new_player_id = models.CharField(max_length=30, default=0)  # 購買玩家




# 排行榜
class Ranking(models.Model):
    #排行榜分类 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 排行榜玩家ID
    ranking_id = models.CharField(max_length=20, default=0)  # 排行榜ID
    chongwu_id = models.CharField(max_length=20, default=0)  # 排行榜玩家ID
    value = models.CharField(max_length=50, default=0)  # 排行榜值
    time = models.IntegerField(default=0)  # 排行榜值
    ranking_sx = models.CharField(max_length=50, default=0, db_index=True)  # 排行榜属性
    ranking_object = models.CharField(max_length=50, default=0, db_index=True)  # 排行榜对象
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 排行榜区分分区



# 玩家坐骑
class Mount(models.Model):
    #坐骑玩家类 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    zuoqi_id = models.CharField(max_length=20, default=0, db_index=True)
    area_name = models.CharField(max_length=20, default=0, db_index=True)  # 品质区分
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Mount, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


# 充值卡
class Card(models.Model):
    #充值卡 可删
    id = models.AutoField(primary_key=True)
    card = models.CharField(max_length=200)  # 卡号
    money = models.CharField(max_length=200)  # 金额
    player_id = models.CharField(max_length=20, default=0)  # 使用者
    time = models.CharField(max_length=50)  # 使用日期
    is_settlement = models.CharField(max_length=10, default=0)  # 是否结算
    settlement_tiem = models.CharField(max_length=10, default=0)  # 结算日期
    zhuangtai = models.CharField(max_length=20, default=0)  # 状态


# 帮派
class Gangs(models.Model):
    #帮派 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)  # 名字
    desc = models.TextField(default='')  # 描述
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 分区
    money = models.CharField(max_length=200, default=0)  # 资金
    max_count = models.CharField(max_length=20, default=0)  # 人数上限
    lvl = models.CharField(max_length=20, default=0)  # 等级
    max_exp = models.CharField(max_length=20, default=0)  # 经验上限
    exp = models.CharField(max_length=20, default=0)  # 经验
    fubangzhu_id = models.CharField(max_length=20, default=0)  # 副帮主ID
    bangzhu_id = models.CharField(max_length=20, default=0)  # 帮主ID
    generation = models.CharField(max_length=20, default=0)  # 帮主第几代
    create_player_id = models.CharField(max_length=20, default=0)  # 创始人ID
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Gangs, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


# 帮派堂口
class GangsTangkou(models.Model):
    #帮派堂口 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)  # 名字
    desc = models.TextField(default='')  # 描述
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    tangzhu_id = models.CharField(max_length=20, default=0)  # 堂主ID
    futangzhu_id = models.CharField(max_length=20, default=0)  # 副堂主ID


# 帮派成员
class GangsMember(models.Model):
    #帮派成员 可删
    id = models.AutoField(primary_key=True)
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    tangkou_id = models.CharField(max_length=20, default=0, db_index=True)  # 堂口ID
    member_id = models.CharField(max_length=20, default=0, db_index=True)  # 成员ID


# 申请加入帮派
class InGangs(models.Model):
    #申请加入帮派 可删
    id = models.AutoField(primary_key=True)
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    tangkou_id = models.CharField(max_length=20, default=0)  # 堂口ID
    player_id = models.CharField(max_length=20, default=0)  # 申请者ID


class Monster(models.Model):
    #怪物搭载 可删
    id = models.AutoField(primary_key=True)
    ys_id = models.CharField(max_length=20, default=0)  # 归属系统NPC  ID
    npc_id = models.CharField(max_length=20, default=0)  # 归属地图NPC  ID
    name = models.CharField(max_length=200, default='')
    desc = models.CharField(max_length=200, default='')
    player_id = models.CharField(max_length=20, default=0)  # 归属玩家
    is_drop = models.CharField(max_length=20, default=0)
    exp_expression = models.TextField(default='')
    money_expression = models.TextField(default='')
    lingqi_expression = models.TextField(default='')
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')
    zhuangtai = models.CharField(max_length=20, default=0)  # 是否参战

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Monster, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'场景NPC属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'场景NPC属性修改')


# 玩家仓库类
class CangKuPlayer(models.Model):
    #玩家仓库 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    item_id = models.IntegerField(default=0)  # 系统内ID
    area_name = models.CharField(max_length=20, default=0)  # 物品分类
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 所属分区
    type = models.CharField(max_length=20, default='')  # 装备类型
    type_id = models.CharField(max_length=20, default=0)  # 装备类型ID
    bangding = models.CharField(max_length=20, default=0)  # 是否绑定
    suoding = models.CharField(max_length=20, default=0)  # 是否锁定
    map_id = models.CharField(max_length=20, default=0, db_index=True)  # 地图ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定玩家ID
    pets_id = models.CharField(max_length=20, default=0, db_index=True)  # 绑定宠物ID
    count = models.CharField(max_length=20, default=0)  # 数量/整型
    update = models.CharField(max_length=20, default=0)  # 怪物当前是否更新 1为当前地图物品已刷新
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')
    duixiang = models.CharField(max_length=20, default=0)  # 使用对象
    biaoshi = models.CharField(max_length=50, default=0)  # 标识
    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(CangKuPlayer, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')



# 玩家士兵
class Soldier(models.Model):
    #玩家士兵 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    desc = models.TextField(default='')
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    shibing_id = models.CharField(max_length=20, default=0, db_index=True)
    pets_id = models.CharField(max_length=20, default=0, db_index=True)
    update = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Soldier, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')

# 士兵散部
class SanBu(models.Model):
    #士兵散部 可删
    id = models.AutoField(primary_key=True)
    count = models.CharField(max_length=20, default=0)
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    shibing_id = models.CharField(max_length=20, default=0, db_index=True)

# 战场
class Battle(models.Model):
    #战场类 可删
    id = models.AutoField(primary_key=True)  # 战场ID
    name = models.CharField(max_length=60, default=0)  # 战场名字
    time = models.CharField(max_length=40)  # 用于关闭战场
    gj_time = models.CharField(max_length=40)  # 用于超时关闭战场
    area_id = models.CharField(max_length=5, default=0, db_index=True)  # 战场所属分区
    map_id = models.CharField(max_length=10, default=0, db_index=True)
    npc_id = models.CharField(max_length=10, default=0, db_index=True)
    bangpai_id = models.CharField(max_length=10, default=0, db_index=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    zhuangtai = models.CharField(max_length=20, default=0, db_index=True)  # 战场状态0为关闭
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Battle, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


# 属性
class PlayerAttribute(models.Model):
    #玩家属性搭载 可删
    id = models.AutoField(primary_key=True)  # 战场ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(PlayerAttribute, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')




class ZhuangYuan(models.Model):
    # 庄院可删
    id = models.AutoField(primary_key=True)  # 战场ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    area_name = models.CharField(max_length=40, default=0, db_index=True)
    lvl = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(ZhuangYuan, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')


class OpenGame(models.Model):
    # 游戏平台展示 可删
    id = models.AutoField(primary_key=True)
    game_area_name = models.CharField(max_length=40, default=0, db_index=True)  # 游戏区分，公测，运营，内测，开发中
    game_name = models.CharField(max_length=40, default=0)
    game_desc = models.CharField(max_length=252, default=0)
    game_id = models.CharField(max_length=40, default=0)
    game_http = models.CharField(max_length=252, default=0)  # 网址
    img_0_code = models.CharField(max_length=252, default=0)
    img_1_code = models.CharField(max_length=252, default=0)
    img_2_code = models.CharField(max_length=252, default=0)
    img_3_code = models.CharField(max_length=252, default=0)
    img_4_code = models.CharField(max_length=252, default=0)
    img_5_code = models.CharField(max_length=252, default=0)
    img_6_code = models.CharField(max_length=252, default=0)
    img_7_code = models.CharField(max_length=252, default=0)
    img_8_code = models.CharField(max_length=252, default=0)
    img_9_code = models.CharField(max_length=252, default=0)
    img_10_code = models.CharField(max_length=252, default=0)

class UtPlayer(models.Model):
# 玩家临时属性 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(UtPlayer, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)

        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')
        
class UhPlayer(models.Model):
# 玩家临时属性 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(UhPlayer, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)

        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'玩家属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'玩家属性修改')
        
class Friends(models.Model):
# 我的好友 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, db_index=True)
    friends_id = models.CharField(max_length=20, db_index=True)
    #blacklist_id = models.CharField(max_length=20, db_index=True)
    zhuangtai = models.IntegerField(default=0,db_index=True)

class Enemy(models.Model):
# 我的仇人 可删
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, db_index=True)
    enemy_id = models.CharField(max_length=20, db_index=True)
    count = models.CharField(max_length=20, db_index=True) # 被攻击次数
    
class Statue(models.Model):
    # 雕像
    id = models.AutoField(primary_key=True)  
    area_name = models.CharField(max_length=10, default=0, db_index=True)
    map_id = models.CharField(max_length=10, default=0, db_index=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    area_id = models.CharField(max_length=5, default=1, db_index=True)  # 战场所属分区
    dipi_player_id = models.CharField(max_length=10, default=0, db_index=True)
    dipi_end_time = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Statue, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
        
    
class BoothPlayer(models.Model):
    # 摊位区分
    id = models.AutoField(primary_key=True)  
    player_id = models.IntegerField(default=0) # 玩家ID
    area_id = models.CharField(max_length=5, default=1, db_index=True)  # 战场所属分区
    map_id = models.IntegerField(default=0) # 摆摊地图
    booth_name = models.CharField(max_length=20, default=0, db_index=True)  # 交易行名
    money_name = models.CharField(max_length=20, default=0)  # 幣種名稱
    money_sx = models.CharField(max_length=20, default=0)  # 幣種屬性
    time = models.IntegerField(default=0) # 到期时间
class Booth(models.Model):
    # 摊位物品列表
    id = models.AutoField(primary_key=True)
    player_id = models.IntegerField(default=0) # 玩家ID
    booth_name = models.CharField(max_length=20, default=0, db_index=True)  # 交易行名
    money_name = models.CharField(max_length=20, default=0)  # 幣種名稱
    money_sx = models.CharField(max_length=20, default=0)  # 幣種屬性
    item_money = models.CharField(max_length=50, default=0) # 单价
    item_count = models.CharField(max_length=50, default=0) # 数量
    item_area_name = models.CharField(max_length=20, default=0, db_index=True) # 类别
    player_item_id = models.IntegerField(default=0) # 物品id
    item_id = models.IntegerField(default=0) # 原始物品id

class House(models.Model):
    # 房屋可删
    id = models.AutoField(primary_key=True)  # 房屋ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)
    lvl = models.CharField(max_length=20, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(House, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
    
class HouseList(models.Model):
    # 房屋家具列表
    id = models.AutoField(primary_key=True)  # 房屋ID
    house_id = models.CharField(max_length=10, default=0, db_index=True) # 房屋ID
    area_name = models.CharField(max_length=10, default=0, db_index=True) # 区分
    lvl = models.CharField(max_length=10, default=0, db_index=True) # 房屋等级
    shuxing = models.CharField(max_length=20, default=0, db_index=True) # 区分
    shuxing_value = models.IntegerField(default=0, db_index=True) # 区分
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(HouseList, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')

class Suo(models.Model):
    """锁"""
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    update = models.CharField(max_length=10, default=1, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        # cache.set("GameAttribute_cache_{}".format(self.id), self)
        super(Suo, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
class RegisterNumber(models.Model):
    """注册码"""
    id = models.AutoField(primary_key=True)
    number = models.CharField(max_length=10, default=0, db_index=True)
    zhuangtai = models.CharField(max_length=10, default=0, db_index=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    player_name = models.CharField(max_length=30, default=0, db_index=True)
# 帮派
class BangHui(models.Model):
    #帮派 可删
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)  # 名字
    desc = models.TextField(default='')  # 描述
    lvl = models.IntegerField(default=0)  # 名字
    area_id = models.CharField(max_length=20, default=0, db_index=True)  # 分区
    gongxian = models.IntegerField(default=0)  # 贡献
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    def __str__(self):
        return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(BangHui, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
# 帮派成员
class BangHuiNumber(models.Model):
    #帮派成员 可删
    id = models.AutoField(primary_key=True)
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 成员ID


# 申请加入帮派
class InBangHui(models.Model):
    #申请加入帮派 可删
    id = models.AutoField(primary_key=True)
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    tangkou_id = models.CharField(max_length=20, default=0)  # 堂口ID
    player_id = models.CharField(max_length=20, default=0)  # 申请者ID
class CdkNumber(models.Model):
    """CDK码"""
    id = models.AutoField(primary_key=True)
    number = models.CharField(max_length=10, default=0, db_index=True)
    zhuangtai = models.CharField(max_length=10, default=0, db_index=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    player_name = models.CharField(max_length=30, default=0, db_index=True)
    
class Transaction(models.Model):
    """交易"""
    id = models.AutoField(primary_key=True)
    player_name = models.CharField(max_length=30, default=0, db_index=True)
    player_id = models.CharField(max_length=10, default=0, db_index=True)
    item_name = models.CharField(max_length=90, default=0, db_index=True)
    pets_name = models.CharField(max_length=90, default=0, db_index=True)
    item_id = models.CharField(max_length=30, default=0, db_index=True)
    pets_id = models.CharField(max_length=30, default=0, db_index=True)
    item_count = models.CharField(max_length=30, default=0, db_index=True)
    ys_item_id = models.CharField(max_length=30, default=0, db_index=True)
    money = models.CharField(max_length=30, default=0, db_index=True)
    area_id = models.CharField(max_length=5, default=1, db_index=True)
    money_name = models.CharField(max_length=30, default=0, db_index=True)
    money_sx = models.CharField(max_length=30, default=0, db_index=True)
    money_bfb = models.CharField(max_length=30, default=0, db_index=True)
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')

    #def __str__(self):
     #   return self.name

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Transaction, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
# BOSS伤害统计
class BossRanking(models.Model):
    """BOSS伤害统计"""
    id = models.AutoField(primary_key=True)
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 排行榜名
    player_name = models.CharField(max_length=50, default=0)  # 排行榜名
    area_id = models.CharField(max_length=10, default=0, db_index=True)  # 排行榜名
    npc_id = models.CharField(max_length=10, default=0, db_index=True)  # 排行榜属性
    value = models.CharField(max_length=50, default=0)  # 排行榜显示数量
    zhuangtai = models.CharField(max_length=50, default=0)  # 排行榜显示数量
# 帮派物品审批
class BangHuiItem(models.Model):
    #帮派成员 可删
    id = models.AutoField(primary_key=True)
    bangpai_id = models.CharField(max_length=20, default=0, db_index=True)  # 帮派ID
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 成员ID
    itemplayer_id = models.CharField(max_length=20, default=0, db_index=True)  # 物品ID
    
#种植
class Plant(models.Model):
    # 种植
    id = models.AutoField(primary_key=True)  
    player_id = models.CharField(max_length=20, default=0, db_index=True)  # 归属玩家
    name = models.CharField(max_length=20, default=0)  # 物品名
    item_id = models.CharField(max_length=20, default=0)  # 物品ID
    area_id = models.CharField(max_length=20, default=0)  # 物品ID
    params = models.TextField(default=u'{}', verbose_name=u'属性(不懂不要修改)')
    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None, is_celery=False):
        try:
            self.params = ujson.dumps(self.json_params)
            if update_fields:
                if len(update_fields) > 0:
                    update_fields.append('params')
        except AttributeError:
            pass
        super(Plant, self).save(force_insert, force_update, using, update_fields)

    def get(self, key, default_value=0):
        value = game_get(self, key, default_value)
        return value

    def set(self, key, value, is_save=False):
        game_set(self, key, value, is_save, u'地图属性设置')

    def change(self, key, value, is_save=False):
        game_change(self, key, value, is_save, u'地图属性修改')
        
    