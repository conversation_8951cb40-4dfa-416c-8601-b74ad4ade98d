# Generated by Django 4.2.9 on 2025-02-26 13:02

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('web', '0021_statue_dipi_end_time'),
    ]

    operations = [
        migrations.DeleteModel(
            name='AttackInfo',
        ),
        migrations.DeleteModel(
            name='Auction',
        ),
        migrations.DeleteModel(
            name='<PERSON>Hui',
        ),
        migrations.DeleteModel(
            name='BangHuiItem',
        ),
        migrations.DeleteModel(
            name='BangHuiNumber',
        ),
        migrations.DeleteModel(
            name='Battle',
        ),
        migrations.DeleteModel(
            name='Booth',
        ),
        migrations.DeleteModel(
            name='BoothPlayer',
        ),
        migrations.DeleteModel(
            name='BossRanking',
        ),
        migrations.DeleteModel(
            name='CangKuPlayer',
        ),
        migrations.DeleteModel(
            name='Card',
        ),
        migrations.DeleteModel(
            name='CdkNumber',
        ),
        migrations.DeleteModel(
            name='ChatMessage',
        ),
        migrations.DeleteModel(
            name='Enemy',
        ),
        migrations.DeleteModel(
            name='Friends',
        ),
        migrations.DeleteModel(
            name='GameAreaName',
        ),
        migrations.DeleteModel(
            name='GameNpcs',
        ),
        migrations.DeleteModel(
            name='Gangs',
        ),
        migrations.DeleteModel(
            name='GangsMember',
        ),
        migrations.DeleteModel(
            name='GangsTangkou',
        ),
        migrations.DeleteModel(
            name='House',
        ),
        migrations.DeleteModel(
            name='HouseList',
        ),
        migrations.DeleteModel(
            name='InBangHui',
        ),
        migrations.DeleteModel(
            name='InGangs',
        ),
        migrations.DeleteModel(
            name='InTeam',
        ),
        migrations.DeleteModel(
            name='ItemMap',
        ),
        migrations.DeleteModel(
            name='ItemPlayer',
        ),
        migrations.DeleteModel(
            name='Monster',
        ),
        migrations.DeleteModel(
            name='Mount',
        ),
        migrations.DeleteModel(
            name='OpenGame',
        ),
        migrations.DeleteModel(
            name='Pets',
        ),
        migrations.DeleteModel(
            name='PetsOut',
        ),
        migrations.DeleteModel(
            name='Plant',
        ),
        migrations.DeleteModel(
            name='Player',
        ),
        migrations.DeleteModel(
            name='PlayerAttribute',
        ),
        migrations.DeleteModel(
            name='PublicAttribute',
        ),
        migrations.DeleteModel(
            name='QuickSkill',
        ),
        migrations.DeleteModel(
            name='Ranking',
        ),
        migrations.DeleteModel(
            name='RegisterNumber',
        ),
        migrations.DeleteModel(
            name='SanBu',
        ),
        migrations.DeleteModel(
            name='Skills',
        ),
        migrations.DeleteModel(
            name='Soldier',
        ),
        migrations.DeleteModel(
            name='Statue',
        ),
        migrations.DeleteModel(
            name='Suo',
        ),
        migrations.DeleteModel(
            name='TaskItemPlayer',
        ),
        migrations.DeleteModel(
            name='TaskPlayer',
        ),
        migrations.DeleteModel(
            name='Team',
        ),
        migrations.DeleteModel(
            name='Transaction',
        ),
        migrations.DeleteModel(
            name='UhPlayer',
        ),
        migrations.DeleteModel(
            name='UseEquip',
        ),
        migrations.DeleteModel(
            name='User',
        ),
        migrations.DeleteModel(
            name='UtPlayer',
        ),
        migrations.DeleteModel(
            name='ZhuangYuan',
        ),
    ]
