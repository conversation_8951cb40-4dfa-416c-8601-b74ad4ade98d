# Generated by Django 4.2.7 on 2024-01-11 12:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AttackInfo',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
                ('attack_player_id', models.CharField(max_length=20)),
                ('time', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Attribute',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('duixiang', models.CharField(db_index=True, max_length=20)),
                ('built_in', models.CharField(default=0, max_length=20)),
                ('name', models.Char<PERSON><PERSON>(max_length=100)),
                ('attribute', models.<PERSON><PERSON><PERSON><PERSON>(db_index=True, max_length=100)),
                ('type', models.Char<PERSON>ield(max_length=20)),
                ('value', models.TextField(default=0)),
                ('position', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Auction',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_name', models.CharField(default=0, max_length=252)),
                ('item_id', models.CharField(db_index=True, default='', max_length=20)),
                ('item_count', models.CharField(default=0, max_length=20)),
                ('item_type', models.CharField(default=0, max_length=20)),
                ('area_name', models.CharField(default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('time', models.CharField(default=0, max_length=50)),
                ('type', models.CharField(default=0, max_length=20)),
                ('auction_name', models.CharField(db_index=True, default=0, max_length=50)),
                ('money_name', models.CharField(default=0, max_length=50)),
                ('money_sx', models.CharField(default=0, max_length=50)),
                ('new_money', models.CharField(default=0, max_length=30)),
                ('min_money', models.CharField(default=0, max_length=30)),
                ('new_player_id', models.CharField(default=0, max_length=30)),
            ],
        ),
        migrations.CreateModel(
            name='AuctionAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('auction_name', models.CharField(default=0, max_length=50)),
                ('money_name', models.CharField(default=0, max_length=50)),
                ('money_sx', models.CharField(default='', max_length=50)),
                ('service_charge', models.CharField(default=0, max_length=50)),
                ('money_minute', models.CharField(default=0, max_length=50)),
                ('max_minute', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='BangHui',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('lvl', models.IntegerField(default=0)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('gongxian', models.IntegerField(default=0)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='BangHuiItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('itemplayer_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='BangHuiNumber',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Battle',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default=0, max_length=60)),
                ('time', models.CharField(max_length=40)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=5)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('zhuangtai', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Booth',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.IntegerField(default=0)),
                ('booth_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('money_name', models.CharField(default=0, max_length=20)),
                ('money_sx', models.CharField(default=0, max_length=20)),
                ('item_money', models.CharField(default=0, max_length=50)),
                ('item_count', models.CharField(default=0, max_length=50)),
                ('item_area_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_item_id', models.IntegerField(default=0)),
                ('item_id', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='BoothArea',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('booth_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('money_name', models.CharField(default=0, max_length=20)),
                ('money_sx', models.CharField(default=0, max_length=20)),
                ('procedures', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='BoothPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.IntegerField(default=0)),
                ('area_id', models.CharField(db_index=True, default=1, max_length=5)),
                ('map_id', models.IntegerField(default=0)),
                ('booth_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('money_name', models.CharField(default=0, max_length=20)),
                ('money_sx', models.CharField(default=0, max_length=20)),
                ('time', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='BossRanking',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_name', models.CharField(default=0, max_length=50)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('value', models.CharField(default=0, max_length=50)),
                ('zhuangtai', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='CangKuPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('item_id', models.IntegerField(default=0)),
                ('area_name', models.CharField(default=0, max_length=20)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('type', models.CharField(default='', max_length=20)),
                ('type_id', models.CharField(default=0, max_length=20)),
                ('bangding', models.CharField(default=0, max_length=20)),
                ('suoding', models.CharField(default=0, max_length=20)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('pets_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('count', models.CharField(default=0, max_length=20)),
                ('update', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
                ('duixiang', models.CharField(default=0, max_length=20)),
                ('biaoshi', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Card',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('card', models.CharField(max_length=200)),
                ('money', models.CharField(max_length=200)),
                ('player_id', models.CharField(default=0, max_length=20)),
                ('time', models.CharField(max_length=50)),
                ('is_settlement', models.CharField(default=0, max_length=10)),
                ('settlement_tiem', models.CharField(default=0, max_length=10)),
                ('zhuangtai', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='CdkNumber',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('number', models.CharField(db_index=True, default=0, max_length=10)),
                ('zhuangtai', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_name', models.CharField(db_index=True, default=0, max_length=30)),
            ],
        ),
        migrations.CreateModel(
            name='ChatAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, max_length=20, verbose_name='区分聊天块')),
            ],
        ),
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('all_area_name', models.CharField(db_index=True, default=0, max_length=20, verbose_name='区分全部')),
                ('area_name', models.CharField(db_index=True, max_length=20, verbose_name='区分聊天块')),
                ('area_id', models.CharField(db_index=True, max_length=20, verbose_name='区分聊天块')),
                ('sender_player_id', models.CharField(max_length=20, verbose_name='发送人ID')),
                ('sender_player_name', models.CharField(max_length=200, verbose_name='发送人名字')),
                ('message', models.CharField(max_length=200, verbose_name='信息内容')),
                ('player_id', models.CharField(db_index=True, max_length=20, verbose_name='信息接收玩家')),
                ('bangpai_id', models.CharField(default=0, max_length=200, verbose_name='信息接收玩家')),
                ('page_name', models.CharField(default=0, max_length=200, verbose_name='信息接收玩家')),
                ('team_id', models.CharField(default=0, max_length=200, verbose_name='信息接收玩家')),
                ('zhuangtai', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='ChongWu',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Enemy',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
                ('enemy_id', models.CharField(db_index=True, max_length=20)),
                ('count', models.CharField(db_index=True, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('page_name', models.CharField(default='None', max_length=50)),
                ('name', models.CharField(default='None', max_length=50)),
                ('operation', models.CharField(db_index=True, default='None', max_length=20)),
                ('map_id', models.CharField(default='None', max_length=20)),
                ('npc_id', models.CharField(default='None', max_length=20)),
                ('chongwu_id', models.CharField(default='None', max_length=20)),
                ('shibing_id', models.CharField(default='None', max_length=20)),
                ('zuoqi_id', models.CharField(default='None', max_length=20)),
                ('item_id', models.CharField(default='None', max_length=20)),
                ('skill_id', models.CharField(default='None', max_length=20)),
                ('code', models.TextField(verbose_name='代码')),
            ],
        ),
        migrations.CreateModel(
            name='EventAll',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(db_index=True, default=0, max_length=50)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=50)),
                ('code', models.TextField(verbose_name='代码')),
            ],
        ),
        migrations.CreateModel(
            name='EventList',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('event', models.CharField(db_index=True, default='None', max_length=20)),
                ('event_all', models.CharField(db_index=True, default='None', max_length=20)),
                ('display', models.TextField(blank=True, default='', null=True, verbose_name='触发条件')),
                ('execute_display', models.TextField(blank=True, default='', null=True, verbose_name='执行条件')),
                ('content', models.TextField(blank=True, default='', null=True, verbose_name='满足提示')),
                ('not_content', models.TextField(blank=True, default='', null=True, verbose_name='不满足提示')),
                ('code', models.TextField(blank=True, default='', null=True, verbose_name='代码')),
                ('params', models.TextField(default='{}', verbose_name='定义属性')),
                ('position', models.IntegerField(db_index=True, default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Expression',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default=0, max_length=200)),
                ('attribute', models.CharField(db_index=True, default=0, max_length=200)),
                ('expression', models.TextField(default='', verbose_name='表达式')),
            ],
        ),
        migrations.CreateModel(
            name='Forum',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(default=0, max_length=20)),
                ('content', models.TextField(default='', verbose_name='内容')),
                ('user_name', models.CharField(default=0, max_length=200)),
                ('click', models.CharField(default=0, max_length=20)),
                ('reply', models.CharField(default=0, max_length=20)),
                ('time', models.CharField(default=0, max_length=50)),
                ('date', models.CharField(default=0, max_length=50)),
                ('zhuangtai', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='ForumReply',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('title_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('content', models.TextField(default='', verbose_name='内容')),
                ('user_name', models.CharField(default=0, max_length=50)),
                ('time', models.CharField(default=0, max_length=50)),
                ('date', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Friends',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
                ('friends_id', models.CharField(db_index=True, max_length=20)),
                ('zhuangtai', models.IntegerField(db_index=True, default=0)),
            ],
        ),
        migrations.CreateModel(
            name='GameAreaName',
            fields=[
                ('area_id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(default=0, max_length=200, verbose_name='游戏分区名')),
                ('start_time', models.CharField(default=0, max_length=200, verbose_name='开区时间')),
                ('create_time', models.CharField(default=0, max_length=200, verbose_name='创建时间')),
                ('is_neice', models.CharField(default=False, max_length=200, verbose_name='是否内测')),
            ],
        ),
        migrations.CreateModel(
            name='GameAttribute',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(db_index=True, default=0, max_length=200)),
                ('value', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='GameAttributeNew',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='GameImg',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(default=0, max_length=50)),
                ('name', models.CharField(default=0, max_length=50)),
                ('value', models.TextField(default=0)),
                ('attribute', models.CharField(db_index=True, default=0, max_length=50)),
                ('code', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='GameImgAreaName',
            fields=[
                ('area_id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='GameMap',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(max_length=200)),
                ('map_upper', models.CharField(default=0, max_length=20)),
                ('map_left', models.CharField(default=0, max_length=20)),
                ('map_right', models.CharField(default=0, max_length=20)),
                ('map_lower', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='GameMapAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='GameMapPlaceItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_code', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='GameMapPlaceNpc',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_code', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='GameMapPlaceSkill',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('skill_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='GameNpc',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(max_length=200)),
                ('is_kill', models.CharField(default=0, max_length=20)),
                ('exp_expression', models.TextField(default='')),
                ('money_expression', models.TextField(default='')),
                ('lingqi_expression', models.TextField(default='')),
                ('is_drop', models.CharField(default=0, max_length=20)),
                ('is_boss', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
                ('refresh_time', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='GameNpcs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('ys_id', models.CharField(default=0, max_length=20)),
                ('name', models.CharField(default='', max_length=200)),
                ('desc', models.CharField(default='', max_length=200)),
                ('area_name', models.CharField(default=0, max_length=200)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('is_kill', models.CharField(db_index=True, default=0, max_length=20)),
                ('update', models.CharField(db_index=True, default=0, max_length=20)),
                ('attack_npc', models.CharField(db_index=True, default=0, max_length=20)),
                ('is_boss', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Gangs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('money', models.CharField(default=0, max_length=200)),
                ('max_count', models.CharField(default=0, max_length=20)),
                ('lvl', models.CharField(default=0, max_length=20)),
                ('max_exp', models.CharField(default=0, max_length=20)),
                ('exp', models.CharField(default=0, max_length=20)),
                ('fubangzhu_id', models.CharField(default=0, max_length=20)),
                ('bangzhu_id', models.CharField(default=0, max_length=20)),
                ('generation', models.CharField(default=0, max_length=20)),
                ('create_player_id', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='GangsMember',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('tangkou_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('member_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='GangsTangkou',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('tangzhu_id', models.CharField(default=0, max_length=20)),
                ('futangzhu_id', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='House',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('lvl', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='HouseList',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('house_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=10)),
                ('lvl', models.CharField(db_index=True, default=0, max_length=10)),
                ('shuxing', models.CharField(db_index=True, default=0, max_length=20)),
                ('shuxing_value', models.IntegerField(db_index=True, default=0)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Img',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(default=0, max_length=50)),
                ('name', models.CharField(default=0, max_length=50)),
                ('value', models.ImageField(upload_to='./web/static/img/')),
                ('attribute', models.CharField(db_index=True, default=0, max_length=50)),
                ('code', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='ImgAreaName',
            fields=[
                ('area_id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='InBangHui',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('tangkou_id', models.CharField(default=0, max_length=20)),
                ('player_id', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='InGangs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('tangkou_id', models.CharField(default=0, max_length=20)),
                ('player_id', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='InTeam',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20, verbose_name='玩家ID')),
                ('team_id', models.CharField(db_index=True, default=0, max_length=20, verbose_name='组队ID')),
            ],
            options={
                'verbose_name': '申请组队信息',
                'verbose_name_plural': '申请组队信息',
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=252)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(max_length=20)),
                ('type', models.CharField(max_length=20)),
                ('type_id', models.CharField(default=0, max_length=20)),
                ('duixiang', models.CharField(default=0, max_length=20)),
                ('pm_money', models.CharField(db_index=True, default=0, max_length=30)),
                ('money', models.CharField(db_index=True, default=0, max_length=30)),
                ('bangpai_gongxian', models.CharField(db_index=True, default=0, max_length=30)),
                ('jifen_zb', models.CharField(db_index=True, default=0, max_length=30)),
                ('bangding', models.CharField(default=0, max_length=30)),
                ('biaoshi', models.CharField(default=0, max_length=20)),
                ('time', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='ItemAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='ItemMap',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=252)),
                ('desc', models.TextField(default='')),
                ('item_id', models.IntegerField(default=0)),
                ('area_name', models.CharField(default=0, max_length=200)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('type', models.CharField(default='', max_length=20)),
                ('type_id', models.CharField(default=0, max_length=20)),
                ('bangding', models.CharField(default=0, max_length=20)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('count', models.CharField(default=0, max_length=200)),
                ('update', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='ItemPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=250)),
                ('desc', models.TextField(default='')),
                ('item_id', models.IntegerField(default=0)),
                ('area_name', models.CharField(default=0, max_length=200)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('type', models.CharField(default='', max_length=20)),
                ('type_id', models.CharField(default=0, max_length=20)),
                ('bangding', models.CharField(default=0, max_length=20)),
                ('bangpai_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('suoding', models.CharField(default=0, max_length=20)),
                ('duixiang', models.CharField(default=0, max_length=20)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('pets_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('count', models.CharField(default=0, max_length=30)),
                ('update', models.CharField(default=0, max_length=20)),
                ('biaoshi', models.CharField(default=0, max_length=20)),
                ('time', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='ItemType',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('type', models.CharField(max_length=20)),
                ('item_areaname', models.CharField(default=0, max_length=200)),
                ('position', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Monster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('ys_id', models.CharField(default=0, max_length=20)),
                ('npc_id', models.CharField(default=0, max_length=20)),
                ('name', models.CharField(default='', max_length=200)),
                ('desc', models.CharField(default='', max_length=200)),
                ('player_id', models.CharField(default=0, max_length=20)),
                ('is_drop', models.CharField(default=0, max_length=20)),
                ('exp_expression', models.TextField(default='')),
                ('money_expression', models.TextField(default='')),
                ('lingqi_expression', models.TextField(default='')),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
                ('zhuangtai', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Mount',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('zuoqi_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='OpenGame',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('game_area_name', models.CharField(db_index=True, default=0, max_length=40)),
                ('game_name', models.CharField(default=0, max_length=40)),
                ('game_desc', models.CharField(default=0, max_length=252)),
                ('game_id', models.CharField(default=0, max_length=40)),
                ('game_http', models.CharField(default=0, max_length=252)),
                ('img_0_code', models.CharField(default=0, max_length=252)),
                ('img_1_code', models.CharField(default=0, max_length=252)),
                ('img_2_code', models.CharField(default=0, max_length=252)),
                ('img_3_code', models.CharField(default=0, max_length=252)),
                ('img_4_code', models.CharField(default=0, max_length=252)),
                ('img_5_code', models.CharField(default=0, max_length=252)),
                ('img_6_code', models.CharField(default=0, max_length=252)),
                ('img_7_code', models.CharField(default=0, max_length=252)),
                ('img_8_code', models.CharField(default=0, max_length=252)),
                ('img_9_code', models.CharField(default=0, max_length=252)),
                ('img_10_code', models.CharField(default=0, max_length=252)),
            ],
        ),
        migrations.CreateModel(
            name='Operation',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.TextField(verbose_name='代码')),
                ('content', models.TextField(default='', verbose_name='内容')),
                ('display', models.TextField(default='', verbose_name='显示')),
                ('page_name', models.CharField(db_index=True, default='None', max_length=200)),
                ('map_id', models.CharField(default='None', max_length=20)),
                ('npc_id', models.CharField(default='None', max_length=20)),
                ('shibing_id', models.CharField(default='None', max_length=20)),
                ('skill_id', models.CharField(default='None', max_length=20)),
                ('item_id', models.CharField(default='None', max_length=20)),
                ('chongwu_id', models.CharField(default='None', max_length=20)),
                ('zuoqi_id', models.CharField(default='None', max_length=20)),
                ('position', models.IntegerField(db_index=True, default=0)),
                ('event', models.CharField(db_index=True, default='None', max_length=20)),
                ('is_input', models.CharField(default=0, max_length=20)),
            ],
            options={
                'ordering': ['position'],
            },
        ),
        migrations.CreateModel(
            name='PageName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('page_name', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='Parameter',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Pets',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('zc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('chongwu_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('area_name', models.CharField(default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='PetsOut',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('pets_id', models.CharField(default=0, max_length=20)),
                ('position', models.IntegerField(db_index=True, default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Player',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='用户名')),
                ('user_id', models.CharField(default=0, max_length=200, verbose_name='归属用户')),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20, verbose_name='归属分区')),
                ('map_id', models.CharField(db_index=True, default=0, max_length=20, verbose_name='地图ID')),
                ('team_id', models.CharField(default=0, max_length=200, verbose_name='组队ID')),
                ('fuwuqi_xianlu', models.CharField(db_index=True, default=0, max_length=5)),
                ('message', models.TextField(default='', verbose_name='消息')),
                ('time', models.CharField(default=0, max_length=30, verbose_name='最后一次动作')),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
                ('is_designer', models.CharField(default=False, max_length=200, verbose_name='是否设计')),
                ('is_neice', models.CharField(default=False, max_length=200, verbose_name='是否内测')),
                ('ip', models.CharField(db_index=True, default=0, max_length=20, verbose_name='归属IP')),
            ],
        ),
        migrations.CreateModel(
            name='PlayerAttribute',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='PublicAttribute',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(db_index=True, default=0, max_length=50)),
                ('value', models.TextField(default=0)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='PublicAttributeNew',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_id', models.CharField(default=0, max_length=5)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='QuickSkill',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('quick', models.CharField(max_length=20)),
                ('name', models.CharField(default=0, max_length=30)),
                ('skill_id', models.CharField(db_index=True, max_length=20)),
                ('item_id', models.CharField(db_index=True, max_length=20)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Ranking',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('ranking_id', models.CharField(default=0, max_length=20)),
                ('chongwu_id', models.CharField(default=0, max_length=20)),
                ('value', models.CharField(default=0, max_length=50)),
                ('time', models.IntegerField(default=0)),
                ('ranking_sx', models.CharField(db_index=True, default=0, max_length=50)),
                ('ranking_object', models.CharField(db_index=True, default=0, max_length=50)),
                ('area_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='RankingAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('ranking_name', models.CharField(db_index=True, default=0, max_length=50)),
                ('ranking_sx', models.CharField(db_index=True, default=0, max_length=50)),
                ('ranking_expression', models.TextField(default=0)),
                ('ranking_count', models.CharField(default=0, max_length=50)),
                ('ranking_object', models.CharField(default=0, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='RegisterNumber',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('number', models.CharField(db_index=True, default=0, max_length=10)),
                ('zhuangtai', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_name', models.CharField(db_index=True, default=0, max_length=30)),
            ],
        ),
        migrations.CreateModel(
            name='SanBu',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('count', models.CharField(default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('shibing_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='SellGoods',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('npc_id', models.CharField(db_index=True, max_length=10)),
                ('item_id', models.CharField(db_index=True, max_length=10)),
            ],
        ),
        migrations.CreateModel(
            name='ShiBing',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('lvl', models.CharField(max_length=20)),
                ('desc', models.TextField(default='')),
                ('fight_message', models.CharField(default='', max_length=200)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Skills',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('lvl', models.CharField(max_length=20)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('pets_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('mount_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('skill_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Soldier',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('shibing_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('pets_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('update', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Statue',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=10)),
                ('map_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('area_id', models.CharField(db_index=True, default=1, max_length=5)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Suo',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('update', models.CharField(db_index=True, default=1, max_length=10)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='SystemAttribute',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=40)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(default=0, max_length=200)),
                ('type', models.CharField(default=0, max_length=20)),
                ('remove', models.CharField(default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_name', models.CharField(default=0, max_length=20)),
                ('display', models.TextField(default='', verbose_name='触发条件')),
                ('execute_display', models.TextField(default='', verbose_name='执行条件')),
                ('content', models.TextField(default='', verbose_name='满足提示')),
                ('not_content', models.TextField(default='', verbose_name='不满足提示')),
                ('code', models.TextField(default='', verbose_name='代码')),
                ('submit_code', models.TextField(default='', verbose_name='完成代码')),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='TaskAreaName',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area_name', models.CharField(db_index=True, max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='TaskItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('task_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_name', models.CharField(default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_count', models.TextField(default=0)),
                ('item_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_name', models.CharField(default=0, max_length=20)),
                ('item_count', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='TaskItemPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('task_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_name', models.CharField(default=0, max_length=20)),
                ('npc_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('npc_count', models.TextField(default=0)),
                ('item_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_name', models.CharField(default=0, max_length=20)),
                ('item_count', models.TextField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='TaskPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('task_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('zhuangtai', models.CharField(default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('team_name', models.CharField(max_length=200, verbose_name='组队名称')),
                ('player_id', models.CharField(db_index=True, max_length=20, verbose_name='玩家ID')),
                ('team_id', models.CharField(db_index=True, default=0, max_length=20, verbose_name='组队ID')),
            ],
            options={
                'verbose_name': '组队信息',
                'verbose_name_plural': '组队信息',
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_name', models.CharField(db_index=True, default=0, max_length=30)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=10)),
                ('item_name', models.CharField(db_index=True, default=0, max_length=90)),
                ('item_id', models.CharField(db_index=True, default=0, max_length=30)),
                ('item_count', models.CharField(db_index=True, default=0, max_length=30)),
                ('ys_item_id', models.CharField(db_index=True, default=0, max_length=30)),
                ('money', models.CharField(db_index=True, default=0, max_length=30)),
                ('area_id', models.CharField(db_index=True, default=1, max_length=5)),
                ('money_name', models.CharField(db_index=True, default=0, max_length=30)),
                ('money_sx', models.CharField(db_index=True, default=0, max_length=30)),
                ('money_bfb', models.CharField(db_index=True, default=0, max_length=30)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='UhPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='UseEquip',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('pets_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('type_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('tz_biaoshi', models.CharField(db_index=True, default=0, max_length=20)),
                ('item_id', models.CharField(default=0, max_length=20)),
                ('time', models.CharField(default=0, max_length=20)),
                ('ys_item_id', models.CharField(default=0, max_length=20)),
                ('soldier_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('mount_id', models.CharField(db_index=True, default=0, max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=20)),
                ('sid', models.CharField(default='', max_length=200)),
                ('come', models.CharField(default='', max_length=200)),
                ('ip', models.CharField(max_length=20)),
                ('password', models.CharField(max_length=200)),
                ('email', models.EmailField(default='0', max_length=20)),
                ('sex', models.CharField(choices=[('m', '男'), ('w', '女')], default='男', max_length=32)),
                ('number', models.CharField(default='0', max_length=32)),
                ('security', models.CharField(default='0', max_length=32)),
                ('c_time', models.DateTimeField(auto_now_add=True)),
                ('is_designer', models.CharField(default=False, max_length=20, verbose_name='是否设计')),
                ('fuwuqi_xianlu', models.CharField(db_index=True, default=0, max_length=5)),
                ('forum_designer', models.CharField(default=False, max_length=20, verbose_name='是否设计')),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='UtPlayer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='ZhuangYuan',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('player_id', models.CharField(db_index=True, default=0, max_length=20)),
                ('area_name', models.CharField(db_index=True, default=0, max_length=40)),
                ('lvl', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
        migrations.CreateModel(
            name='ZuoQi',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('desc', models.TextField(default='')),
                ('area_name', models.CharField(db_index=True, default=0, max_length=20)),
                ('params', models.TextField(default='{}', verbose_name='属性(不懂不要修改)')),
            ],
        ),
    ]
