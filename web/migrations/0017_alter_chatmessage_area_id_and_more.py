# Generated by Django 4.2.9 on 2024-08-18 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('web', '0016_alter_chatmessage_bangpai_id_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='chatmessage',
            name='area_id',
            field=models.IntegerField(db_index=True, default=0, verbose_name='区分聊天块'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='bangpai_id',
            field=models.IntegerField(db_index=True, default=0, verbose_name='信息接收玩家'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='player_id',
            field=models.IntegerField(db_index=True, default=0, verbose_name='信息接收玩家'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='sender_player_id',
            field=models.IntegerField(db_index=True, default=0, verbose_name='发送人ID'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='team_id',
            field=models.IntegerField(db_index=True, default=0, verbose_name='信息接收玩家'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='zhuangtai',
            field=models.IntegerField(db_index=True, default=0),
        ),
    ]
