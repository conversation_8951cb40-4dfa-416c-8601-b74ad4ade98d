# Generated by Django 4.2.9 on 2024-08-18 11:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('web', '0015_attackinfo_auction_banghui_banghuiitem_banghuinumber_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='chatmessage',
            name='bangpai_id',
            field=models.CharField(db_index=True, default=0, max_length=20, verbose_name='信息接收玩家'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='page_name',
            field=models.CharField(db_index=True, default=0, max_length=20, verbose_name='信息接收玩家'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='sender_player_id',
            field=models.Char<PERSON>ield(db_index=True, max_length=20, verbose_name='发送人ID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='chatmessage',
            name='team_id',
            field=models.Char<PERSON><PERSON>(db_index=True, default=0, max_length=20, verbose_name='信息接收玩家'),
        ),
    ]
