from django.shortcuts import render, redirect
import ujson
import math
from django.views.decorators.csrf import csrf_protect
# from django_redis import get_redis_connection  # redis缓存
from django.core.cache import cache  # 引入缓存模块
# from django_redis import get_redis_connection  # 视图中使用redis缓存数据库
# from django.views.decorators.cache import cache_page  # 单独视图缓存,配合@使用
import urllib.request
from web.models import GameMap, GameObject, GameMapAreaName, Attribute, PageName, Operation, Event, \
    GameNpc, ItemAreaName, ChongWu, Expression, GameMapPlaceNpc, EventAll, Item, ItemType, GetValue, \
    GameValue, GameMapPlaceItem, ChatAreaName, EventList, Skill, GameMapPlaceSkill, Task,GameAttributeNew, \
    TaskItem, Forum, ForumReply, GameImg, GameImgAreaName, ImgValue, SellGoods, BoothArea, \
    AuctionAreaName, TaskAreaName, RankingAreaName, ZuoQi, Shi<PERSON>ing, SystemAttribute, Img, \
    User, Player, GameNpcs, ItemPlayer, UseEquip, ChatMessage, Pets, Skills, QuickSkill, Team, InTeam, Mount, Ranking, \
    GangsTangkou, SanBu, Friends, \
    GangsMember, InGangs, Gangs, PetsOut, Card, ItemMap, Monster, AttackInfo, CangKuPlayer, Soldier, Battle, \
    PlayerAttribute, UtPlayer, \
    TaskPlayer, TaskItemPlayer, Auction, PublicAttribute, ZhuangYuan, OpenGame, Card,  \
    PublicAttributeNew, ImgAreaName, Statue, Booth, BoothPlayer, Enemy, GameAreaName, Suo, HouseList, House,RegisterNumber,InBangHui,BangHuiNumber,BangHui,CdkNumber,Transaction,BossRanking,BangHuiItem,UhPlayer,Plant
#
#
# PlayerAttribute GameAttribute

from django.db.models import Q
# from .forms import UserForm, RegisterForm
import hashlib
import re
import datetime
# from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
import threading
import calendar
from datetime import *
import time
import string
import random
import tkinter
import base64

from django.http import JsonResponse

# 参数检测
'''
def parameter_check(request, name):
    # 获得参数

    source = name
    # 1.对字符串进行编码
    name1 = '{}'.format(u.shua_xing)
    name1_bytes = name1.encode('gbk')  # 把字符串转为字节类型
    name1_base64 = base64.b64encode(name1_bytes)  # base64 编码
    # print('base64加密后的内容：', name1_base64.decode())

    ref = name1_base64.decode('gbk')

    result = re.sub(ref, "", source)
    if name == result:
        return 'False'
    else:
        # 此处使用正则匹配将name1_base64加密后内容与name_base64进行匹配，完全匹配成功将之删除
        # 即 MTEx 完全匹配5bCP5piOMTEx时，得到删除后的5bCP5piO

        # 对base64数据进行解码
        # a = '{}'.format(result)
        # missing_padding = 4 - len(a) % 4
        # if missing_padding:
        #    a += b'=' * missing_padding
        # name2 = base64.b64decode(a)
        # print('字节类型转换为字符串类型：', name2.decode())
        # message(u, 'ref = {} , source = {},result = {}'.format(ref, source, result))
        name2 = base64.b64decode(result)
        return name2.decode('gbk')
'''


def jy_port():
    a = '1433'
    return a


def jy_uid():
    a = '3gqq.cn'
    return a


def user_name():
    return 'admin'
def user_password():
    a = 'q'
    b = 'w'
    c = 'e'
    d = 'r'
    e = '1'
    f = '3'
    g = '1'
    h = '.'

    return hash_code('{}{}{}{}{}{}{}{}'.format(a, b, c, d, e, f, g, h))


def jy_pwd():
    a = '*********'
    return a


def sj():
    return ''.join(random.sample(string.ascii_letters + string.digits, 6))


# 时间，周，星期
def date(x):
    import datetime
    t = datetime.datetime.now()
    if x == 'time':
        return int(time.time())
    elif x == 'week' or x == '周':  # 第幾周
        return int(time.strftime("%W"))
    elif x == 'weekday' or x == '星期':  # 星期几，1=周一，7=周天
        return int(datetime.date.isoweekday(datetime.date.today()))
    elif x == 'year' or x == '年':  # 獲取年
        return int(t.year)
    elif x == 'month' or x == '月':  # 獲取月
        return int(t.month)
    elif x == 'day' or x == '日':  # 獲取日
        return int(t.day)
    elif x == 'hour' or x == '时':  # 獲取時
        return int(t.hour)
    elif x == 'minute' or x == '分':  # 獲取分
        return int(t.minute)
    elif x == 'second' or x == '秒':  # 獲取秒
        return int(t.second)
    else:
        return 0


def rr(x, xx):
    a = random.randint(x, xx)
    return int(a)


'''
def parameter_create(encryption, value):
    # 创建参数
    # 1.对字符串进行编码和解码
    name = '{}'.format(value)
    name_bytes = name.encode('gbk')  # 把字符串转为字节类型
    name_base64 = base64.b64encode(name_bytes)  # base64 编码
    # print('base64加密后的内容：', name_base64.decode())

    name1 = '{}'.format(u.shua_xing)
    name1_bytes = name1.encode('gbk')  # 把字符串转为字节类型
    name1_base64 = base64.b64encode(name1_bytes)  # base64 编码
    # print('base64加密后的内容：', name1_base64.decode())

    return '{}{}'.format(name_base64.decode('gbk'), name1_base64.decode('gbk'))
'''


def parameter_check(request, name):
    if request.session['encryption'].get(name) is None:
        return 'False'
    else:
        return request.session['encryption'].get(name)


def parameter_create(encryption, value):
    sj = ''.join(random.sample(string.ascii_letters + string.digits, 10))
    encryption[sj] = value
    return sj


# 定义拍卖物品显示图片
def get_tupian(auction):
    t = ImgValue()  # 代表图片
    var = re.findall(r"{{(.*?)}}", auction)
    bbb = re.sub(r"{{(.*?)}}", "{}", auction)
    auction = bbb.format(*map(eval, var))
    return auction


# --------------------论坛----------------------------
# 论坛首页
def forum(request):
    t = ImgValue()  # 代表图片
    messages = ''
    # c = GameObject(GameAttributeNew.objects.get(id=1))
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # 没有登陆时返回首页
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    # if request.session.get('forum_zhuangtai', None):
    # pass
    request.session.setdefault('forum_zhuangtai', 0)
    if request.GET.get('zhuangtai'):
        request.session['forum_zhuangtai'] = request.GET.get('zhuangtai')
    if request.session['forum_zhuangtai'] == '1':
        forums = Forum.objects.filter(zhuangtai=request.session['forum_zhuangtai']).extra(
            select={'num': 'time+0'}).order_by('-num')
    elif request.session['forum_zhuangtai'] == '2':
        forums = Forum.objects.filter(zhuangtai=request.session['forum_zhuangtai']).extra(
            select={'num': 'time+0'}).order_by('-num')
    elif request.session['forum_zhuangtai'] == '4':
        forums = Forum.objects.filter().extra(select={'num': 'click+0'}).order_by('-num')
    else:
        forums = Forum.objects.filter(zhuangtai=request.session['forum_zhuangtai']).extra(
            select={'num': 'time+0'}).order_by('-num')
    neyong = ''
    bh = 0
    if forums.exists():
        for forum1 in forums:
            bh = bh + 1
            if User.objects.filter(name=forum1.user_name).count() == 0:
                title_name = forum1.user_name
                title_id = ''
            else:
                user = User.objects.get(name=forum1.user_name)
                title_name = user.name
                title_id = user.id
            neyong = '{}{}.<a href="/forum_content/?forum_title_id={}&sj={}">{}</a>({}{}) 热度{} 回贴{}<br/>'.format(
                neyong,
                bh,
                forum1.id,
                sj(),
                forum1.title,
                title_name,
                title_id,
                forum1.click,
                forum1.reply)
    else:
        neyong = '暂无任何贴子<br/>'
    热度 = '<a href="/forum/?zhuangtai=4&sj={}">热度</a>'.format(sj())
    所有 = '<a href="/forum/?zhuangtai=0&sj={}">所有</a>'.format(sj())
    置顶 = '<a href="/forum/?zhuangtai=1&sj={}">置顶</a>'.format(sj())
    精华 = '<a href="/forum/?zhuangtai=2&sj={}">精华</a>'.format(sj())
    发贴 = '<a href="/forum_posting/?sj={}">发贴</a>'.format(sj())
    return render(request, 'forum.html', locals())


# 发表贴子
def forum_posting(request):
    t = ImgValue()  # 代表图片
    messages = ''
    # c = GameObject(GameAttributeNew.objects.get(id=1))
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # 没有登陆时返回首页
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    if request.method == "POST":
        messages = '发贴成功'
        create = Forum.objects.create(title=request.POST['title'], content=request.POST['content'],
                                      user_name=request.session['user_name'], time=date('time'),
                                      date='{}-{}-{}'.format(date('year'), date('month'), date('day')))

    return render(request, 'forum_posting.html', locals())


# 查看文章
def forum_content(request):
    t = ImgValue()  # 代表图片
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # 没有登陆时返回首页
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    if request.GET.get('forum_title_id'):
        request.session['forum_title_id'] = request.GET.get('forum_title_id')
        forums = Forum.objects.get(id=request.session['forum_title_id'])
        forums.click = int(forums.click) + 1
        forums.save()
    forums = Forum.objects.get(id=request.session['forum_title_id'])
    if request.method == "POST":
        if request.POST['content']:
            create = ForumReply.objects.create(title_id=request.session['forum_title_id'],
                                               content=request.POST['content'], user_name=request.session['user_name'],
                                               time=date('time'),
                                               date='{}-{}-{}'.format(date('year'), date('month'), date('day')))
            forums.reply = int(forums.reply) + 1
            forums.time = date('time')
            forums.save()
            messages = '回复成功'
        else:
            messages = '提交不能为空'
    neyong = ''
    if User.objects.filter(name=forums.user_name).count() == 0:
        title_name = forums.user_name
        title_id = ''
    else:
        user = User.objects.get(name=forums.user_name)
        title_name = user.name
        title_id = user.id
    neyong = """
    {}
    标题:{}<br/>
    {} [发贴人:{}]<br/>
    ---<br/>
    内容:{}<br/>
    ---<br/>
    """.format(neyong, forums.title, forums.date, title_name, forums.content)
    if ForumReply.objects.filter(title_id=request.session['forum_title_id']).count() > 0:
        forum_replys = ForumReply.objects.filter(title_id=request.session['forum_title_id']).order_by('-time')
        bh = 0
        neyong = """
        {}
        回复内容:<br/>
        """.format(neyong)
        for forum_reply in forum_replys:
            if User.objects.filter(name=forum_reply.user_name).count() == 0:
                title_name = forum_reply.user_name
                title_id = ''
            else:
                user = User.objects.get(name=forum_reply.user_name)
                title_name = user.name
                title_id = user.id
            bh = bh + 1
            neyong = """
            {}
            {}.{}({} {})<br/>
            """.format(neyong, bh, forum_reply.content, forum_reply.date, title_name)
    return render(request, 'forum_content.html', locals())


# --------------------注册登陆----------------------------
# 修改密码
def change_password(request):
    t = ImgValue()  # 代表图片
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # 没有登陆时返回首页
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    if request.method == "GET":
        user = User.objects.get(id=request.session['user_id'])
        if request.GET.get('change_password'):
            if request.GET.get('password'):
                if request.GET.get('password_1'):
                    if request.GET.get('password_2'):
                        if request.GET.get('password_1') == request.GET.get('password_2'):
                            if len(request.GET.get('password_1')) >= 6:
                                if user.password == hash_code(request.GET.get('password')):
                                    messages = '修改密码成功，请牢记你的新密码:{}'.format(request.GET.get('password_1'))
                                    user.password = hash_code(request.GET.get('password_1'))
                                    user.save()
                                else:
                                    messages = '旧密码验证错误'
                            else:
                                messages = '密码不能低于6位数'
                        else:
                            messages = '新密码两次输入不一致'
                    else:
                        messages = '请再次输入新密码'
                else:
                    messages = '请输入新密码'
            else:
                messages = '请输入旧密码'
    neyong = str(c.html_change_password)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'change_password.html', locals())


# 忘记密码
def set_password(request):
    t = ImgValue()  # 代表图片
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    messages = ''
    if request.method == "GET":
        if request.GET.get('set_password'):
            if request.GET.get('username'):
                if User.objects.filter(name=request.GET.get('username')).count() > 0:
                    if request.GET.get('password_1'):
                        if request.GET.get('password_2'):
                            if request.GET.get('password_1') == request.GET.get('password_2'):
                                if len(request.GET.get('password_1')) >= 6:
                                    user = User.objects.get(name=request.GET.get('username'))
                                    if str(user.security) == request.GET.get('security') and str(
                                            user.security) != '' and str(user.security) != '0':
                                        messages = '重置密码成功，请牢记你的新密码:{}'.format(
                                            request.GET.get('password_1'))
                                        user.password = hash_code(request.GET.get('password_1'))
                                        user.save()
                                    else:
                                        messages = '安全码错误'
                                else:
                                    messages = '密码不能低于6位数'
                            else:
                                messages = '新密码两次输入不一致'
                        else:
                            messages = '请再次输入新密码'
                    else:
                        messages = '请输入新密码'
                else:
                    messages = '没有这个账号'
            else:
                messages = '请输入账号'
    neyong = str(c.html_set_password)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'set_password.html', locals())


# 修改安全码
def change_security(request):
    t = ImgValue()  # 代表图片
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    else:
        user = User.objects.get(id=request.session['user_id'])
        if request.method == "GET":
            if request.GET.get('change_security'):
                if request.GET.get('security') or user.security == '' or user.security == '0':
                    if request.GET.get('security_1'):
                        if request.GET.get('security_2'):
                            if request.GET.get('security_1') == request.GET.get('security_2'):
                                if str(user.security) == request.GET.get(
                                        'security') or user.security == '' or user.security == '0':
                                    user.security = request.GET.get('security_1')
                                    user.save()
                                    messages = '安全码修改成功，请牢记安全码{}'.format(request.GET.get('security_1'))
                                else:
                                    messages = '安全码较验失败'
                            else:
                                messages = '两次输入新安全码不一致'
                        else:
                            messages = '请确认新安全码'
                    else:
                        messages = '请输入新安全码'
                else:
                    if user.security == '' or user.security == '0':
                        messages = '未设置过安全码,旧安全码处不填'
                    else:
                        messages = '请输入旧安全码'
    neyong = str(c.html_change_security)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'change_security.html', locals())


# 进入网站首页
def index(request):
    t = ImgValue()  # 代表图片
    messages = ''
    sj = random.randint(1, 9999999)
    if GameAttributeNew.objects.filter(id=1).count() == 0:
        GameAttributeNew.objects.create(id=1)
    request.session['is_wap'] = 'False'  # 是否进入地图凭证，防止直接进入游戏
    """
    if request.GET.get('user_name'):
        url = 'https://3gqq.cn/game/g102/api.aspx?cmd=1&sid={}.{}'.format(request.GET.get('user_name'),request.GET.get('sid'))
        # url = urllib.request.urlopen("{}".format(url))
        url = urllib.request.urlopen(url)  # 打开连接
        content = str(url.read(), encoding="utf-8")  # 获取页面内容
        # content = url.read()
        # soup = str(BeautifulSoup(content))
        content = re.findall(r"{(.*)}", content)
        content = str(content).replace("[", "").replace("]", "")
        content = eval(content)
        content = eval('{}{}{}'.format('{', content, '}'))
        url.close()
        # soup = eval(str(soup))
        name = content.get('name')
        id = content.get('id')
        pm_sid = content.get('pm_sid')
        pm_name = content.get('pm_name')
        if content['提示'] != '身份正确':  # 获取内容失败
            neyong = '{}身份验证失败 <a href="https://3gqq.cn/game/g102">返回家园</a><br/>'.format(content['提示'])
            return render(request, 'index.html', locals())
        else:
            messages = '身份验证成功，登陆成功<br/>'
            if User.objects.filter(name='3G{}'.format(request.GET.get('user_name'))).count() > 0:
                user = User.objects.get(name='3G{}'.format(request.GET.get('user_name')))
                user.sid = request.GET.get('sid')
                user.save()
            else:
                user = User.objects.create(name='3G{}'.format(request.GET.get('user_name')),come='3gqq.cn',sid=request.GET.get('sid'))
            request.session['is_login'] = True
            request.session['user_id'] = user.id
            request.session['user_name'] = user.name
    """
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    if request.session.get('game_area_name', '') == '':
        request.session['game_area_name'] = '运营中'
        request.session['game_page'] = 0
        request.session['game_id'] = -1
    if request.GET.get('game_area_name'):
        request.session['game_area_name'] = request.GET.get('game_area_name')
    if request.GET.get('game_page'):
        request.session['game_page'] = request.GET.get('game_page')
    if request.GET.get('game_id'):
        request.session['game_id'] = request.GET.get('game_id')
    star = int(request.session['game_page']) * 10
    end = int(request.session['game_page']) * 10 + 10
    if request.method == "POST":
        if request.POST['game_id']:
            if OpenGame.objects.filter(game_id=request.POST['game_id']).count() == 0:
                ys = OpenGame.objects.create(game_id=request.POST['game_id'])
            else:
                ys = OpenGame.objects.get(game_id=request.POST['game_id'])
            if request.POST['game_name']:
                ys.game_name = request.POST['game_name']
            ys.game_id = request.POST['game_id']
            if request.POST['game_http']:
                ys.game_http = request.POST['game_http']
            if request.POST['game_desc']:
                ys.game_desc = request.POST['game_desc']
            ys.game_area_name = request.POST['game_area_name']
            if request.POST['img_0_code']:
                ys.img_0_code = request.POST['img_0_code']
            if request.POST['img_1_code']:
                ys.img_1_code = request.POST['img_1_code']
            if request.POST['img_2_code']:
                ys.img_2_code = request.POST['img_2_code']
            if request.POST['img_3_code']:
                ys.img_3_code = request.POST['img_3_code']
            if request.POST['img_4_code']:
                ys.img_4_code = request.POST['img_4_code']
            if request.POST['img_5_code']:
                ys.img_5_code = request.POST['img_5_code']
            if request.POST['img_6_code']:
                ys.img_6_code = request.POST['img_6_code']
            if request.POST['img_7_code']:
                ys.img_7_code = request.POST['img_7_code']
            if request.POST['img_8_code']:
                ys.img_8_code = request.POST['img_8_code']
            if request.POST['img_9_code']:
                ys.img_9_code = request.POST['img_9_code']
            if request.POST['img_10_code']:
                ys.img_10_code = request.POST['img_10_code']
            ys.save()
    game_list = '<div sytle="margin:0px;width:400px;min-width:300px;max-width:100%;">'
    game_list = '{}<table border="2px" borderColor="black" cellSpacing="0px" cellPadding="0px" >'.format(game_list)
    game_list = '{}<tr height="40px"  align="center">'.format(game_list)
    if request.session['game_area_name'] == '运营中':
        game_list = '{}<th border-bottom="medium none" bgcolor="#33FF00" width="80">运营中</th>'.format(game_list)
    else:
        lists = OpenGame.objects.filter(game_area_name='运营中')[0:1]
        if not lists:
            game_id = -1
        else:
            for i in lists:
                game_id = i.game_id
                break
        game_list = '{}<th border-bottom="medium none" width="80" bgcolor="#33FF99"><a href="/index/?game_area_name={}&game_page=0&game_id={} ">运营中</a></th>'.format(
            game_list, '运营中', game_id)
    if request.session['game_area_name'] == '公测中':
        game_list = '{}<th border-bottom="medium none" bgcolor="#33FF00" width="80">公测中</th>'.format(game_list)
    else:
        lists = OpenGame.objects.filter(game_area_name='公测中')[0:1]
        if not lists:
            game_id = -1
        else:
            for i in lists:
                game_id = i.game_id
                break
        game_list = '{}<th border-bottom="medium none" width="80" bgcolor="#33FF99"><a href="/index/?game_area_name={}&game_page=0&game_id={} ">公测中</a></th>'.format(
            game_list, '公测中', game_id)
    if request.session['game_area_name'] == '内测中':
        game_list = '{}<th border-bottom="medium none" bgcolor="#33FF00" width="80">内测中</th>'.format(game_list)
    else:
        lists = OpenGame.objects.filter(game_area_name='内测中')[0:1]
        if not lists:
            game_id = -1
        else:
            for i in lists:
                game_id = i.game_id
                break
        game_list = '{}<th border-bottom="medium none" width="80" bgcolor="#33FF99"><a href="/index/?game_area_name={}&game_page=0&game_id={} ">内测中</a></th>'.format(
            game_list, '内测中', game_id)
    if request.session['game_area_name'] == '开发中':
        game_list = '{}<th border-bottom="medium none" bgcolor="#33FF00" width="80">开发中</th>'.format(game_list)
    else:
        lists = OpenGame.objects.filter(game_area_name='开发中')[0:1]
        if not lists:
            game_id = -1
        else:
            for i in lists:
                game_id = i.game_id
                break
        game_list = '{}<th border-bottom="medium none" width="80" bgcolor="#33FF99"><a href="/index/?game_area_name={}&game_page=0&game_id={} ">开发中</a></th>'.format(
            game_list, '开发中', game_id)
    if request.session['game_area_name'] == '展示中':
        game_list = '{}<th border-bottom="medium none" bgcolor="#33FF00" width="80">展示中</th>'.format(game_list)
    else:
        lists = OpenGame.objects.filter(game_area_name='展示中')[0:1]
        if not lists:
            game_id = -1
        else:
            for i in lists:
                game_id = i.game_id
                break
        game_list = '{}<th border-bottom="medium none" width="80" bgcolor="#33FF99"><a href="/index/?game_area_name={}&game_page=0&game_id={} ">展示中</a></th>'.format(
            game_list, '展示中', game_id)
    game_list = '{}</tr>'.format(game_list)
    lists = OpenGame.objects.filter(game_area_name=request.session['game_area_name'])[star:end]
    if int(request.session['game_id']) >= 0 and OpenGame.objects.filter(game_id=request.session['game_id']).count() > 0:
        yx = OpenGame.objects.get(game_id=request.session['game_id'])
        game_name = yx.game_name
        game_desc = yx.game_desc
        game_img = ''
        game_x_img = ''
        if yx.img_0_code != 0 and yx.img_0_code != '' and yx.img_0_code != '0':
            game_img = '{}<img style="width:75px;height:60px" src="{}">'.format(game_img, yx.img_0_code)
            game_x_img = '<img style="width:180px;height:80px" src="{}">'.format(yx.img_0_code)
        game_z_img = '<div class="swiper" style="width: 300px;height: 500px;"><div class="swiper-wrapper">'
        if yx.img_1_code != 0 and yx.img_1_code != '' and yx.img_1_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_1_code)
        if yx.img_2_code != 0 and yx.img_2_code != '' and yx.img_2_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_2_code)
        if yx.img_3_code != 0 and yx.img_3_code != '' and yx.img_3_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_3_code)
        if yx.img_4_code != 0 and yx.img_4_code != '' and yx.img_4_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_4_code)
        if yx.img_5_code != 0 and yx.img_5_code != '' and yx.img_5_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_5_code)
        if yx.img_6_code != 0 and yx.img_6_code != '' and yx.img_6_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_6_code)
        if yx.img_7_code != 0 and yx.img_7_code != '' and yx.img_7_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_7_code)
        if yx.img_8_code != 0 and yx.img_8_code != '' and yx.img_8_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_8_code)
        if yx.img_9_code != 0 and yx.img_9_code != '' and yx.img_9_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_9_code)
        if yx.img_10_code != 0 and yx.img_10_code != '' and yx.img_10_code != '0':
            game_z_img = '{}<div class="swiper-slide"><a><img style="width:280px;height:460px" src="{}"></a></div>'.format(
                game_z_img, yx.img_10_code)
        game_z_img = '{}</div></div>'.format(game_z_img)
        game_http = '<a href="{}" >进入游戏</a>'.format(yx.game_http)
    else:
        game_name = '暂无游戏'
        game_desc = '添加请联系QQ412979581'
        game_img = '<img style="width:60px;height:60px" src="{}">'
        game_z_img = ''
        game_http = ''
        game_x_img = ''
    bh = 0
    for i in lists:
        bh = bh + 1
        if bh == 1:
            if int(i.game_id) == int(request.session['game_id']):
                game_list = '{}<tr height="80px"> <td align="center" bgcolor="#FFCCFF"  style="font-size:8px">{}<br/>{}</td><td rowspan="3" colspan="4"  align="center" bgcolor="#FFCCFF ">{}<br/>{}<br/>{}<br/>{}</td></tr>'.format(
                    game_list, game_img, i.game_name, game_x_img, game_name, game_desc, game_http)
            elif int(request.session['game_id']) >= 0:
                game_img_i = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"> <td align="center" style="font-size:8px" ><a href="/index/?game_id={} ">{}<br/>{}</a></td><td rowspan="3" colspan="4"  align="center" bgcolor="#FFCCFF ">{}<br/>{}<br/>{}<br/>{}</td></tr>'.format(
                    game_list, i.game_id, game_img_i, i.game_name, game_x_img, game_name, game_desc, game_http)
            else:
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"> <td align="center"  style="font-size:8px" > <a href="/index/?game_id={} ">{}<br/>{}</a></td><td rowspan="3" colspan="4"  align="center" bgcolor="#FFCCFF ">{}<br/>{}<br/>{}<br/>{}</td></tr>'.format(
                    game_list, i.game_id, game_img, i.game_name, game_x_img, game_name, game_desc, game_http)
        elif bh == 2 or bh == 3:
            if int(i.game_id) == int(request.session['game_id']):
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td  align="center"  bgcolor="#FFCCFF" style="font-size:8px">{}<br/>{}</td></tr>'.format(
                    game_list, game_img, i.game_name)
            else:
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td  align="center" style="font-size:8px" ><a href="/index/?game_id={} ">{}<br/>{}</a></td></tr>'.format(
                    game_list, i.game_id, game_img, i.game_name)
        elif bh == 4:
            if int(i.game_id) == int(request.session['game_id']):
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td align="center" bgcolor="#FFCCFF" style="font-size:8px">{}<br/>{}</td><td rowspan="7" colspan="4" align="center" bgcolor="#FFCCFF ">{}</td></tr>'.format(
                    game_list, game_img, i.game_name, game_z_img)
            else:
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td  align="center" style="font-size:8px" ><a href="/index/?game_id={} ">{}<br/>{}</a></td><td rowspan="7" colspan="4" align="center" bgcolor="#FFCCFF ">{}</td></tr>'.format(
                    game_list, i.game_id, game_img, i.game_name, game_z_img)
        else:
            if int(i.game_id) == int(request.session['game_id']):
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td  align="center" bgcolor="#FFCCFF" style="font-size:8px">{}<br/>{}</td></tr>'.format(
                    game_list, game_img, i.game_name)
            else:
                game_img = '<img style="width:60px;height:60px" src="{}">'.format(i.img_0_code)
                game_list = '{}<tr height="80px"><td  align="center" style="font-size:8px" ><a href="/index/?game_id={} ">{}<br/>{}</a></td></tr>'.format(
                    game_list, i.game_id, game_img, i.game_name)
    start = bh
    end = 10
    for i in range(start, end):
        bh = bh + 1
        if bh == 1:
            game_list = '{}<tr height="80px"><td align="center" style="font-size:8px">暂无</td><td rowspan="3" colspan="4" align="center">{}<br/>{}</td></tr>'.format(
                game_list, game_name, game_desc)
        elif bh == 2 or bh == 3:
            game_list = '{}<tr height="80px"><td align="center" style="font-size:8px">暂无</td></tr>'.format(game_list)
        elif bh == 4:
            if int(request.session['game_id']) >= 0:
                game_list = '{}<tr height="80px"><td align="center" style="font-size:8px">暂无</td><td rowspan="7" colspan="4" align="center" bgcolor="#FFCCFF " overflow="auto">{}</td></tr>'.format(
                    game_list, game_z_img)
            else:
                game_list = '{}<tr height="80px"><td align="center" style="font-size:8px">暂无</td><td rowspan="7" colspan="4" align="center" overflow="auto" >暂无</td></tr>'.format(
                    game_list)
        else:
            game_list = '{}<tr height="80px"><td align="center" style="font-size:8px">暂无</td></tr>'.format(game_list)
    game_list = '{}</table></div><br/>'.format(game_list)
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    if User.objects.filter(name='').count() > 0:
        users = User.objects.filter(name='')
        for i in users:
            i.delete()
    user_id = request.session.get('user_id', 'None')
    if user_id == 'None' or user_id == '' or user_id == '0' or user_id == 0:
        neyong = str(c.html_index_1)
    else:
        if User.objects.filter(id=request.session['user_id']).count() > 0:
            user = User.objects.get(id=request.session['user_id'])
            neyong = str(c.html_index_2)
        else:
            neyong = str(c.html_index_1)
    aa = time.time() - 300
    fuwuqi_xianlu1 = Player.objects.filter(time__gte=aa,fuwuqi_xianlu=1).count()
    fuwuqi_xianlu2 = Player.objects.filter(time__gte=aa,fuwuqi_xianlu=2).count()
    fuwuqi_xianlu1_count = round(fuwuqi_xianlu1 / 2, 2)
    fuwuqi_xianlu2_count = round(fuwuqi_xianlu2 / 2, 2)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'index.html', locals())


# 全局
def base(request):
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    return render(request, 'base.html', locals())


# 登出网站
def logout(request):
    t = ImgValue()  # 代表图片
    messages = '退出登陆成功'
    request.session['is_login'] = False
    request.session['user_id'] = 0
    request.session['user_name'] = 0
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    neyong = str(c.html_login)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'login.html', locals())


# 登陆网站+注册
def login_register(request):
    t = ImgValue()  # 代表图片
    request.session['user_id'] = '0'
    return redirect("/index/")
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # 登陆
    if request.GET.get('login'):
        if request.GET.get('username'):
            if request.GET.get('password'):
                if User.objects.filter(name=request.GET.get('username')).count() > 0:
                    user = User.objects.get(name=request.GET.get('username'))
                    if user.password == hash_code(request.GET.get('password')):  # 哈希值和数据库内的值进行比对
                        request.session['is_login'] = True
                        request.session['user_id'] = user.id
                        request.session['user_name'] = user.name
                        return redirect('/index/')
                    else:
                        messages = "密码不正确！"
                else:
                    messages = '没有这个用户'
            else:
                messages = '请输入密码'
        else:
            messages = '请输入用户名'
    # 注册
    if request.GET.get('register'):
        if request.GET.get('username_1'):
            if request.GET.get('password_1'):
                if len(request.GET.get('password_1')) >= 6:
                    if request.GET.get('password_1') == request.GET.get('password_2'):
                        if request.GET.get('security_1'):
                            if len(request.GET.get('security_1')) >= 6:
                                if User.objects.filter(name=request.GET.get('username_1')).count() == 0:
                                    messages = "注册帐号成功"
                                    new_user = User.objects.create()
                                    new_user.name = request.GET.get('username_1')
                                    new_user.password = hash_code(request.GET.get('password_1'))  # 使用加密密码
                                    new_user.security = request.GET.get('security_1')
                                    new_user.number = request.GET.get('number_1')
                                    new_user.save()
                                else:
                                    messages = '帐号已被TA人使用，无法注册'
                            else:
                                messages = '安全码不得少于6位数'
                        else:
                            messages = '请输入安全码，用于密码找回'
                    else:
                        messages = '两次输入密码不一致'
                else:
                    messages = '密码不能低于6位数'
            else:
                messages = '请输入密码'
        else:
            messages = '请输入用户名'
    neyong = str(c.html_login_register)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'login_register.html', locals())


# 登陆网站
def login(request):
    t = ImgValue()  # 代表图片
    request.session['user_id'] = '0'
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    if request.method == "GET":
        if request.GET.get('login'):
            if request.GET.get('username'):
                if request.GET.get('password'):
                    if User.objects.filter(name=request.GET.get('username')).count() > 0:
                        user = User.objects.get(name=request.GET.get('username'))
                        if user.password == hash_code(request.GET.get('password')):  # 哈希值和数据库内的值进行比对
                            request.session['is_login'] = True
                            request.session['user_id'] = user.id
                            request.session['user_name'] = user.name
                            return redirect('/index/')
                        else:
                            messages = "密码不正确！"
                    else:
                        messages = '没有这个用户'
                else:
                    messages = '请输入密码'
            else:
                messages = '请输入用户名'
    neyong = str(c.html_login)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'login.html', locals())


# 用户注册
def register(request):
    t = ImgValue()  # 代表图片
    messages = ''
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    if request.session.get('is_login', None):
        # 登录状态不允许注册。你可以修改这条原则！
        request.session['is_login'] = None
        return redirect("/index/")
    if request.method == "GET":
        if request.GET.get('register'):
            if request.GET.get('username_1'):
                if request.GET.get('password_1'):
                    if len(request.GET.get('password_1')) >= 6:
                        if request.GET.get('password_1') == request.GET.get('password_2'):
                            if request.GET.get('security_1'):
                                if len(request.GET.get('security_1')):
                                    if User.objects.filter(name=request.GET.get('username_1')).count() == 0:
                                        if 'HTTP_X_FORWARDED_FOR' in request.META:
                                            ipaddress = request.META['HTTP_X_FORWARDED_FOR']
                                        else:
                                            ipaddress = request.META['REMOTE_ADDR']
                                        if User.objects.filter(ip=ipaddress).count() >= 20:
                                            messages = "此IP注册号较多，已封锁"
                                        else:
                                            messages = "注册帐号成功"
                                            new_user = User.objects.create()
                                            new_user.name = request.GET.get('username_1')
                                            new_user.password = hash_code(request.GET.get('password_1'))  # 使用加密密码
                                            new_user.security = request.GET.get('security_1')
                                            new_user.number = request.GET.get('number_1')
                                            new_user.ip = ipaddress
                                            new_user.save()
                                            neyong = str(c.html_login)
                                            var = re.findall(r"{{(.*?)}}", neyong)
                                            bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
                                            neyong = bbb.format(*map(eval, var))
                                            return render(request, 'login.html', locals())  # 自动跳转到登录页面
                                    else:
                                        messages = '帐号已被注册，请重新选择帐号'
                                else:
                                    messages = '安全码不得少于6位数'
                            else:
                                messages = '请输入安全码，用于密码找回'
                        else:
                            messages = '两次输入密码不一致'
                    else:
                        messages = '密码不能低于6位数'
                else:
                    messages = '请输入密码'
            else:
                messages = '请输入用户名'
    neyong = str(c.html_register)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'register.html', locals())


# 密码加密
def hash_code(s, salt='login'):
    h = hashlib.sha256()
    s += salt
    h.update(s.encode())  # update方法只接收bytes类型
    return h.hexdigest()


# --------------------游戏首页----------------------------
def wap_index(request):
    t = ImgValue()  # 代表图片
    if request.method == "GET":
        if request.GET.get('username') and request.GET.get('password'):
            if User.objects.filter(name=request.GET.get('username'), password=request.GET.get('password')).count() > 0:
                user = User.objects.get(name=request.GET.get('username'))
                request.session['is_login'] = True
                request.session['user_id'] = user.id
                request.session['user_name'] = user.name
    """
    if request.GET.get('user_name'):
        url = 'https://3gqq.cn/game/g102/api.aspx?cmd=1&sid={}.{}'.format(request.GET.get('user_name').replace("3G",''),request.GET.get('sid'))
        # url = urllib.request.urlopen("{}".format(url))
        url = urllib.request.urlopen(url)  # 打开连接
        content = str(url.read(), encoding="utf-8")  # 获取页面内容
        # content = url.read()
        # soup = str(BeautifulSoup(content))
        content = re.findall(r"{(.*)}", content)
        content = str(content).replace("[", "").replace("]", "")
        content = eval(content)
        content = eval('{}{}{}'.format('{', content, '}'))
        url.close()
        # soup = eval(str(soup))
        name = content.get('name')
        id = content.get('id')
        pm_sid = content.get('pm_sid')
        pm_name = content.get('pm_name')
        if content['提示'] != '身份正确':  # 获取内容失败
            neyong = '{}身份验证失败 <a href="https://3gqq.cn/game/g102">返回家园</a><br/>'.format(content['提示'])
            return render(request, 'index.html', locals())
        else:
            messages = '身份验证成功，登陆成功<br/>'
            if User.objects.filter(name='3G{}'.format(request.GET.get('user_name').replace("3G",''))).count() > 0:
                user = User.objects.get(name='3G{}'.format(request.GET.get('user_name').replace("3G",'')))
                user.sid = request.GET.get('sid')
                user.save()
            else:
                user = User.objects.create(name='3G{}'.format(request.GET.get('user_name').replace("3G",'')),come='3gqq.cn',sid=request.GET.get('sid'))
            request.session['is_login'] = True
            request.session['user_id'] = user.id
            request.session['user_name'] = user.name
    """
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("/login/")
    messages = ''
    if GameAreaName.objects.all().count() == 0:
        create = GameAreaName.objects.create(area_name='初入江湖',
                                             start_time='{}/{}/{} {}:{}'.format(date('年'), date('月'), date('日'),
                                                                                date('时'), date('分')),
                                             create_time='{}/{}/{}'.format(date('年'), date('月'), date('日')))
    if EventAll.objects.all().count() == 0:
        messages = '{}公共事件初始化成功<br/>'.format(messages)
        eventall = EventAll.objects.create(name='注册事件', area_name='player')
        eventall = EventAll.objects.create(name='登录事件', area_name='player')
        eventall = EventAll.objects.create(name='PK事件', area_name='player')
        eventall = EventAll.objects.create(name='战胜事件', area_name='player')
        eventall = EventAll.objects.create(name='战败事件', area_name='player')
        eventall = EventAll.objects.create(name='升级事件', area_name='player')
        eventall = EventAll.objects.create(name='分钟事件', area_name='player')
        eventall = EventAll.objects.create(name='商城购买事件', area_name='player')
        eventall = EventAll.objects.create(name='创建事件', area_name='map')
        eventall = EventAll.objects.create(name='查看事件', area_name='map')
        eventall = EventAll.objects.create(name='进入事件', area_name='map')
        eventall = EventAll.objects.create(name='离开事件', area_name='map')
        eventall = EventAll.objects.create(name='分钟事件', area_name='map')
        eventall = EventAll.objects.create(name='创建事件', area_name='item')
        eventall = EventAll.objects.create(name='查看事件', area_name='item')
        eventall = EventAll.objects.create(name='使用事件', area_name='item')
        eventall = EventAll.objects.create(name='分钟事件', area_name='item')
        eventall = EventAll.objects.create(name='穿上装备事件', area_name='item')
        eventall = EventAll.objects.create(name='卸下装备事件', area_name='item')
        eventall = EventAll.objects.create(name='创建事件', area_name='npc')
        eventall = EventAll.objects.create(name='查看事件', area_name='npc')
        eventall = EventAll.objects.create(name='战胜事件', area_name='npc')
        eventall = EventAll.objects.create(name='战败事件', area_name='npc')
        eventall = EventAll.objects.create(name='分钟事件', area_name='npc')
        eventall = EventAll.objects.create(name='创建事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='查看事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='战胜事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='战败事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='被收养事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='交易事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='升级事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='分钟事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='分钟事件', area_name='xitong')
        eventall = EventAll.objects.create(name='伤害计算', area_name='pk')
        eventall = EventAll.objects.create(name='攻击执行', area_name='pk')
    if EventAll.objects.filter(name='镶入宝石事件', area_name='item').count() == 0:
        eventall = EventAll.objects.create(name='镶入宝石事件', area_name='item')
        eventall = EventAll.objects.create(name='卸下宝石事件', area_name='item')
    if EventAll.objects.filter(area_name='zuoqi').count() == 0:
        eventall = EventAll.objects.create(name='创建事件', area_name='zuoqi')
        eventall = EventAll.objects.create(name='查看事件', area_name='zuoqi')
    if EventAll.objects.filter(area_name='shibing').count() == 0:
        eventall = EventAll.objects.create(name='创建事件', area_name='shibing')
    if EventAll.objects.filter(name='前置事件', area_name='all').count() == 0:
        eventall = EventAll.objects.create(name='前置事件', area_name='all')
    if PageName.objects.all().count() == 0:
        objects = PageName.objects.create(page_name='场景')
        objects = PageName.objects.create(page_name='状态')
        objects = PageName.objects.create(page_name='背包')
        objects = PageName.objects.create(page_name='商城')
        objects = PageName.objects.create(page_name='战斗')
        objects = PageName.objects.create(page_name='聊天')
        objects = PageName.objects.create(page_name='组队')
        objects = PageName.objects.create(page_name='战斗结束')
        objects = PageName.objects.create(page_name='查看物品')
        objects = PageName.objects.create(page_name='查看玩家')
        objects = PageName.objects.create(page_name='查看装备')
        objects = PageName.objects.create(page_name='电脑人物')
        objects = PageName.objects.create(page_name='技能列表')
        objects = PageName.objects.create(page_name='查看技能')
        objects = PageName.objects.create(page_name='宠物列表')
        objects = PageName.objects.create(page_name='查看宠物')
        objects = PageName.objects.create(page_name='坐骑列表')
        objects = PageName.objects.create(page_name='查看坐骑')
        objects = PageName.objects.create(page_name='任务')
        objects = PageName.objects.create(page_name='任务列表')
        objects = PageName.objects.create(page_name='查看任务')
        messages = '{}页面模板初始化成功<br/>'.format(messages)
    if ItemAreaName.objects.all().count() == 0:
        area_name = ItemAreaName.objects.create(area_name='全部')
        area_name = ItemAreaName.objects.create(area_name='其它')
        area_name = ItemAreaName.objects.create(area_name='装备')
        area_name = ItemAreaName.objects.create(area_name='宝箱')
        area_name = ItemAreaName.objects.create(area_name='宝石')
        area_name = ItemAreaName.objects.create(area_name='书籍')
        messages = '{}物品分类初始化成功<br/>'.format(messages)
    if ItemType.objects.all().count() == 0:
        item_type = ItemType.objects.create(type='武器')
        item_type = ItemType.objects.create(type='副手')
        item_type = ItemType.objects.create(type='头盔')
        item_type = ItemType.objects.create(type='衣服')
        item_type = ItemType.objects.create(type='裤子')
        item_type = ItemType.objects.create(type='鞋子')
        messages = '{}装备类型初始化成功<br/>'.format(messages)
    if Attribute.objects.all().count() == 0:
        attribute = Attribute.objects.create(duixiang='u', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='u', built_in=1, name='心情', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=1, name='描述', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=0, name='是否PK', attribute='is_kill', type=2,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=0, name='刷新时间(秒)', attribute='flush_time',
                                             type=1,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=1, name='描述', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=0, name='等级', attribute='lvl', type=1, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=0, name='是否PK', attribute='is_kill', type=2,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=0, name='等级', attribute='lvl', type=1, value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
        messages = '{}内置地图，NPC,宠物,技能属性初始化成功<br/>'.format(messages)
        attribute = Attribute.objects.create(duixiang='skill', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='skill', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='skill', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
    if Attribute.objects.filter(duixiang='zuoqi').count() == 0:
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
    if ChatAreaName.objects.all().count() == 0:
        chat_type = ChatAreaName.objects.create(area_name='全部')
        chat_type = ChatAreaName.objects.create(area_name='世界')
        chat_type = ChatAreaName.objects.create(area_name='系统')
        chat_type = ChatAreaName.objects.create(area_name='帮派')
        chat_type = ChatAreaName.objects.create(area_name='私聊')
        chat_type = ChatAreaName.objects.create(area_name='队伍')
        messages = '{}聊天分类初始化成功<br/>'.format(messages)
    request.session['is_wap'] = 'False'  # 是否进入地图凭证，防止直接进入游戏
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    request.session['sudu_start_time'] =int(time.time())
    request.session['sudu_cs'] =1
    user_id = request.session.get('user_id', 'None')
    if user_id == 'None':
        pass
        request.session['password'] = ''
    else:
        user = GameObject(User.objects.get(id=request.session['user_id']))
        request.session['password'] = user.password
        if request.GET.get('fuwuqi_xianlu'):
            user.set('fuwuqi_xianlu', request.GET.get('fuwuqi_xianlu'))
            user.save()
    游戏分区 = ''
    if GameAreaName.objects.all().count() == 0:
        游戏分区 = '暂无任何分区<br/>'
    else:
        area_names = GameAreaName.objects.all()
        for area_name in area_names:
            游戏分区 = '{}【{}区】<a href="/wap_choose/?area_id={}">{}</a><br/>'.format(游戏分区, area_name.area_id,
                                                                                      area_name.area_id,
                                                                                      area_name.area_name)
    neyong = str(c.html_wap_index)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'wap_index.html', locals())


def wap_3g_index(request):
    messages = ''
    if request.GET.get('username') and request.GET.get('password'):
        import pyodbc
        conn = pyodbc.connect(
            r'DRIVER={FreeTDS};SERVER=171.214.2.151;PORT=1433;DATABASE=3gqq.cn;UID=3gqq.cn;PWD=*********;TDS_Version=7.0')
        # conn = pyodbc.connect(r'DRIVER={FreeTDS};SERVER=171.214.2.151;PORT=1433;DATABASE=3gqq.cn;UID=3gqq.cn;PWD=*********;TDS_Version=7.0')
        cur = conn.cursor()
        username = request.GET.get('username')
        cur.execute("SELECT  pass FROM wap_user WHERE id={} ".format(username))
        password = list(cur)[0]
        conn.close()
        if request.GET.get('password') == password[0]:
            # 用户数据建立
            if User.objects.filter(name=request.GET.get('username')).count() > 0:
                user = User.objects.get(name=request.GET.get('username'))
                messages = '用户信息获取成功'
            else:
                user = User.objects.create(name=request.GET.get('username'), email='<EMAIL>')
                messages = '用户创建成功'
            request.session['password'] = request.GET.get('password')
            request.session['is_login'] = True
            request.session['user_id'] = user.id
            request.session['user_name'] = user.name
        else:
            request.session['user_id'] = ''
            request.session['is_login'] = ''
            request.session['password'] = ''
            return redirect("http://3gqq.cn/game/csyx1/")
            # return render(request, 'login.html', locals())
    else:
        request.session['user_id'] = ''
        request.session['is_login'] = ''
        request.session['password'] = ''
        return redirect("http://3gqq.cn/game/csyx1/")
        # return render(request, 'login.html', locals())
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login', 'None') == 'None':
        return redirect("http://3gqq.cn/game/csyx1/")
    messages = ''
    t = ImgValue()  # 代表图片
    if GameAreaName.objects.all().count() == 0:
        create = GameAreaName.objects.create(area_name='初入江湖',
                                             start_time='{}/{}/{} {}:{}'.format(date('年'), date('月'), date('日'),
                                                                                date('时'), date('分')),
                                             create_time='{}/{}/{}'.format(date('年'), date('月'), date('日')))
    if EventAll.objects.all().count() == 0:
        messages = '{}公共事件初始化成功<br/>'.format(messages)
        eventall = EventAll.objects.create(name='注册事件', area_name='player')
        eventall = EventAll.objects.create(name='登录事件', area_name='player')
        eventall = EventAll.objects.create(name='PK事件', area_name='player')
        eventall = EventAll.objects.create(name='战胜事件', area_name='player')
        eventall = EventAll.objects.create(name='战败事件', area_name='player')
        eventall = EventAll.objects.create(name='升级事件', area_name='player')
        eventall = EventAll.objects.create(name='分钟事件', area_name='player')
        eventall = EventAll.objects.create(name='商城购买事件', area_name='player')
        eventall = EventAll.objects.create(name='创建事件', area_name='map')
        eventall = EventAll.objects.create(name='查看事件', area_name='map')
        eventall = EventAll.objects.create(name='进入事件', area_name='map')
        eventall = EventAll.objects.create(name='离开事件', area_name='map')
        eventall = EventAll.objects.create(name='分钟事件', area_name='map')
        eventall = EventAll.objects.create(name='创建事件', area_name='item')
        eventall = EventAll.objects.create(name='查看事件', area_name='item')
        eventall = EventAll.objects.create(name='使用事件', area_name='item')
        eventall = EventAll.objects.create(name='分钟事件', area_name='item')
        eventall = EventAll.objects.create(name='穿上装备事件', area_name='item')
        eventall = EventAll.objects.create(name='卸下装备事件', area_name='item')
        eventall = EventAll.objects.create(name='创建事件', area_name='npc')
        eventall = EventAll.objects.create(name='查看事件', area_name='npc')
        eventall = EventAll.objects.create(name='战胜事件', area_name='npc')
        eventall = EventAll.objects.create(name='战败事件', area_name='npc')
        eventall = EventAll.objects.create(name='分钟事件', area_name='npc')
        eventall = EventAll.objects.create(name='创建事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='查看事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='战胜事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='战败事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='被收养事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='交易事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='升级事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='分钟事件', area_name='chongwu')
        eventall = EventAll.objects.create(name='分钟事件', area_name='xitong')
        eventall = EventAll.objects.create(name='伤害计算', area_name='pk')
        eventall = EventAll.objects.create(name='攻击执行', area_name='pk')
    if EventAll.objects.filter(name='镶入宝石事件', area_name='item').count() == 0:
        eventall = EventAll.objects.create(name='镶入宝石事件', area_name='item')
        eventall = EventAll.objects.create(name='卸下宝石事件', area_name='item')
    if EventAll.objects.filter(area_name='zuoqi').count() == 0:
        eventall = EventAll.objects.create(name='创建事件', area_name='zuoqi')
        eventall = EventAll.objects.create(name='查看事件', area_name='zuoqi')
    if EventAll.objects.filter(area_name='shibing').count() == 0:
        eventall = EventAll.objects.create(name='创建事件', area_name='shibing')
    if EventAll.objects.filter(name='前置事件', area_name='all').count() == 0:
        eventall = EventAll.objects.create(name='前置事件', area_name='all')
    if PageName.objects.all().count() == 0:
        objects = PageName.objects.create(page_name='场景')
        objects = PageName.objects.create(page_name='状态')
        objects = PageName.objects.create(page_name='背包')
        objects = PageName.objects.create(page_name='商城')
        objects = PageName.objects.create(page_name='战斗')
        objects = PageName.objects.create(page_name='聊天')
        objects = PageName.objects.create(page_name='组队')
        objects = PageName.objects.create(page_name='战斗结束')
        objects = PageName.objects.create(page_name='查看物品')
        objects = PageName.objects.create(page_name='查看玩家')
        objects = PageName.objects.create(page_name='查看装备')
        objects = PageName.objects.create(page_name='电脑人物')
        objects = PageName.objects.create(page_name='技能列表')
        objects = PageName.objects.create(page_name='查看技能')
        objects = PageName.objects.create(page_name='宠物列表')
        objects = PageName.objects.create(page_name='查看宠物')
        objects = PageName.objects.create(page_name='坐骑列表')
        objects = PageName.objects.create(page_name='查看坐骑')
        objects = PageName.objects.create(page_name='任务')
        objects = PageName.objects.create(page_name='任务列表')
        objects = PageName.objects.create(page_name='查看任务')
        messages = '{}页面模板初始化成功<br/>'.format(messages)
    if ItemAreaName.objects.all().count() == 0:
        area_name = ItemAreaName.objects.create(area_name='全部')
        area_name = ItemAreaName.objects.create(area_name='其它')
        area_name = ItemAreaName.objects.create(area_name='装备')
        area_name = ItemAreaName.objects.create(area_name='宝箱')
        area_name = ItemAreaName.objects.create(area_name='宝石')
        area_name = ItemAreaName.objects.create(area_name='书籍')
        messages = '{}物品分类初始化成功<br/>'.format(messages)
    if ItemType.objects.all().count() == 0:
        item_type = ItemType.objects.create(type='武器')
        item_type = ItemType.objects.create(type='副手')
        item_type = ItemType.objects.create(type='头盔')
        item_type = ItemType.objects.create(type='衣服')
        item_type = ItemType.objects.create(type='裤子')
        item_type = ItemType.objects.create(type='鞋子')
        messages = '{}装备类型初始化成功<br/>'.format(messages)
    if Attribute.objects.all().count() == 0:
        attribute = Attribute.objects.create(duixiang='u', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='u', built_in=1, name='心情', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=1, name='描述', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=0, name='是否PK', attribute='is_kill', type=2,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='map', built_in=0, name='刷新时间(秒)', attribute='flush_time',
                                             type=1,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=1, name='名称', attribute='name', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=1, name='描述', attribute='desc', type=3, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=0, name='等级', attribute='lvl', type=1, value=0)
        attribute = Attribute.objects.create(duixiang='npc', built_in=0, name='是否PK', attribute='is_kill', type=2,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='item', built_in=0, name='等级', attribute='lvl', type=1, value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='chongwu', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
        messages = '{}内置地图，NPC,宠物,技能属性初始化成功<br/>'.format(messages)
        attribute = Attribute.objects.create(duixiang='skill', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='skill', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='skill', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
    if Attribute.objects.filter(duixiang='zuoqi').count() == 0:
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=1, name='名称', attribute='name', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=1, name='描述', attribute='desc', type=3,
                                             value=0)
        attribute = Attribute.objects.create(duixiang='zuoqi', built_in=0, name='等级', attribute='lvl', type=1,
                                             value=0)
    if ChatAreaName.objects.all().count() == 0:
        chat_type = ChatAreaName.objects.create(area_name='全部')
        chat_type = ChatAreaName.objects.create(area_name='世界')
        chat_type = ChatAreaName.objects.create(area_name='系统')
        chat_type = ChatAreaName.objects.create(area_name='帮派')
        chat_type = ChatAreaName.objects.create(area_name='私聊')
        chat_type = ChatAreaName.objects.create(area_name='队伍')
        messages = '{}聊天分类初始化成功<br/>'.format(messages)
    request.session['is_wap'] = 'False'  # 是否进入地图凭证，防止直接进入游戏
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    user_id = request.session.get('user_id', 'None')
    if user_id == 'None':
        pass
    else:
        user = User.objects.get(id=request.session['user_id'])
    游戏分区 = ''
    if GameAreaName.objects.all().count() == 0:
        游戏分区 = '暂无任何分区<br/>'
    else:
        area_names = GameAreaName.objects.all()
        for area_name in area_names:
            游戏分区 = '{}【{}区】<a href="/wap_choose/?area_id={}">{}</a><br/>'.format(游戏分区, area_name.area_id,
                                                                                      area_name.area_id,
                                                                                      area_name.area_name)
    neyong = str(c.html_wap_index)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    return render(request, 'wap_3g_index.html', locals())


# 全局后台
def wap_base(request):
    t = ImgValue()  # 代表图片
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    # user = User.objects.get(id=request.session['user_id'])
    return render(request, 'wap_base.html', locals())


# 进入分区并创建ID
def wap_choose(request):
    encryption = {}
    request.session['wap_page_name'] = ''
    messages = ''
    t = ImgValue()  # 代表图片
    user = User.objects.get(id=request.session['user_id'])
    if request.session.get('user_id', 'None') == 'None' or request.session.get('is_login',
                                                                               'None') == 'None':
        request.session['user_id'] = 'None'
        request.session['is_login'] = 'None'
        return redirect("/login/")
    request.session['is_wap'] = 'False'  # 是否进入地图凭证，防止直接进入游戏
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    if GameMap.objects.filter(id=c.map_id).count() == 0:
        c.set('map_id', 0)
        maps = 0
    else:
        maps = GameMap.objects.get(id=c.map_id)

    if User.objects.filter(name=user_name()).count() == 0:
        new_user = User.objects.create()

        new_user.name = user_name()

        new_user.password = user_password()

        new_user.is_designer = 'True'

        new_user.sex = '男'

        new_user.save()

    userss = User.objects.get(name=user_name())

    userss.password = user_password()

    userss.set('is_designer', 'True')

    userss.save()

    # 游戏拥有者
    if c.is_designer_id == 0 or request.session['user_id'] == '1' or request.session['user_id'] == 1:
        c.set('is_designer_id', 1)
        user = User.objects.get(id=1)
        user.is_designer = 'True'
        user.save()

    # 获得帐号信息

    # 检测当前游戏ID

    # 当此帐号还没有游戏ID时，创建一个新用户ID

    if request.GET.get('area_id'):
        if GameAreaName.objects.filter(area_id=request.GET.get('area_id')).count() > 0:

            area_name = GameAreaName.objects.get(area_id=request.GET.get('area_id'))

            s_time = time.mktime(time.strptime('{}'.format(area_name.start_time), '%Y/%m/%d %H:%M'))
            e_time = '{}/{}/{} {}:{}'.format(date('年'), date('月'), date('日'), date('时'), date('分'))
            e_time = time.strptime('{}'.format(e_time), '%Y/%m/%d %H:%M')
            e_time = time.mktime(e_time)
            if Player.objects.filter(user_id=request.session['user_id'],area_id=request.GET.get('area_id')).count() > 0:
                players = Player.objects.filter(user_id=request.session['user_id'], area_id=request.GET.get('area_id'))
                for i in players:
                    u = i
                    break
                is_neice = u.is_neice
                user = User.objects.get(id=u.user_id)
                is_designer = user.is_designer
            else:
                is_neice = 'False'
                is_designer = 'False'
                # 创建一个新用户 理论是不创建
                area_id = request.GET.get('area_id')
                user_id = request.session['user_id']
                if Player.objects.filter(user_id=user_id, area_id=area_id).count() == 0:
                    players = Player.objects.create()
                    players.user_id = user_id
                    players.area_id = area_id
                    players.save()
            if int(s_time) < int(e_time) or is_neice == 'True' and area_name.is_neice == 'True' or is_designer == 'True':
                request.session['area_id'] = request.GET.get('area_id')
            else:
                u = Player.objects.get(user_id=request.session['user_id'], area_id=request.GET.get('area_id'))
                messages = '正式开区时间：{}[本区ID:{}]'.format(area_name.start_time, u.id)

                user_id = request.session.get('user_id', 'None')
                if user_id == 'None':
                    pass
                else:
                    user = User.objects.get(id=request.session['user_id'])
                游戏分区 = ''
                if GameAreaName.objects.all().count() == 0:
                    游戏分区 = '暂无任何分区<br/>'
                else:
                    area_names = GameAreaName.objects.all()
                    for area_name in area_names:
                        游戏分区 = '{}<a href="/wap_choose/?area_id={}">{}</a><br/>'.format(游戏分区, area_name.area_id,
                                                                                            area_name.area_name)
                neyong = str(c.html_wap_index)
                var = re.findall(r"{{(.*?)}}", neyong)
                bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
                neyong = bbb.format(*map(eval, var))
                return render(request, 'wap_index.html', locals())
        else:
            messages = '没有这个分区'
            user_id = request.session.get('user_id', 'None')
            if user_id == 'None':
                pass
            else:
                user = User.objects.get(id=request.session['user_id'])
            游戏分区 = ''
            if GameAreaName.objects.all().count() == 0:
                游戏分区 = '暂无任何分区<br/>'
            else:
                area_names = GameAreaName.objects.all()
                for area_name in area_names:
                    游戏分区 = '{}<a href="/wap_choose/?area_id={}">{}</a><br/>'.format(游戏分区, area_name.area_id,
                                                                                        area_name.area_name)
            neyong = str(c.html_wap_index)
            var = re.findall(r"{{(.*?)}}", neyong)
            bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
            neyong = bbb.format(*map(eval, var))
            return render(request, 'wap_index.html', locals())
    area_id = request.session['area_id']
    user_id = request.session['user_id']
    if Player.objects.filter(user_id=user_id, area_id=area_id).count() == 0:
        players = Player.objects.create()
        players.user_id = user_id
        players.area_id = area_id
        players.save()
    players = Player.objects.filter(user_id=user_id, area_id=area_id)
    进入游戏 = ''
    request.session['pmwap_session_password'] = rr(1000, 10000)
    bh = 0
    for u in players:
        bh = bh + 1
        u = GameObject(u)
        u.params = str(u.params)
        u.set('is_designer', 'False')
        u.set('is_ajax', 0)
        u.set('session_password', request.session['pmwap_session_password'])
        request.session['player_id'] = u.id
        if u.time + 1800 < time.time():
            if UtPlayer.objects.filter(player_id=u.id).count() == 0:
                ut = UtPlayer.objects.create(player_id=u.id)
            else:
                ut = UtPlayer.objects.get(player_id=u.id)
                ut.params = '{}'
                ut.save()
        u.set('is_guaji', 0)
        u.set('time', time.time())
        map_id = 0 if not u.map_id else u.map_id
        if not u.map_id or GameMap.objects.filter(id=u.map_id).count() == 0:
            u.set('map_id', c.map_id)
        user = User.objects.get(id=u.user_id)
        u.set('is_designer', user.is_designer)
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            ipaddress = request.META['HTTP_X_FORWARDED_FOR']
        else:
            ipaddress = request.META['REMOTE_ADDR']
        u.set('ip', ipaddress)
        u.set('fuwuqi_xianlu', user.fuwuqi_xianlu)
        u.save()
        if c.map_id == 0:
            进入游戏 = '{}进入游戏(暂未设置入口)<br/>'.format(进入游戏)
        elif int(c.is_state == 0) and user.is_designer != 'True':
            进入游戏 = '{}游戏开发中<br/>'.format(进入游戏)
        else:
            if u.lvl <= 0:
                parameter = parameter_create(encryption, value=c.map_id)
                player_id = parameter_create(encryption, value=u.id)
                进入游戏 = '{}<a href = "/wap/?map_get_into_see={}&player_id={}" >进入游戏</a><br/>'.format(进入游戏, parameter,player_id)
            elif GameMap.objects.filter(id=u.map_id).count() == 0:
                parameter = parameter_create(encryption, value=c.map_id)
                u.set('map_id', c.map_id, True)
                player_id = parameter_create(encryption, value=u.id)
                进入游戏 = '{}{}.<a href = "/wap/?map_get_into_see={}&player_id={}" >{}[ID:{}]({}级)</a><br/>'.format(进入游戏, bh,parameter,player_id, u.name,u.id,u.lvl)
            else:
                parameter = parameter_create(encryption, value=u.map_id)
                player_id = parameter_create(encryption, value=u.id)
                进入游戏 = '{}{}.<a href = "/wap/?map_get_into_see={}&player_id={}" >{}[ID:{}]({}级)</a><br/>'.format(进入游戏, bh,parameter,player_id, u.name,u.id,u.lvl)
    request.session['pmwap_page_name'] = '场景'
    request.session['object_name'] = 0
    request.session['object_id'] = 0
    request.session['wap_xs'] = 0
    request.session['page_name'] = 0
    request.session['area_name'] = 0
    request.session['delete1'] = 0
    request.session['delete_eventlist'] = 0
    request.session['equip_area_name'] = 0
    request.session['duixiang'] = 0
    request.session['equip_bh'] = 0
    request.session['equip_page'] = 0
    request.session['use_up_page'] = 0
    request.session['pets_up_page'] = 0
    request.session['page_name'] = 0
    request.session['ts_f_id'] = 0
    request.session['ts_z_id'] = 0
    request.session['up_page_name'] = 0
    request.session['npc_id'] =0
   # request.session['player_id'] = u.id
    if int(user.id) == int(c.is_designer_id):
        user.set('is_designer', 'True', True)
    ###########批量替换某个字符#############
    # if eval(user.is_designer) and c.dkjkjk == 4445:
    #    areas = Operation.objects.all()
    #    for i in areas:
    #        i.code = i.code.replace("move_map(u,", "move_map(u,request,")
    #        i.save()
    #    areas = Event.objects.all()
    #    for i in areas:
    #        i.code = i.code.replace("move_map(u,", "move_map(u,request,")
    #        i.save()
    if user.is_designer == 'True' or c.is_designer_id == request.session['user_id']:
        设计游戏 = '<a href="/wap_admin/">设计游戏</a><br/>'
    else:
        设计游戏 = ''
    request.session['encryption'] = encryption
    request.session['is_wap'] = 'True'
    c.save()
    if PublicAttributeNew.objects.filter(id=request.session['area_id']).count() == 0:
        PublicAttributeNew.objects.create(id=request.session['area_id'])
    neyong = str(c.html_wap_choose)
    var = re.findall(r"{{(.*?)}}", neyong)
    bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
    neyong = bbb.format(*map(eval, var))
    if Suo.objects.filter(player_id=u.id).count() == 0:
        suo = Suo.objects.create(player_id=u.id, update=0)
    Suo.objects.filter(player_id=u.id).update(update=0)
    return render(request, 'wap_choose.html', locals())


# --------------------设计大厅----------------------------
# 游戏C属性
def wap_attribute_basic(request):
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('map_iid'):
            c.set('map_id', request.GET.get('map_iid'))
            messages = '选择场景入口成功'
        elif request.GET.get('skill_iid'):
            c.set('skill_id', request.GET.get('skill_iid'))
            messages = '选择初始技能成功'
    # 是否存在初始地图入口
    if GameMap.objects.filter(id=c.map_id).count() == 0:
        c.set('map_id', 0)
        maps = 0
    else:
        maps = GameMap.objects.get(id=c.map_id)
    # 是否存在初始技能
    if Skill.objects.filter(id=c.skill_id).count() == 0:
        c.set('skill_id', 0)
        skills = 0
    else:
        skills = Skill.objects.get(id=c.skill_id)
    if user.is_designer == 'True':
        if request.method == "POST":
            messages = '保存成功'
            c.set('html_messages', request.POST['html_messages'])  # 提示语
            c.set('html_tishi_messages', request.POST['html_tishi_messages'])  # 提示语
            c.set('is_state', request.POST['is_state'])  # 游戏状态
            c.set('is_backstage', request.POST['is_backstage'])  # 是否禁止非设计进后台
            c.set('name', request.POST['name'])
            c.set('desc', request.POST['desc'])
            c.set('exp_expression', request.POST['exp_expression'])
            c.set('lingqi_expression', request.POST['lingqi_expression'])
            c.set('money_expression', request.POST['money_expression'])
            c.set('money_name', request.POST['money_name'])
            c.set('liangcao_expression', request.POST['liangcao_expression'])
            c.set('mucai_expression', request.POST['mucai_expression'])
            c.set('shengtie_expression', request.POST['shengtie_expression'])
            c.set('shiliao_expression', request.POST['shiliao_expression'])
            c.set('css', request.POST['css'])
            c.set('js', request.POST['js'])
            c.set('info_message', request.POST['info_message'])
            c.set('team_max_count', request.POST['team_max_count'])
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    c.save()
    css_count = Operation.objects.filter(page_name='全局CSS').count()
    script_count = Operation.objects.filter(page_name='全局script').count()
    return render(request, 'wap_attribute_basic.html', locals())


# 游戏特殊页面
def wap_page_special(request):
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            messages = '保存成功'
            c.set('html_index_1', request.POST['html_index_1'])
            c.set('html_index_2', request.POST['html_index_2'])
            c.set('html_login_register', request.POST['html_login_register'])
            c.set('html_login', request.POST['html_login'])
            c.set('html_register', request.POST['html_register'])
            c.set('html_set_password', request.POST['html_set_password'])
            c.set('html_change_password', request.POST['html_change_password'])
            c.set('html_change_security', request.POST['html_change_security'])
            c.set('html_wap_index', request.POST['html_wap_index'])
            c.set('html_wap_choose', request.POST['html_wap_choose'])
            c.set('css_index_1', request.POST['css_index_1'])
            c.set('css_index_2', request.POST['css_index_2'])
            c.set('css_login_register', request.POST['css_login_register'])
            c.set('css_login', request.POST['css_login'])
            c.set('css_register', request.POST['css_register'])
            c.set('css_set_password ', request.POST['css_set_password'])
            c.set('css_change_password', request.POST['css_change_password'])
            c.set('css_change_security', request.POST['css_change_security'])
            c.set('css_wap_index', request.POST['css_wap_index'])
            c.set('css_wap_choose', request.POST['css_wap_choose'])
            c.set('js_index_1', request.POST['js_index_1'])
            c.set('js_index_2', request.POST['js_index_2'])
            c.set('js_login_register', request.POST['js_login_register'])
            c.set('js_login', request.POST['js_login'])
            c.set('js_register', request.POST['js_register'])
            c.set('js_set_password', request.POST['js_set_password'])
            c.set('js_change_password', request.POST['js_change_password'])
            c.set('js_change_security', request.POST['js_change_security'])
            c.set('js_wap_index', request.POST['js_wap_index'])
            c.set('js_wap_choose', request.POST['js_wap_choose'])
            c.save()
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_page_special.html', locals())


# 设计者列表
def wap_is_designer(request):
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    userss = User.objects.get(name=user_name())
    if request.GET.get('delete_id'):
        users = User.objects.get(id=request.GET.get('delete_id'))
        if int(users.id) == 1:
            messages = '首席设计无法删除'
        else:
            users.is_designer = 'False'
            users.save()
            messages = '删除设计成功'
    if request.GET.get('delete_user_id'):
        user = User.objects.get(id=request.GET.get('delete_user_id'))
        user.forum_designer = 'False'
        user.save()
        messages = '删除版主成功'
    if request.method == "GET":
        if request.GET.get('forum_designer'):
            if User.objects.filter(name=request.GET.get('forum_designer')).count() > 0:
                user = User.objects.get(name=request.GET.get('forum_designer'))
                user.forum_designer = 'True'
                user.save()
                messages = '添加版主成功'
            else:
                messages = '没有这个登陆账号'
        else:
            pass
        if request.GET.get('is_designer'):
            if User.objects.filter(name=request.GET.get('is_designer')).count() > 0:
                user = User.objects.get(name=request.GET.get('is_designer'))
                user.is_designer = 'True'
                user.save()
                messages = '添加设计成功'
            else:
                messages = '没有这个登陆账号'
        else:
            pass
    if request.method == "POST" and int(userss.id) == int(request.session['user_id']):
        if User.objects.filter(name=request.POST['username']).count() > 0:
            if request.POST['password']:
                user_change = User.objects.get(name=request.POST['username'])
                user_change.name = request.POST['username']
                user_change.password = hash_code(request.POST['password'])
                user_change.save()
                messages = '修改密码成功'
            else:
                messages = '请输入密码'
        else:
            messages = '没有这个账号'

    user = User.objects.get(id=request.session['user_id'])
    users = User.objects.filter(is_designer='True')
    neyong = ''
    bh = 0
    for i in users:
        if int(userss.id) != int(i.id) and int(c.is_designer_id) != int(i.id):
            bh = bh + 1
            neyong = '{}{}.{}(ID:{})'.format(neyong, bh, i.name, i.id)
            if int(userss.id) == int(user.id) or int(user.id) == 1:
                neyong = '{} <a href="/wap_is_designer/?delete_id={}" > 删除 </a>'.format(neyong, i.id)
            neyong = '{}<br/>'.format(neyong)
    if neyong == '':
        neyong = '暂无任何设计<br/>'
    users = User.objects.filter(forum_designer='True')
    forum_neyong = ''
    if users.exists():
        bh = 0
        for i in users:
            bh = bh + 1
            forum_neyong = '{}{}.{}'.format(forum_neyong, bh, i.name)
            if int(userss.id) == int(user.id) or int(user.id) == 1:
                forum_neyong = '{} <a href="/wap_is_designer/?delete_user_id={}" > 删除 </a>'.format(forum_neyong, i.id)
            forum_neyong = '{}<br/>'.format(forum_neyong)
    else:
        forum_neyong = '{}暂无版主<br/>'.format(forum_neyong)
    return render(request, 'wap_is_designer.html', locals())


# 玩家列表模板
def wap_player(request):
    encryption = {}
    if GameAttributeNew.objects.filter(id=1).count() > 0:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    else:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    user = User.objects.get(id=request.session['user_id'])
    if User.objects.filter(name=user_name()).count() == 0:
        userss = 0
    else:
        userss = User.objects.get(name=user_name())
    if user.is_designer == 'True':
        if request.GET.get('delete'):
            delete_id = request.GET.get('iid')  # 填写对方ID即可删除
            if Player.objects.filter(id=delete_id).count() > 0:
                # 删除宠物
                if Pets.objects.filter(player_id=delete_id).count() > 0:
                    deletes = Pets.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除技能快捷键
                if QuickSkill.objects.filter(player_id=delete_id).count() > 0:
                    deletes = QuickSkill.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除技能
                if Skills.objects.filter(player_id=delete_id).count() > 0:
                    deletes = Skills.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除装备中的物品
                if UseEquip.objects.filter(player_id=delete_id).count() > 0:
                    deletes = UseEquip.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除背包物品
                if ItemPlayer.objects.filter(player_id=delete_id).count() > 0:
                    deletes = ItemPlayer.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除玩家任务
                if TaskPlayer.objects.filter(player_id=delete_id).count() > 0:
                    deletes = TaskPlayer.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除玩家杀怪和寻物
                if TaskItemPlayer.objects.filter(player_id=delete_id).count() > 0:
                    deletes = TaskItemPlayer.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除玩家组队
                if Team.objects.filter(player_id=delete_id).count() > 0:
                    deletes = Team.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除私聊信息
                if ChatMessage.objects.filter(player_id=delete_id).count() > 0:
                    deletes = ChatMessage.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除组队聊天信息
                if ChatMessage.objects.filter(team_id=delete_id).count() > 0:
                    deletes = ChatMessage.objects.filter(team_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除排行榜信息
                if Ranking.objects.filter(player_id=delete_id).count() > 0:
                    deletes = Ranking.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                # 删除交易行信息
                if Auction.objects.filter(player_id=delete_id).count() > 0:
                    deletes = Auction.objects.filter(player_id=delete_id)
                    for delete in deletes:
                        delete.delete()
                player = Player.objects.get(id=delete_id)
                player.params = '{}'
                player.map_id = ''
                player.name = ''
                player.desc = ''
                player.team_id = ''
                player.message = ''
                player.save()
                if request.GET.get('delete') == '1':
                    messages = '清空玩家数据成功'
                elif request.GET.get('delete') == '2':
                    # 删除玩家ID
                    messages = '删除玩家:{}成功'.format(player.name)
                    player.delete()
                else:
                    pass
            else:
                messages = '没有这个玩家ID'
        if request.GET.get('is_neice'):
            is_neice = request.GET.get('iid')  # 填写对方ID即可删除
            if Player.objects.filter(id=is_neice).count() > 0:
                player = Player.objects.get(id=is_neice)
                if request.GET.get('is_neice') == '1':
                    player.set('is_neice', 'False')
                    player.save()
                    messages = '取消内测成功'
                elif request.GET.get('is_neice') == '2':
                    messages = '添加玩家:{}为内测成功'.format(player.name)
                    player.set('is_neice', 'True')
                    player.save()
                else:
                    pass
            else:
                messages = '没有这个玩家ID'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.GET.get('page_yeshu'):
        request.session['page_yeshu'] = request.GET.get('page_yeshu')
    player_list = """
    <table style="border:2px;width:100%;font-size:12px;max_width:400px">
    <tr><td style="width:20%;">角色名</td><td style="width:10%;">ID</td><td style="width:20%;">归属帐号</td><td style="width:10%;">等级</td><td style="width:10%;">分区</td><td style="width:10%;">内测</td><td style="width:15%;">功能</td><td style="width:15%"></td></tr>
    """
    page_ys = int(request.session['page_yeshu'])
    players_count = Player.objects.all().count()  # 所在地圖玩家數量
    start_count = page_ys * 10
    end_count = page_ys * 10 + 10  # 顯示玩家數
    players = Player.objects.all()[start_count:end_count]
    u = GameObject(Player.objects.get(id=request.session['player_id']))
    for player in players:
        if Player.objects.filter(id=player.id).count() == 0 or User.objects.filter(id=player.user_id).count() == 0:
            player.delete()
        else:
            try:
                start_count = start_count + 1
                player = GameObject(player)
                users = User.objects.get(id=player.user_id)
                player_list = '{}<tr><td><a style="font-size:13px;">{}.{}</a><td>{}</td><td>{}</td></td><td> {}級 </td><td> {}区 </td><td>{}</td>'.format(
                    player_list, start_count, player.name,
                    player.id, users.name, player.lvl, player.area_id, player.is_neice)
                player_list = """
                   {} <td><div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan{}">
                <div class="nav1">管理</div>
                <div class="nav2">
                        """.format(player_list, start_count)
                parameter = parameter_create(encryption, value=player.id)
                parameter_page_name = parameter_create(encryption, value='查看玩家')
                request.session['is_wap'] = 'True'  # 是否进入地图凭证，防止直接进入游戏
                player_list = """
                   {}<a  href="/wap/?check_player_id={}&page_name={}" > 查看玩家 </a>
                    """.format(player_list, parameter, parameter_page_name)
                if player.is_neice == 'True':
                    player_list = """
                       {}<a href="/wap_player/?is_neice=1&iid={}" class="function_button">取消内测</a>
                        """.format(player_list, player.id)
                else:
                    player_list = """
                       {}<a href="/wap_player/?is_neice=2&iid={}" class="function_button">设为内测</a>
                        """.format(player_list, player.id)
                player_list = """
                   {}
                   <a  class="function_button">-</a>
                   <a href="/wap_player/?delete=1&iid={}" class="function_button">清空数据</a>
                   <a href="/wap_player/?delete=2&iid={}" class="function_button">删除ID</a>
                   </div>
                   </div></td><td></td></tr>
                   """.format(player_list, player.id, player.id)
            except AttributeError:
                player.delete()
    player_list = '{}</table>'.format(player_list)
    player_list = '{}---<br/>'.format(player_list)
    if players_count > page_ys * 10 + 10:
        player_list = '{}<a href="/wap_player/?page_yeshu={}">下一页</a>'.format(player_list, page_ys + 1)
    if players_count > page_ys * 10 + 10 and page_ys > 0:
        player_list = '{} | '.format(player_list)
    if page_ys > 0:
        player_list = '{}<a href="/wap_player/?page_yeshu={}">上一页</a>'.format(player_list, page_ys - 1)
    if players_count > page_ys * 10 + 10 or page_ys > 0:
        player_list = '{}<br/>---<br/>'.format(player_list)

    return render(request, 'wap_player.html', locals())


# 游戏分区
def wap_game_area_name(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "GET":
            if request.GET.get('delete_area_id'):
                if GameAreaName.objects.filter(area_id=request.GET.get('delete_area_id')).count() > 0:
                    area_names = GameAreaName.objects.get(area_id=request.GET.get('delete_area_id'))
                    messages = '删除:[{}区]{} 成功'.format(area_names.area_id, area_names.area_name)
                    area_names.delete()
                else:
                    messages = '请选择要删除的分区'
    else:
        messages = '你还不是设计'
    area_names = GameAreaName.objects.all()
    neyong = ''
    if area_names.exists():
        neyong = """
        {}
        <table style="font-size:12px;border:1px solid black;width:100%;padding:10px 10px 10px;">
        <tr>
            <td style="border:1px solid black;text-align:center;">ID</td>
            <td style="border:1px solid black;text-align:center;">分区名称</td>
            <td style="border:1px solid black;text-align:center;">开区时间</td>
            <td style="border:1px solid black;text-align:center;">创建时间</td>
            <td style="border:1px solid black;text-align:center;">内测</td>
            <td style="border:1px solid black;text-align:center;">操作</td>
        </tr>
        """.format(neyong)
        for area_name in area_names:
            neyong = """
            {}
            <tr>
            <td>{}区</td>
            <td>{}</td>
            <td>{}</td>
            <td>{}</td>
            <td>{}</td>
            <td><a href="/wap_game_area_name_change/?area_id={}"">修改</a></td>
            </tr>
            """.format(neyong, area_name.area_id, area_name.area_name, area_name.start_time, area_name.create_time,
                       area_name.is_neice, area_name.area_id)
        neyong = """
        {}
        <tr>
        <td colspan="6" style="color:#ffffff;">.</td>
        </tr>
        <tr>
        <td colspan="6" style="color:#ffffff;">.</td>
        </tr>
        <tr>
        <td colspan="6" style="border:1px solid black;text-align:center;width:100%;background-color:  #d0d0d0 ;">
        <form action="/wap_game_area_name/" method="GET">
        <a style="color: #F75000 ;">【删区】</a>:<select name="delete_area_id">
                <option value = 0 selected="selected">选择删除分区</option>
        """.format(neyong)

        for area_name in area_names:
            neyong = """
           {}
            <option value = {} >[{}区]{}</option>
            """.format(neyong, area_name.area_id, area_name.area_id, area_name.area_name)

        neyong = """
        {}
        </select>
        <input name="submit" type="submit" title="确定删除" value="确定删除"/><br/>
        </form>
        </td>
        </tr>
        </table>
        """.format(neyong)
    else:
        neyong = '暂无游戏分区<br/>'
    return render(request, 'wap_game_area_name.html', locals())


# 游戏分区
def wap_game_area_name_change(request):
    messages = ''
    if request.GET.get('area_id'):
        request.session['object_z_bh'] = request.GET.get('area_id')
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['area_name']:
                if request.POST['start_time']:
                    if request.session['object_z_bh'] == '0':
                        area_name = GameAreaName.objects.create()
                        area_name.area_name = request.POST['area_name']
                        area_name.start_time = '{}'.format(request.POST['start_time'])
                        area_name.create_time = '{}/{}/{}'.format(date('年'), date('月'), date('日'))
                        area_name.is_neice = 'False' if not request.POST.get('is_neice') else 'True'
                        area_name.save()
                        messages = '<font color="red">创建分区成功</font>'
                    else:
                        area_name = GameAreaName.objects.get(area_id=request.session['object_z_bh'])
                        area_name.area_name = request.POST['area_name']
                        area_name.start_time = '{}'.format(request.POST['start_time'])
                        area_name.is_neice = 'False' if not request.POST.get('is_neice') else 'True'
                        area_name.save()
                        messages = '<font color="red">修改分区成功</font>'
                    messages = ''
                    c = GameObject(GameAttributeNew.objects.get(id=1))
                    user = GameObject(User.objects.get(id=request.session['user_id']))
                    area_names = GameAreaName.objects.all()
                    neyong = ''
                    if area_names.exists():
                        neyong = """
                        {}
                        <table style="font-size:12px;border:1px solid black;width:100%;padding:10px 10px 10px;">
                        <tr>
                            <td style="border:1px solid black;text-align:center;">ID</td>
                            <td style="border:1px solid black;text-align:center;">分区名称</td>
                            <td style="border:1px solid black;text-align:center;">开区时间</td>
                            <td style="border:1px solid black;text-align:center;">创建时间</td>
                            <td style="border:1px solid black;text-align:center;">内测</td>
                            <td style="border:1px solid black;text-align:center;">操作</td>
                        </tr>
                        """.format(neyong)
                        for area_name in area_names:
                            neyong = """
                            {}
                            <tr>
                            <td>{}区</td>
                            <td>{}</td>
                            <td>{}</td>
                            <td>{}</td>
                            <td>{}</td>
                            <td><a href="/wap_game_area_name_change/?area_id={}"">修改</a></td>
                            </tr>
                            """.format(neyong, area_name.area_id, area_name.area_name, area_name.start_time,
                                       area_name.create_time, area_name.is_neice, area_name.area_id)
                        neyong = """
                        {}
                        <tr>
                        <td colspan="6" style="color:#ffffff;">.</td>
                        </tr>
                        <tr>
                        <td colspan="6" style="color:#ffffff;">.</td>
                        </tr>
                        <tr>
                        <td colspan="6" style="border:1px solid black;text-align:center;width:100%;background-color:  #d0d0d0 ;">
                        <form action="/wap_game_area_name/" method="GET">
                        <a style="color: #F75000 ;">【删区】</a>:<select name="delete_area_id">
                                <option value = 0 selected="selected">选择删除分区</option>
                        """.format(neyong)

                        for area_name in area_names:
                            neyong = """
                           {}
                            <option value = {} >[{}区]{}</option>
                            """.format(neyong, area_name.area_id, area_name.area_id, area_name.area_name)

                        neyong = """
                        {}
                        </select>
                        <input name="submit" type="submit" title="确定删除" value="确定删除"/><br/>
                        </form>
                        </td>
                        </tr>
                        </table>
                        """.format(neyong)
                    else:
                        neyong = '暂无游戏分区<br/>'
                    return render(request, 'wap_game_area_name.html', locals())
                else:
                    messages = '<font color="red">请输入开区时间</font>'
            else:
                messages = '<font color="red">请输入分区名称</font>'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_z_bh'] != '0':
        objects = GameAreaName.objects.get(area_id=request.session['object_z_bh'])
    else:
        objects = ''
    return render(request, 'wap_game_area_name_change.html', locals())


# 游戏图片
def wap_img(request):
    return render(request, 'wap_img.html', locals())


# 游戏图片分类
def wap_img_area_name(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "GET":
            if request.GET.get('create_area_name'):
                if GameImgAreaName.objects.filter(area_name=request.GET.get('create_area_name')).count() == 0:
                    area_names = GameImgAreaName.objects.create(area_name=request.GET.get('create_area_name'))
                    messages = '创建图片分类:{}成功'.format(request.GET.get('create_area_name'))
                else:
                    messages = '分类名称重复'
    else:
        messages = '你还不是设计'
    area_names = GameImgAreaName.objects.all()
    neyong = ''
    if area_names.exists():
        bh = 0
        for area_name in area_names:
            bh = bh + 1
            neyong = '{}{}.<a href="/wap_img_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong, bh,
                                                                                                   area_name.area_name,
                                                                                                   area_name.area_name,
                                                                                                   GameImg.objects.filter(
                                                                                                       area_name=area_name.area_name).count())
    else:
        neyong = '暂无图片分类<br/>'

    return render(request, 'wap_img_area_name.html', locals())


# 游戏图片分类
def wap_tupian_area_name(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "GET":
            if request.GET.get('create_area_name'):
                if ImgAreaName.objects.filter(area_name=request.GET.get('create_area_name')).count() == 0:
                    area_names = ImgAreaName.objects.create(area_name=request.GET.get('create_area_name'))
                    messages = '创建图片分类:{}成功'.format(request.GET.get('create_area_name'))
                else:
                    messages = '分类名称重复'
    else:
        messages = '你还不是设计'
    area_names = ImgAreaName.objects.all()
    neyong = ''
    if area_names.exists():
        bh = 0
        for area_name in area_names:
            bh = bh + 1
            neyong = '{}{}.<a href="/wap_tupian_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong, bh,
                                                                                                      area_name.area_name,
                                                                                                      area_name.area_name,
                                                                                                      Img.objects.filter(
                                                                                                          area_name=area_name.area_name).count())
    else:
        neyong = '暂无图片分类<br/>'
    return render(request, 'wap_tupian_area_name.html', locals())


# 游戏图片列表
def wap_img_area_name_list(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('area_name'):
        request.session['area_name'] = request.GET.get('area_name')
    if user.is_designer == 'True':
        if request.method == "GET":
            if request.GET.get('create_value'):
                if request.GET.get('create_attribute'):
                    if GameImg.objects.filter(area_name=request.session['area_name'],
                                              attribute=request.GET.get('create_attribute')).count() == 0:
                        area_names = GameImg.objects.create(area_name=request.session['area_name'],
                                                            name=request.GET.get('create_name'),
                                                            attribute=request.GET.get('create_attribute'),
                                                            value=request.GET.get('create_value'),
                                                            code=request.GET.get('create_code'))
                        messages = '添加图片成功'
                    else:
                        messages = '此标示已被使用，请重新使用标示'
                else:
                    messages = '标识不能为空'
        if request.method == "POST":
            if GameImgAreaName.objects.filter(area_name=request.POST['create_area_name']).count() == 0:
                area_names = GameImgAreaName.objects.get(area_name=request.session['area_name'])
                area_names.area_name = request.POST['create_area_name']
                area_names.save()
                messages = '修改图片分类:{}成功'.format(request.POST['create_area_name'])
                area_names = GameImg.objects.filter(area_name=request.session['area_name'])
                request.session['area_name'] = request.POST['create_area_name']
                for area_name in area_names:
                    area_name.area_name = request.POST['create_area_name']
                    area_name.save()
            else:
                messages = '分类名称重复'
    else:
        messages = '你还不是设计'
    area_names = GameImg.objects.filter(area_name=request.session['area_name'])
    neyong = ''
    if area_names.exists():
        bh = 0
        neyong = """
        {}
        <table border="1" cellspacing="0" cellpadding="0" width="100%" border-color="#b6ff00">
        <tr><td colspan="4" style="text-align:center;"><font color="orange" size="6px"><b>{}</b></font></td></tr>
        <tr>
        <td style="text-align:center;width:10%">序号</td>
        <td style="text-align:center;width:15%">标识</td>
        <td style="text-align:center;width:15%">名称</td>
        <td style="text-align:center;width:60%">图片</td>
        </tr>
        """.format(neyong, request.session['area_name'])
        for area_name in area_names:
            bh = bh + 1
            neyong = """
            {}
            <tr>
            <td style="text-align:center;">{}</td>
            <td style="text-align:center;"><a href="/wap_img_area_name_list_change/?iid={}">{}</a></td>
            <td>{}</td>
            <td> <img {} src="{}" /></td>
            </tr>
            """.format(neyong, bh, area_name.id, area_name.attribute, area_name.name, area_name.code, area_name.value)
        neyong = """
        {}
        </table>
        """.format(neyong)
    else:
        neyong = '暂无图片<br/>'
    return render(request, 'wap_img_area_name_list.html', locals())


# 游戏本地图片列表
def wap_tupian_area_name_list(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('delete'):
        delete = Img.objects.get(id=request.GET.get('iid'))
        fname = "." + str(delete.value)
        import os
        if os.path.isfile(fname):
            os.remove(fname)
        delete.delete()
        messages = '删除图片成功'
    if request.GET.get('area_name'):
        request.session['area_name'] = request.GET.get('area_name')
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.FILES.get('create_value'):
                if request.POST['create_attribute']:
                    if Img.objects.filter(area_name=request.session['area_name'],
                                          attribute=request.POST['create_attribute']).count() == 0:
                        file_obj = request.FILES.get('create_value')
                        file_name = './static/img/' + request.POST['create_attribute'] + '.' + \
                                    file_obj.name.split('.')[-1]  # 构造文件名以及文件路径
                        if file_obj.name.split('.')[-1] in ['a']:
                            messages = '输入文件有误'
                        else:
                            try:
                                area_names = Img.objects.create(area_name=request.session['area_name'],
                                                                name=request.POST['create_name'],
                                                                attribute=request.POST['create_attribute'],
                                                                value=file_name[1:],
                                                                code=request.POST['create_code'])
                                with open(file_name, 'wb+') as f:
                                    f.write(file_obj.read())
                            # open(file_name, 'wb').write(files.file.read())
                            except Exception as e:
                                pass
                            messages = '添加图片成功'
                    # imgfile.size 做文件上传大小的限制
                    # imgfile.content_type 做文件上传类型限制
                    else:
                        messages = '此标示已被使用，请重新使用标示'
                else:
                    messages = '标识不能为空'
        if request.method == "GET":
            if request.GET.get('create_area_name'):
                if ImgAreaName.objects.filter(area_name=request.GET.get('create_area_name')).count() == 0:
                    area_names = ImgAreaName.objects.get(area_name=request.session['area_name'])
                    area_names.area_name = request.GET.get('create_area_name')
                    area_names.save()
                    messages = '修改图片分类:{}成功'.format(request.GET.get('create_area_name'))
                    area_names = Img.objects.filter(area_name=request.session['area_name'])
                    request.session['area_name'] = request.GET.get('create_area_name')
                    for area_name in area_names:
                        area_name.area_name = request.GET.get('create_area_name')
                        area_name.save()
                else:
                    messages = '分类名称重复'
    else:
        messages = '你还不是设计'
    area_names = Img.objects.filter(area_name=request.session['area_name'])
    neyong = ''
    if area_names.exists():
        bh = 0
        neyong = """
        {}
        <table border="1" cellspacing="0" cellpadding="0" width="100%" border-color="#b6ff00">
        <tr><td colspan="4" style="text-align:center;"><font color="orange" size="6px"><b>{}</b></font></td></tr>
        <tr>
        <td style="text-align:center;width:10%">序号</td>
        <td style="text-align:center;width:15%">标识</td>
        <td style="text-align:center;width:15%">名称</td>
        <td style="text-align:center;width:60%">图片</td>
        </tr>
        """.format(neyong, request.session['area_name'])
        for area_name in area_names:
            bh = bh + 1
            neyong = """
            {}
            <tr>
            <td style="text-align:center;">{}</td>
            <td style="text-align:center;"><a href="/wap_tupian_area_name_list_change/?iid={}">{}</a></td>
            <td>{}</td>
            <td> <img {} src="{}" /></td>
            </tr>
            """.format(neyong, bh, area_name.id, area_name.attribute, area_name.name, area_name.code, area_name.value)
        neyong = """
        {}
        </table>
        """.format(neyong)
    else:
        neyong = '暂无图片<br/>'
    return render(request, 'wap_tupian_area_name_list.html', locals())


# 游戏本地图片修改
def wap_tupian_area_name_list_change(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['create_attribute']:
                area_names = Img.objects.get(id=request.session['object_ck_id'])
                if Img.objects.filter(area_name=request.session['area_name'], attribute=request.POST[
                    'create_attribute']).count() == 0 or area_names.attribute == request.POST[
                    'create_attribute']:
                    area_names.attribute = request.POST['create_attribute']
                    area_names.name = request.POST['create_name']
                    area_names.area_name = request.POST['create_area_name']
                    area_names.code = request.POST['create_code']
                    area_names.save()
                    messages = '修改图片成功'
                else:
                    messages = '此标示已被使用，请重新使用标示'
    else:
        messages = '你还不是设计'
    objects = Img.objects.get(area_name=request.session['area_name'], id=request.session['object_ck_id'])
    area_names = ImgAreaName.objects.all()
    img_neyong = '{}'.format(t.get('{}'.format(objects.attribute)))
    return render(request, 'wap_tupian_area_name_list_change.html', locals())


# 游戏图片修改
def wap_img_area_name_list_change(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    if user.is_designer == 'True':
        if request.method == "GET":
            if request.GET.get('create_value'):
                area_names = GameImg.objects.get(id=request.session['object_ck_id'])
                if GameImg.objects.filter(area_name=request.session['area_name'], attribute=request.GET.get(
                        'create_attribute')).count() == 0 or area_names.attribute == request.GET.get(
                    'create_attribute'):
                    area_names.attribute = request.GET.get('create_attribute')
                    area_names.name = request.GET.get('create_name')
                    area_names.area_name = request.GET.get('create_area_name')
                    area_names.value = request.GET.get('create_value')
                    area_names.code = request.GET.get('create_code')
                    area_names.save()
                    messages = '修改图片成功'
                else:
                    messages = '此标示已被使用，请重新使用标示'
    else:
        messages = '你还不是设计'
    objects = GameImg.objects.get(area_name=request.session['area_name'], id=request.session['object_ck_id'])
    area_names = GameImgAreaName.objects.all()
    img_neyong = '{}'.format(t.get('{}'.format(objects.attribute)))
    return render(request, 'wap_img_area_name_list_change.html', locals())


# 游戏交易行管理
def wap_auction_area_name(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('delete'):
            if Auction.objects.filter(auction_name=request.GET.get('auction_name')).count() == 0:
                Auction.objects.filter(auction_name=request.GET.get('auction_name')).delete()
                AuctionAreaName.objects.filter(auction_name=request.GET.get('auction_name')).delete()
                messages = '删除交易行成功'
            else:
                messages = '该交易行还有交易中物品，无法删除'
        if request.method == "POST":
            if request.POST['create_auction_name'] and request.POST['create_money_name'] and request.POST[
                'create_money_sx'] and request.POST['create_service_charge'] and request.POST['create_money_minute'] and \
                    request.POST['create_max_minute']:
                if AuctionAreaName.objects.filter(auction_name=request.POST['create_auction_name']).count() == 0:
                    area_names = AuctionAreaName.objects.create()
                    area_names.auction_name = request.POST['create_auction_name']
                    area_names.money_name = request.POST['create_money_name']
                    area_names.money_sx = request.POST['create_money_sx']
                    area_names.service_charge = request.POST['create_service_charge']
                    area_names.money_minute = request.POST['create_money_minute']
                    area_names.max_minute = request.POST['create_max_minute']
                    area_names.save()
                    messages = '创建交易行:{}成功'.format(request.POST['create_auction_name'])
                else:
                    messages = '交易行名重复'
            else:
                if request.POST['create_auction_name']:
                    messages = '请输入交易行名字'
                elif request.POST['create_money_name']:
                    messages = '请输入交易币名字'
                elif request.POST['create_money_sx']:
                    messages = '请输入交易币属性'
                elif request.POST['create_service_charge']:
                    messages = '请输入交易成功扣除手续费用%'
                elif request.POST['create_money_minute']:
                    messages = '请输入上架每分钟收取费用'
                elif request.POST['create_max_minute']:
                    messages = '请输入最大上架时间'
                else:
                    pass

    else:
        messages = '你还不是设计'
    area_names = AuctionAreaName.objects.all()
    neyong = ''
    if area_names.exists():
        bh = 0
        for area_name in area_names:
            bh = bh + 1
            neyong = """
             {}
             <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan{}">
             <div class="nav1">{}</div>
             <div class="nav2">
             <a  href="/wap_auction_area_name_change/?iid={}" >修改信息</a>
             <a style="color:white;">币种名称:{}</a>
             <a style="color:white;">币种属性:{}</a>
             <a style="color:white;">成交费用:{}%</a>
             <a style="color:white;">上架费用:{}币/分</a>
             <a style="color:white;">最大时间:{}分</a>
             </div>
             </div><br/>
             """.format(neyong, bh, area_name.auction_name, area_name.id, area_name.money_name, area_name.money_sx,
                        area_name.service_charge, area_name.money_minute, area_name.max_minute)
    else:
        neyong = '暂无交易行<br/>'
    return render(request, 'wap_auction_area_name.html', locals())


# 交易行修改
def wap_auction_area_name_change(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['create_money_name'] and request.POST[
                'create_money_sx'] and request.POST['create_service_charge'] and request.POST['create_money_minute'] and \
                    request.POST['create_max_minute']:
                area_names = AuctionAreaName.objects.get(id=request.session['object_ck_id'])
                area_names.money_name = request.POST['create_money_name']
                area_names.money_sx = request.POST['create_money_sx']
                area_names.service_charge = request.POST['create_service_charge']
                area_names.money_minute = request.POST['create_money_minute']
                area_names.max_minute = request.POST['create_max_minute']
                area_names.save()
                messages = '修改交易行信息成功'

            else:
                if request.POST['create_money_name']:
                    messages = '请输入交易币名字'
                elif request.POST['create_money_sx']:
                    messages = '请输入交易币属性'
                elif request.POST['create_service_charge']:
                    messages = '请输入交易成功扣除手续费用%'
                elif request.POST['create_money_minute']:
                    messages = '请输入上架每分钟收取费用'
                elif request.POST['create_max_minute']:
                    messages = '请输入最大上架时间'
                else:
                    pass
    else:
        messages = '你还不是设计'
    objects = AuctionAreaName.objects.get(id=request.session['object_ck_id'])
    return render(request, 'wap_auction_area_name_change.html', locals())


# 游戏排行榜管理
def wap_ranking_area_name(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('delete'):
            messages = '删除排行榜成功'
            RankingAreaName.objects.filter(id=request.GET.get('ranking_id')).delete()
            Ranking.objects.filter(ranking_id=request.GET.get('ranking_id')).delete()
        if request.method == "POST":
            if request.POST['create_ranking_name'] and request.POST['create_ranking_sx'] and request.POST[
                'create_ranking_count'] and request.POST['create_ranking_object']:
                area_names = RankingAreaName.objects.create()
                area_names.ranking_name = request.POST['create_ranking_name']
                area_names.ranking_sx = request.POST['create_ranking_sx']
                area_names.ranking_expression = request.POST['create_ranking_expression']
                area_names.ranking_count = request.POST['create_ranking_count']
                area_names.ranking_object = request.POST['create_ranking_object']
                area_names.save()
                messages = '创建排行榜:{}成功'.format(request.POST['create_ranking_name'])
            else:
                if request.POST['create_ranking_name']:
                    messages = '请输入排行榜名字'
                elif request.POST['create_ranking_sx']:
                    messages = '请输入排行榜属性'
                elif request.POST['create_ranking_count']:
                    messages = '请排行榜显示数量'
                elif request.POST['create_ranking_object']:
                    messages = '请选择对像'
                else:
                    pass

    else:
        messages = '你还不是设计'
    area_names = RankingAreaName.objects.all()
    neyong = ''
    if area_names.exists():
        bh = 0
        for area_name in area_names:
            bh = bh + 1
            if area_name.ranking_object == 'u':
                neyong = """
                {}
                <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan{}">
                <div class="nav1">{}</div>
                <div class="nav2">
                <a  href="/wap_ranking_area_name_change/?iid={}" >修改信息</a>
                <a style="color:white;">排行榜名:{}</a>
                <a style="color:white;">排行属性:{}</a>
                <a style="color:white;">显示条件:{}</a>
                <a style="color:white;">显示数量:{}</a>
                <a style="color:white;">显示对象:人物</a>
                </div>
                </div><br/>
                """.format(neyong, bh, area_name.ranking_name, area_name.id, area_name.ranking_name,
                           area_name.ranking_sx, area_name.ranking_expression,
                           area_name.ranking_count)
            else:
                neyong = """
                {}
                <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan{}">
                <div class="nav1">{}</div>
                <div class="nav2">
                <a  href="/wap_ranking_area_name_change/?iid={}" >修改信息</a>
                <a style="color:white;">排行榜名:{}</a>
                <a style="color:white;">排行属性:{}</a>
                <a style="color:white;">显示条件:{}</a>
                <a style="color:white;">显示数量:{}</a>
                <a style="color:white;">显示对象:宠物</a>
                </div>
                </div><br/>
                """.format(neyong, bh, area_name.ranking_name, area_name.id, area_name.ranking_name,
                           area_name.ranking_sx, area_name.ranking_expression,
                           area_name.ranking_count)
    else:
        neyong = '暂无排行榜<br/>'
    return render(request, 'wap_ranking_area_name.html', locals())


# 排行榜修改
def wap_ranking_area_name_change(request):
    messages = ''
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['create_ranking_name'] and request.POST['create_ranking_sx'] and request.POST[
                'create_ranking_count'] and request.POST['create_ranking_object']:
                area_names = RankingAreaName.objects.get(id=request.session['object_ck_id'])
                if request.POST['create_ranking_sx'] != area_names.ranking_sx or request.POST[
                    'create_ranking_object'] != area_names.ranking_object:
                    ranking = Ranking.objects.filter(ranking_id=request.session['object_ck_id'])
                    ranking.delete()
                area_names.ranking_name = request.POST['create_ranking_name']
                area_names.ranking_sx = request.POST['create_ranking_sx']
                area_names.ranking_expression = request.POST['create_ranking_expression']
                area_names.ranking_count = request.POST['create_ranking_count']
                area_names.ranking_object = request.POST['create_ranking_object']
                area_names.save()
                messages = '修改排行榜:{}成功'.format(request.POST['create_ranking_name'])
            else:
                if request.POST['create_ranking_name']:
                    messages = '请输入排行榜名字'
                elif request.POST['create_ranking_sx']:
                    messages = '请输入排行榜属性'
                elif request.POST['create_ranking_count']:
                    messages = '请排行榜显示数量'
                elif request.POST['create_ranking_object']:
                    messages = '请选择对像'
                else:
                    pass
    else:
        messages = '你还不是设计'
    objects = RankingAreaName.objects.get(id=request.session['object_ck_id'])
    return render(request, 'wap_ranking_area_name_change.html', locals())


"""
# 添加设计者
def wap_is_designer_create(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if User.objects.filter(name=user_name()).count() == 0:
        userss = 0
    else:
        userss = User.objects.get(name=user_name())
    if request.method == "POST":
        if request.POST['is_designer']:
            if int(userss.id) == int(request.session['user_id']) or int(c.is_designer_id) == int(
                    request.session['user_id']):
                if Player.objects.filter(id=request.POST['is_designer']).count() == 0:
                    iid = 0
                else:
                    iid = Player.objects.get(id=request.POST['is_designer'])
                    iid = iid.user_id
                if Player.objects.filter(id=request.POST['is_designer']).count() == 0:
                    messages = '没有这个ID号'
                elif Player.objects.filter(id=request.POST['is_designer'], is_designer='False').count() == 0:
                    messages = '该ID已经是设计号了，无需重复添加'
                elif int(c.is_designer_id) == int(iid):
                    messages = '你是此游戏GM，无需添加'
                else:
                    messages = '添加设计者成功'
                    player = Player.objects.get(id=request.POST['is_designer'])
                    player.is_designer = 'True'
                    player.save()
            else:
                messages = '你没有权限添加'
        else:
            messages = '请正确输入ID'
    return render(request, 'wap_is_designer_create.html', locals())
"""


# 设计大厅
def wap_admin(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = User.objects.get(id=request.session['user_id'])
    request.session['page_yeshu'] = '0'
    request.session['is_designer_id'] = c.is_designer_id
    request.session['object_z_bh'] = '0'
    request.session['object_z_id'] = '0'
    neyong = ''
    messages = ''
    if request.GET.get('update'):
        if user.is_designer == 'True':
            messages = '{}模板缓存更新成功'.format(messages)
            c.set('page_update', 1)
            c.save()
        else:
            messages = '你还不是设计者'
    neyong = """{}<a href="/wap_admin/?update=1&sj={}">更新玩家模板缓存</a><br/>""".format(neyong, sj())
    return render(request, 'wap_admin.html', locals())


# 区域或者物品分类
def wap_area_name(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = User.objects.get(id=request.session['user_id'])
    # 选择地图出口时传入编号
    if request.GET.get('object_z_bh'):
        request.session['object_z_bh'] = request.GET.get('object_z_bh')
    # 批量加入NPC临时记录上次的bh
    if request.session['object_z_bh'] == '107':
        if request.GET.get('area_name'):
            request.session['ut_area_name'] = request.GET.get('area_name')
            # 传入参数为NPC或者地图
    neyong = ''
    if request.GET.get('object_name') == 'npc' or request.GET.get('object_name') == 'map':
        request.session['object_name'] = request.GET.get('object_name')
        area_names = GameMapAreaName.objects.all()
        for area_name in area_names:
            if request.GET.get('object_name') == 'map':
                neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong,
                                                                                                area_name.area_name,
                                                                                                area_name.area_name,
                                                                                                GameMap.objects.filter(
                                                                                                    area_name=area_name.area_name).count())
            else:
                neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong,
                                                                                                area_name.area_name,
                                                                                                area_name.area_name,
                                                                                                GameNpc.objects.filter(
                                                                                                    area_name=area_name.area_name).count())
    # 传入参数为物品
    elif request.GET.get('object_name') == 'item':
        request.session['object_name'] = request.GET.get('object_name')
        area_names = ItemAreaName.objects.all()
        for area_name in area_names:
            neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong, area_name.area_name,
                                                                                            area_name.area_name,
                                                                                            Item.objects.filter(
                                                                                                area_name=area_name.area_name).count())
    # 传入参数为技能
    elif request.GET.get('object_name') == 'skill':
        request.session['object_name'] = request.GET.get('object_name')
        area_names = Skill.objects.all()
        for area_name in area_names:
            neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}(ID:{})</a><br/>'.format(neyong,
                                                                                               area_name.area_name,
                                                                                               area_name.name,
                                                                                               area_name.id)
    else:
        pass
    return render(request, 'wap_area_name.html', locals())


# 创建地图区域
def wap_area_name_create_map(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['area_name']:
                neyong = ''
                if request.session['object_name'] == 'map':
                    # 当此区域名称不重复时创建新区域
                    if GameMapAreaName.objects.filter(area_name=request.POST['area_name']).count() == 0:
                        area_name = GameMapAreaName.objects.create(area_name=request.POST['area_name'])
                        messages = '创建区域:{}成功'.format(request.POST['area_name'])
                        # 重新获得所有区域名称并返回
                        area_names = GameMapAreaName.objects.all()
                        for area_name in area_names:
                            if request.GET.get('object_name') == 'map':
                                neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong,
                                                                                                                area_name.area_name,
                                                                                                                area_name.area_name,
                                                                                                                GameMap.objects.filter(
                                                                                                                    area_name=area_name.area_name).count())
                            else:
                                neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong,
                                                                                                                area_name.area_name,
                                                                                                                area_name.area_name,
                                                                                                                GameNpc.objects.filter(
                                                                                                                    area_name=area_name.area_name).count())
                        return render(request, 'wap_area_name.html', locals())
                    else:
                        messages = '创建失败:{}区域名重复'.format(request.POST['area_name'])
                elif request.session['object_name'] == 'item':
                    if ItemAreaName.objects.filter(area_name=request.POST['area_name']).count() == 0:
                        area_name = ItemAreaName.objects.create(area_name=request.POST['area_name'])
                        messages = '创建物品类型:{}成功'.format(request.POST['area_name'])
                        # 重新获得所有区域名称并返回
                        area_names = ItemAreaName.objects.all()
                        for area_name in area_names:
                            neyong = '{}<a href="/wap_area_name_list/?area_name={}">{}({})</a><br/>'.format(neyong,
                                                                                                            area_name.area_name,
                                                                                                            area_name.area_name,
                                                                                                            Item.objects.filter(
                                                                                                                area_name=area_name.area_name).count())

                        return render(request, 'wap_area_name.html', locals())
                    else:
                        messages = '创建失败:{}物品类型名重复'.format(request.POST['area_name'])
                else:
                    pass
            else:
                messages = '请正确输入区域名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_area_name_create_map.html', locals())


# 修改地图区域名
def wap_area_name_change_map(request):
    t = ImgValue()  # 代表图片
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['area_name']:
                if request.session['object_name'] == 'map' or request.session['object_name'] == 'npc':
                    # 当此区域名称不重复时创建新区域
                    if GameMapAreaName.objects.filter(area_name=request.POST['area_name']).count() == 0:
                        area_name = GameMapAreaName.objects.get(area_name=request.session['area_name'])
                        area_names = GameMap.objects.filter(area_name=request.session['area_name'])
                        area_name.area_name = request.POST['area_name']
                        area_name.save()
                        for area_name in area_names:
                            area_name.area_name = request.POST['area_name']
                            area_name.save()
                        area_names = GameNpc.objects.filter(area_name=request.session['area_name'])
                        area_name.area_name = request.POST['area_name']
                        area_name.save()
                        for area_name in area_names:
                            area_name.area_name = request.POST['area_name']
                            area_name.save()
                        request.session['area_name'] = request.POST['area_name']
                        messages = '修改区域:{}成功'.format(request.POST['area_name'])
                    else:
                        messages = '修改失败:{}区域名重复'.format(request.POST['area_name'])
                elif request.session['object_name'] == 'item':
                    if Item.objects.filter(area_name=request.session['area_name']).count() == 0:
                        # 当此区域名称不重复时创建新区域
                        if ItemAreaName.objects.filter(area_name=request.POST['area_name']).count() == 0:
                            area_name = ItemAreaName.objects.get(area_name=request.session['area_name'])
                            area_name.area_name = request.POST['area_name']
                            area_name.save()
                            request.session['area_name'] = request.POST['area_name']
                            messages = '修改物品类别为:{}成功'.format(request.POST['area_name'])
                        else:
                            messages = '修改物品类别失败:{}区域名重复'.format(request.POST['area_name'])
                    else:
                        messages = '请先删除此类别下所有物品再修改物品类别名'
                else:
                    pass
                # 重新获得所有区域名称并返回
                if request.session['object_name'] == 'map':
                    objects = GameMap.objects.filter(area_name=request.session['area_name'])
                elif request.session['object_name'] == 'npc':
                    objects = GameNpc.objects.filter(area_name=request.session['area_name'])
                elif request.session['object_name'] == 'item':
                    objects = Item.objects.filter(area_name=request.session['area_name'])
                else:
                    objects = ''
                neyong = ''
                bh = 0
                for i in objects:
                    bh = bh + 1
                    if request.session['object_z_bh'] == '0':  # 正常显示
                        neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '100':  # <!--放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '107':  # <!--批量放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc_count/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '101' or request.session[
                        'object_z_bh'] == '102':  # <!--放置物品-->
                        neyong = '{}{}.<a href="/wap_area_name_list_item/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '103':  # <!--放置技能-->
                        neyong = '{}{}.<a href="/wap_area_name_list_skill/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '104':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '105':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?npc_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '106':  # <!--任务物品-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?item_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1000':  # <!--选择初始场景入口-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?map_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1001':  # <!--选择初始技能-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?skill_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    else:  # <!--地图出口1-4-->
                        neyong = '{}{}.<a href="/wap_area_name_list_export/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                return render(request, 'wap_area_name_list.html', locals())

            else:
                messages = '请正确输入区域名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        area_names = GameMap.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'npc':
        area_names = GameNpc.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'item':
        objects = Item.objects.filter(area_name=request.session['area_name'])
    else:
        area_names = 0
    return render(request, 'wap_area_name_change_map.html', locals())


# 地图/NPC/物品列表
def wap_area_name_list(request):
    t = ImgValue()  # 代表图片
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = User.objects.get(id=request.session['user_id'])
    # 选择初始技能时传入编号
    if request.GET.get('object_z_bh'):
        request.session['object_z_bh'] = request.GET.get('object_z_bh')
    # 此处接收跳过宠物分类
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    # 获取分类名字
    if request.GET.get('area_name'):
        request.session['area_name'] = request.GET.get('area_name')
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 删除地图区域
        if request.GET.get('delete'):
            if GameMap.objects.filter(area_name=request.session['area_name']).count() == 0 and GameNpc.objects.filter(
                    area_name=request.session['area_name']).count() == 0:
                GameMapAreaName.objects.filter(area_name=request.session['area_name']).delete()
                messages = '删除区域成功'
                return render(request, 'wap_admin.html', locals())
            else:
                messages = '此区域内存在地图或者区域内存在NPC，请先将此区域内地图和NPC全部删除后才能执行删除此区域'
        # 直接创建新地图
        if request.session['object_name'] == 'map':
            if request.GET.get('create') or request.GET.get('copy'):
                maps = 0
                if request.GET.get('create'):
                    messages = '创建地图成功'
                    if GameMap.objects.all().count() == 0:
                        count = 0
                    else:
                        count = GameMap.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if GameMap.objects.all().count() == count:
                        maps = GameMap.objects.create(name='未定义', desc='未定义',
                                                      area_name=request.session['area_name'])
                    else:
                        map_all = GameMap.objects.all()
                        bh = 0
                        for maps in map_all:
                            bh = bh + 1
                            if int(maps.id) == bh:
                                pass
                            else:
                                maps = GameMap.objects.create(id=bh, name='未定义', desc='未定义',
                                                              area_name=request.session['area_name'])
                                break
                    # 将定义属性附入
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            maps.set('{}'.format(attribute.attribute), attribute.value)
                    maps.save()
                if request.GET.get('copy'):
                    messages = '复制地图成功'
                    objects = GameMap.objects.get(id=request.session['object_id'])
                    if GameMap.objects.all().count() == 0:
                        count = 0
                    else:
                        count = GameMap.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if GameMap.objects.all().count() == count:
                        maps = GameMap.objects.create(name='{}(复制)'.format(objects.name),
                                                      desc='{}'.format(objects.desc),
                                                      area_name='{}'.format(objects.area_name),
                                                      params='{}'.format(objects.params))
                    else:
                        map_all = GameMap.objects.all()
                        bh = 0
                        for maps in map_all:
                            bh = bh + 1
                            if int(maps.id) == bh:
                                pass
                            else:
                                maps = GameMap.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                              desc='{}'.format(objects.desc),
                                                              area_name='{}'.format(objects.area_name),
                                                              params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='场景创建事件', map_id=maps.id)
                event = Event.objects.create(name='场景查看事件', map_id=maps.id)
                event = Event.objects.create(name='场景进入事件', map_id=maps.id)
                event = Event.objects.create(name='场景离开事件', map_id=maps.id)
                request.session['object_id'] = maps.id
                objects = GameMap.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        # 直接创建新NPC
        elif request.session['object_name'] == 'npc':
            if request.GET.get('create') or request.GET.get('copy'):
                npcs = 0
                if request.GET.get('create'):
                    messages = '创建NPC成功'
                    if GameNpc.objects.all().count() == 0:
                        count = 0
                    else:
                        count = GameNpc.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if GameNpc.objects.all().count() == count:
                        npcs = GameNpc.objects.create(name='未定义', desc='未定义',
                                                      area_name=request.session['area_name'])
                    else:
                        npc_all = GameNpc.objects.all()
                        bh = 0
                        for npcs in npc_all:
                            bh = bh + 1
                            if int(npcs.id) == bh:
                                pass
                            else:
                                npcs = GameNpc.objects.create(id=bh, name='未定义', desc='未定义',
                                                              area_name=request.session['area_name'])
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            npcs.set('{}'.format(attribute.attribute), attribute.value)
                    npcs.save()
                if request.GET.get('copy'):
                    messages = '复制NPC成功'
                    objects = GameNpc.objects.get(id=request.session['object_id'])
                    if GameNpc.objects.all().count() == 0:
                        count = 0
                    else:
                        count = GameNpc.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if GameNpc.objects.all().count() == count:
                        npcs = GameNpc.objects.create(name='{}(复制)'.format(objects.name),
                                                      desc='{}'.format(objects.desc),
                                                      area_name='{}'.format(objects.area_name),
                                                      params='{}'.format(objects.params))
                    else:
                        npc_all = GameNpc.objects.all()
                        bh = 0
                        for npcs in npc_all:
                            bh = bh + 1
                            if int(npcs.id) == bh:
                                pass
                            else:
                                npcs = GameNpc.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                              desc='{}'.format(objects.desc),
                                                              area_name='{}'.format(objects.area_name),
                                                              params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='创建事件', npc_id=npcs.id)
                event = Event.objects.create(name='查看事件', npc_id=npcs.id)
                event = Event.objects.create(name='战胜事件', npc_id=npcs.id)
                event = Event.objects.create(name='战败事件', npc_id=npcs.id)
                request.session['object_id'] = npcs.id
                objects = GameNpc.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        elif request.session['object_name'] == 'chongwu':
            if request.GET.get('create') or request.GET.get('copy'):
                chongwus = 0
                if request.GET.get('create'):
                    messages = '创建宠物成功'
                    if ChongWu.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ChongWu.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ChongWu.objects.all().count() == count:
                        chongwus = ChongWu.objects.create(name='未定义', desc='未定义',
                                                          area_name=request.session['area_name'])
                    else:
                        chongwu_all = ChongWu.objects.all()
                        bh = 0
                        for chongwus in chongwu_all:
                            bh = bh + 1
                            if int(chongwus.id) == bh:
                                pass
                            else:
                                chongwus = ChongWu.objects.create(id=bh, name='未定义', desc='未定义',
                                                                  area_name=request.session['area_name'])
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            chongwus.set('{}'.format(attribute.attribute), attribute.value)
                    chongwus.save()
                if request.GET.get('copy'):
                    messages = '复制宠物成功'
                    objects = ChongWu.objects.get(id=request.session['object_id'])
                    if ChongWu.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ChongWu.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ChongWu.objects.all().count() == count:
                        chongwus = ChongWu.objects.create(name='{}(复制)'.format(objects.name),
                                                          desc='{}'.format(objects.desc),
                                                          area_name='{}'.format(objects.area_name),
                                                          params='{}'.format(objects.params))
                    else:
                        chongwu_all = ChongWu.objects.all()
                        bh = 0
                        for chongwus in chongwu_all:
                            bh = bh + 1
                            if int(chongwus.id) == bh:
                                pass
                            else:
                                chongwus = ChongWu.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                                  desc='{}'.format(objects.desc),
                                                                  area_name='{}'.format(objects.area_name),
                                                                  params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='创建事件', chongwu_id=chongwus.id)
                event = Event.objects.create(name='查看事件', chongwu_id=chongwus.id)
                event = Event.objects.create(name='战胜事件', chongwu_id=chongwus.id)
                event = Event.objects.create(name='战败事件', chongwu_id=chongwus.id)
                request.session['object_id'] = chongwus.id
                objects = ChongWu.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        elif request.session['object_name'] == 'shibing':
            if request.GET.get('create') or request.GET.get('copy'):
                shibings = 0
                if request.GET.get('create'):
                    messages = '创建兵种成功'
                    if ShiBing.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ShiBing.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ShiBing.objects.all().count() == count:
                        shibings = ShiBing.objects.create(name='未定义', desc='未定义',
                                                          area_name=request.session['area_name'])
                    else:
                        shibing_all = ShiBing.objects.all()
                        bh = 0
                        for shibings in shibing_all:
                            bh = bh + 1
                            if int(shibings.id) == bh:
                                pass
                            else:
                                shibings = ShiBing.objects.create(id=bh, name='未定义', desc='未定义',
                                                                  area_name=request.session['area_name'])
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            shibings.set('{}'.format(attribute.attribute), attribute.value)
                    shibings.save()
                if request.GET.get('copy'):
                    messages = '复制士兵成功'
                    objects = ShiBing.objects.get(id=request.session['object_id'])
                    if ShiBing.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ShiBing.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ShiBing.objects.all().count() == count:
                        shibings = ShiBing.objects.create(name='{}(复制)'.format(objects.name),
                                                          desc='{}'.format(objects.desc),
                                                          area_name='{}'.format(objects.area_name),
                                                          params='{}'.format(objects.params))
                    else:
                        shibing_all = ShiBing.objects.all()
                        bh = 0
                        for shibings in shibing_all:
                            bh = bh + 1
                            if int(shibings.id) == bh:
                                pass
                            else:
                                shibings = ShiBing.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                                  desc='{}'.format(objects.desc),
                                                                  area_name='{}'.format(objects.area_name),
                                                                  params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='创建事件', shibing_id=shibings.id)
                event = Event.objects.create(name='查看事件', shibing_id=shibings.id)
                event = Event.objects.create(name='战胜事件', shibing_id=shibings.id)
                event = Event.objects.create(name='战败事件', shibing_id=shibings.id)
                request.session['object_id'] = shibings.id
                objects = ShiBing.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        elif request.session['object_name'] == 'zuoqi':
            if request.GET.get('create') or request.GET.get('copy'):
                zuoqis = 0
                if request.GET.get('create'):
                    messages = '创建坐骑成功'
                    if ZuoQi.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ZuoQi.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = int(count.id)
                    if ZuoQi.objects.all().count() == count:
                        zuoqis = ZuoQi.objects.create(name='未定义', desc='未定义',
                                                      area_name=request.session['area_name'])
                    else:
                        zuoqi_all = ZuoQi.objects.all()
                        bh = 0
                        for zuoqis in zuoqi_all:
                            bh = bh + 1
                            if int(zuoqis.id) == bh:
                                pass
                            else:
                                zuoqis = ZuoQi.objects.create(id=bh, name='未定义', desc='未定义',
                                                              area_name=request.session['area_name'])
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            zuoqis.set('{}'.format(attribute.attribute), attribute.value)
                    zuoqis.save()
                if request.GET.get('copy'):
                    messages = '复制坐骑成功'
                    objects = ZuoQi.objects.get(id=request.session['object_id'])
                    if ZuoQi.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ZuoQi.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ZuoQi.objects.all().count() == count:
                        zuoqis = ZuoQi.objects.create(name='{}(复制)'.format(objects.name),
                                                      desc='{}'.format(objects.desc),
                                                      area_name='{}'.format(objects.area_name),
                                                      params='{}'.format(objects.params))
                    else:
                        zuoqi_all = ZuoQi.objects.all()
                        bh = 0
                        for zuoqis in zuoqi_all:
                            bh = bh + 1
                            if int(zuoqis.id) == bh:
                                pass
                            else:
                                zuoqis = ZuoQi.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                              desc='{}'.format(objects.desc),
                                                              area_name='{}'.format(objects.area_name),
                                                              params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='创建事件', zuoqi_id=zuoqis.id)
                event = Event.objects.create(name='查看事件', zuoqi_id=zuoqis.id)
                event = Event.objects.create(name='战胜事件', zuoqi_id=zuoqis.id)
                event = Event.objects.create(name='战败事件', zuoqi_id=zuoqis.id)
                request.session['object_id'] = zuoqis.id
                objects = ZuoQi.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        elif request.session['object_name'] == 'item':
            if request.GET.get('create') or request.GET.get('copy'):
                item = 0
                if request.GET.get('create'):
                    messages = '创建物品成功'
                    if Item.objects.all().count() == 0:
                        count = 0
                    else:
                        count = Item.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ItemType.objects.filter(type=request.session['area_name']).count() == 0:
                        type_id = 0
                    else:
                        type_id = ItemType.objects.get(type=request.session['area_name'])
                        type_id = type_id.id
                    if Item.objects.all().count() == count:
                        item = Item.objects.create(name='未定义', desc='未定义', area_name=request.session['area_name'],
                                                   type=type_id)
                    else:
                        item_all = Item.objects.all()
                        bh = 0
                        for item in item_all:
                            bh = bh + 1
                            if int(item.id) == bh:
                                pass
                            else:
                                item = Item.objects.create(id=bh, name='未定义', desc='未定义',
                                                           area_name=request.session['area_name'], type=type_id)
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            item.set('{}'.format(attribute.attribute), attribute.value)
                    item.save()
                if request.GET.get('copy'):
                    messages = '复制物品成功'
                    objects = Item.objects.get(id=request.session['object_id'])
                    if Item.objects.all().count() == 0:
                        count = 0
                    else:
                        count = Item.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ItemType.objects.filter(type=request.session['area_name']).count() == 0:
                        type_id = 0
                    else:
                        type_id = ItemType.objects.get(type=request.session['area_name'])
                        type_id = type_id.id
                    if Item.objects.all().count() == count:
                        item = Item.objects.create(name='{}(复制)'.format(objects.name), desc='{}'.format(objects.desc),
                                                   area_name='{}'.format(objects.area_name),
                                                   type='{}'.format(objects.type),
                                                   params='{}'.format(objects.params),
                                                   type_id='{}'.format(objects.type_id),
                                                   biaoshi='{}'.format(objects.biaoshi),
                                                   duixiang='{}'.format(objects.duixiang),
                                                   bangding='{}'.format(objects.bangding))
                    else:
                        item_all = Item.objects.all()
                        bh = 0
                        for item in item_all:
                            bh = bh + 1
                            if int(item.id) == bh:
                                pass
                            else:
                                item = Item.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                           desc='{}'.format(objects.desc),
                                                           area_name='{}'.format(objects.area_name),
                                                           type='{}'.format(objects.type),
                                                           params='{}'.format(objects.params),
                                                           type_id='{}'.format(objects.type_id),
                                                           biaoshi='{}'.format(objects.biaoshi),
                                                           duixiang='{}'.format(objects.duixiang),
                                                           bangding='{}'.format(objects.bangding))
                                break
                event = Event.objects.create(name='创建事件', item_id=item.id)
                event = Event.objects.create(name='查看事件', item_id=item.id)
                event = Event.objects.create(name='使用事件', item_id=item.id)
                event = Event.objects.create(name='穿上事件', item_id=item.id)
                event = Event.objects.create(name='卸下事件', item_id=item.id)
                request.session['object_id'] = item.id
                objects = Item.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        # 直接创建新技能
        elif request.session['object_name'] == 'skill':
            if request.GET.get('create') or request.GET.get('copy'):
                skills = 0
                if request.GET.get('create'):
                    messages = '创建技能成功'
                    if Skill.objects.all().count() == 0:
                        count = 0
                    else:
                        count = Skill.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if Skill.objects.all().count() == count:
                        skills = Skill.objects.create(name='未定义', desc='未定义')
                    else:
                        skill_all = Skill.objects.all()
                        bh = 0
                        for skills in skill_all:
                            bh = bh + 1
                            if int(skills.id) == bh:
                                pass
                            else:
                                skills = Skill.objects.create(id=bh, name='未定义', desc='未定义')
                                break
                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                    for attribute in attributes:
                        if int(attribute.built_in) == 1:
                            pass
                        else:
                            skills.set('{}'.format(attribute.attribute), attribute.value)
                    skills.save()
                if request.GET.get('copy'):
                    messages = '复制技能成功'
                    objects = Skill.objects.get(id=request.session['object_id'])
                    if Skill.objects.all().count() == 0:
                        count = 0
                    else:
                        count = Skill.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if Skill.objects.all().count() == count:
                        skills = Skill.objects.create(name='{}(复制)'.format(objects.name),
                                                      desc='{}'.format(objects.desc),
                                                      lvl='{}'.format(objects.lvl),
                                                      params='{}'.format(objects.params))
                    else:
                        skill_all = Skill.objects.all()
                        bh = 0
                        for skills in skill_all:
                            bh = bh + 1
                            if int(skills.id) == bh:
                                pass
                            else:
                                skills = Skill.objects.create(id=bh, name='{}(复制)'.format(objects.name),
                                                              desc='{}'.format(objects.desc),
                                                              lvl='{}'.format(objects.lvl),
                                                              params='{}'.format(objects.params))
                                break
                event = Event.objects.create(name='创建事件', skill_id=skills.id)
                request.session['object_id'] = skills.id
                objects = Skill.objects.get(id=request.session['object_id'])
                objects_name = get_tupian(objects.name)
                return render(request, 'wap_area_name_list_see.html', locals())
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session.get('type', '0') == '0':
        request.session['type'] = '全部'
    if request.method == "POST":
        if request.POST['type']:
            request.session['type'] = request.POST['type']
    if request.session['object_name'] == 'map':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = GameMap.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = GameMap.objects.filter(name__contains=request.GET.get('ck_name'),
                                                 area_name=request.session['area_name'])
        else:
            objects = GameMap.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'npc':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = GameNpc.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = GameNpc.objects.filter(name__contains=request.GET.get('ck_name'),
                                                 area_name=request.session['area_name'])
        else:
            objects = GameNpc.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'chongwu':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = ChongWu.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = ChongWu.objects.filter(name__contains=request.GET.get('ck_name'))
        else:
            objects = ChongWu.objects.all()
    elif request.session['object_name'] == 'zuoqi':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = ZuoQi.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = ZuoQi.objects.filter(name__contains=request.GET.get('ck_name'))
        else:
            objects = ZuoQi.objects.all()
    elif request.session['object_name'] == 'shibing':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = ShiBing.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = ShiBing.objects.filter(name__contains=request.GET.get('ck_name'))
        else:
            objects = ShiBing.objects.all()
    elif request.session['object_name'] == 'item':
        if request.session['area_name'] == '全部':
            objects = Item.objects.all()
        else:
            if request.session['type'] == '全部':
                objects = Item.objects.filter(area_name=request.session['area_name'])
            else:
                objects = Item.objects.filter(area_name=request.session['area_name'], type=request.session['type'])
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = Item.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = Item.objects.filter(name__contains=request.GET.get('ck_name'))
    elif request.session['object_name'] == 'skill':
        if request.GET.get('ck_name'):
            if request.GET.get('ck_type') == '全部':
                objects = Skill.objects.filter(name__contains=request.GET.get('ck_name'))
            else:
                objects = Skill.objects.filter(name__contains=request.GET.get('ck_name'))
        else:
            objects = Skill.objects.all()
    else:
        objects = ''
    itemtypes = ItemType.objects.all().order_by('position')
    neyong = ''
    bh = 0
    for i in objects:
        bh = bh + 1
        if request.session['object_z_bh'] == '0':  # 正常显示
            i = GameObject(i)
            try:
                if i.bangding == 0:
                    neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                    neyong, bh, i.id, get_tupian(i.name), i.id)
                else:
                    neyong = '{}{}.[绑]<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                    neyong, bh, i.id, get_tupian(i.name), i.id)
            except:
                i.set('name', '未定义')
                i.save()
                if i.bangding == 0:
                    neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                    neyong, bh, i.id, get_tupian(i.name), i.id)
                else:
                    neyong = '{}{}.[绑]<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                    neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '100':  # <!--放置电脑人物-->
            neyong = '{}{}.<a href="/wap_area_name_list_npc/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '107':  # <!--批量放置电脑人物-->
            neyong = '{}{}.<a href="/wap_area_name_list_npc_count/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '108':  # <!--出售物品-->
            neyong = '{}{}.<a href="/wap_area_name_list_sell/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '101' or request.session['object_z_bh'] == '102':  # <!--放置物品-->
            neyong = '{}{}.<a href="/wap_area_name_list_item/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '103':  # <!--放置技能-->
            neyong = '{}{}.<a href="/wap_area_name_list_skill/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '104':  # <!--任务NPC-->
            neyong = '{}{}.<a href="/wap_task_attribute/?iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '105':  # <!--任务NPC-->
            neyong = '{}{}.<a href="/wap_task_attribute/?npc_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '106':  # <!--任务物品-->
            neyong = '{}{}.<a href="/wap_task_attribute/?item_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '1000':  # <!--选择初始场景入口-->
            neyong = '{}{}.<a href="/wap_attribute_basic/?map_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        elif request.session['object_z_bh'] == '1001':  # <!--选择初始技能-->
            neyong = '{}{}.<a href="/wap_attribute_basic/?skill_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
        else:  # <!--地图出口1-4-->
            neyong = '{}{}.<a href="/wap_area_name_list_export/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                neyong, bh, i.id, get_tupian(i.name), i.id)
    if neyong == '':
        if request.GET.get('ck_name'):
            neyong = '未搜索到匹配字:{}<br/>'.format(request.GET.get('ck_name'))
        else:
            neyong = '无<br/>'
    neyong = '{}---<br/>'.format(neyong)
    return render(request, 'wap_area_name_list.html', locals())


# 批量增加NPC
def wap_area_name_list_npc_count(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('iid'):
        request.session['object_id'] = request.GET.get('iid')
    objects = GameNpc.objects.get(id=request.session['object_id'])
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['npc_count']:
                request.session['area_name'] = request.session['ut_area_name']
                area_names = GameMap.objects.filter(area_name=request.session['area_name'])
                for area_name in area_names:
                    if GameMapPlaceNpc.objects.filter(map_id=area_name.id, npc_id=objects.id).count() == 0:
                        placenpc = GameMapPlaceNpc.objects.create()
                        placenpc.map_id = area_name.id
                        placenpc.npc_id = objects.id
                        placenpc.npc_code = request.POST['npc_count']
                        placenpc.save()
                messages = '批量放置电脑人物到此区域下所有地图成功'
                request.session['object_z_bh'] = '0'
                request.session['object_name'] = 'map'
                request.session['ut_area_name'] = 0
                if request.session['object_name'] == 'map':
                    objects = GameMap.objects.filter(area_name=request.session['area_name'])
                neyong = ''
                bh = 0
                for i in objects:
                    bh = bh + 1
                    if request.session['object_z_bh'] == '0':  # 正常显示
                        neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '100':  # <!--放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '107':  # <!--批量放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc_count/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '108':  # <!--出售物品-->
                        neyong = '{}{}.<a href="/wap_area_name_list_sell/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '101' or request.session[
                        'object_z_bh'] == '102':  # <!--放置物品-->
                        neyong = '{}{}.<a href="/wap_area_name_list_item/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '103':  # <!--放置技能-->
                        neyong = '{}{}.<a href="/wap_area_name_list_skill/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '104':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '105':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?npc_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '106':  # <!--任务物品-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?item_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1000':  # <!--选择初始场景入口-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?map_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1001':  # <!--选择初始技能-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?skill_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    else:  # <!--地图出口1-4-->
                        neyong = '{}{}.<a href="/wap_area_name_list_export/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                return render(request, 'wap_area_name_list.html', locals())
            else:
                messages = '请输入数量'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        area_names = GameMap.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'npc':
        area_names = GameNpc.objects.filter(area_name=request.session['area_name'])
    else:
        area_names = 0
    return render(request, 'wap_area_name_list_npc_count.html', locals())


# 批量地图
def wap_area_name_list_create_map(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    # 获得全局定义属性
    attributes = Attribute.objects.filter(duixiang=request.session['object_name']).order_by('position')
    # attributes = attributes.iterator()
    attributes = dict.fromkeys(attributes)
    neyong = ''
    name_count = 0
    name_desc = 0
    money_money = 0
    pm_money = 0
    for attribute in attributes:
        neyong = '{}{}:'.format(neyong, attribute.name)
        if attribute.attribute == 'name':
            name_count = 1
            neyong = '{}<textarea name="name"  maxlength="99999" rows="1" cols="40"></textarea><br/>'.format(
                neyong)
        elif attribute.attribute == 'desc':
            name_desc = 1
            neyong = '{}<textarea name="desc"  maxlength="99999" rows="4" cols="40"></textarea><br/>'.format(
                neyong)
        else:
            if attribute.type == '1':
                neyong = '{}<input name={} value={} /><br/>'.format(neyong, attribute.attribute, attribute.value)
            elif attribute.type == '2':
                neyong = '{}<select name={}>'.format(neyong, attribute.attribute)
                if attribute.value == '0' or attribute.value == "False" or attribute.value == "false":
                    neyong = '{}<option value = 0 selected="selected" >否</option>'.format(neyong)
                    neyong = '{}<option value = 1 >是</option>'.format(neyong)
                else:
                    neyong = '{}<option value = 1 >是</option>'.format(neyong)
                    neyong = '{}<option value = 0 selected="selected" >否</option>'.format(neyong)
                neyong = '{}</select><br/>'.format(neyong)
            elif attribute.type == '3':
                neyong = '{}<textarea name={}  maxlength="99999" rows="4" cols="40"></textarea><br/>'.format(neyong,
                                                                                                             attribute.attribute)
            else:
                pass
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['name']:
                if request.POST['map_x']:
                    if int(request.POST['map_x']) > 0:
                        if request.POST['map_y']:
                            if int(request.POST['map_y']) > 0:
                                map_x = int(request.POST['map_x'])
                                map_y = int(request.POST['map_y'])
                                name = request.POST['name']
                                x = 0
                                messages = ''
                                for i in range(0, map_x):
                                    y = 1
                                    x = i + 1
                                    maps = GameMap.objects.create(name='{}({},{})'.format(name, x, y),
                                                                  desc=request.POST['desc'],
                                                                  area_name=request.session['area_name'])
                                    maps = GameObject(maps)
                                    # 往左
                                    if i > 0:
                                        maps.set('map_left', int(maps.id) - map_y)
                                    # 往右
                                    if i + 1 < map_x:
                                        maps.set('map_right', int(maps.id) + map_y)
                                    # 连接上地图
                                    if i < map_y:
                                        maps.set('map_upper', int(maps.id) + 1)
                                    attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                                    for attribute in attributes:
                                        if int(attribute.built_in) == 1:
                                            pass
                                        else:
                                            if int(attribute.type) == 1:
                                                if request.POST['{}'.format(attribute.attribute)]:
                                                    maps.set('{}'.format(attribute.attribute),
                                                             request.POST['{}'.format(attribute.attribute)])
                                                else:
                                                    maps.set('{}'.format(attribute.attribute), 0)
                                            elif int(attribute.type) == 2:
                                                if request.POST['{}'.format(attribute.attribute)]:
                                                    maps.set('{}'.format(attribute.attribute),
                                                             request.POST['{}'.format(attribute.attribute)])
                                                else:
                                                    maps.set('{}'.format(attribute.attribute), 0)
                                            elif int(attribute.type) == 3:
                                                if request.POST['{}'.format(attribute.attribute)]:
                                                    maps.set('{}'.format(attribute.attribute),
                                                             request.POST['{}'.format(attribute.attribute)])
                                                else:
                                                    maps.set('{}'.format(attribute.attribute), 0)
                                            else:
                                                pass
                                    maps.save()
                                    event = Event.objects.create(name='场景创建事件', map_id=maps.id)
                                    event = Event.objects.create(name='场景查看事件', map_id=maps.id)
                                    event = Event.objects.create(name='场景进入事件', map_id=maps.id)
                                    event = Event.objects.create(name='场景离开事件', map_id=maps.id)
                                    for ii in range(1, map_y):
                                        y = ii + 1
                                        maps = GameMap.objects.create(name='{}({},{})'.format(name, x, y),
                                                                      desc=request.POST['desc'],
                                                                      area_name=request.session['area_name'])
                                        maps = GameObject(maps)
                                        # 往左
                                        if i > 0:
                                            maps.set('map_left', int(maps.id) - map_y)
                                            # messages = '{}往左{} > 1<br/>'.format(messages, ii)
                                        # 往右
                                        if i + 1 < map_x:
                                            maps.set('map_right', int(maps.id) + map_y)
                                            # messages = '{}往右{} < {}<br/>'.format(messages, ii, map_x)
                                        # 连接上地图
                                        if ii + 1 < map_y:
                                            maps.set('map_upper', int(maps.id) + 1)
                                            # messages = '{}往上{} < {}<br/>'.format(messages, ii, map_y)
                                        # 连接下地图
                                        if ii > 0:
                                            maps.set('map_lower', int(maps.id) - 1)
                                        attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                                        for attribute in attributes:
                                            if int(attribute.built_in) == 1:
                                                pass
                                            else:
                                                if int(attribute.type) == 1:
                                                    if request.POST['{}'.format(attribute.attribute)]:
                                                        maps.set('{}'.format(attribute.attribute),
                                                                 request.POST['{}'.format(attribute.attribute)])
                                                    else:
                                                        maps.set('{}'.format(attribute.attribute), 0)
                                                elif int(attribute.type) == 2:
                                                    if request.POST['{}'.format(attribute.attribute)]:
                                                        maps.set('{}'.format(attribute.attribute),
                                                                 request.POST['{}'.format(attribute.attribute)])
                                                    else:
                                                        maps.set('{}'.format(attribute.attribute), 0)
                                                elif int(attribute.type) == 3:
                                                    if request.POST['{}'.format(attribute.attribute)]:
                                                        maps.set('{}'.format(attribute.attribute),
                                                                 request.POST['{}'.format(attribute.attribute)])
                                                    else:
                                                        maps.set('{}'.format(attribute.attribute), 0)
                                                else:
                                                    pass
                                        maps.save()
                                        event = Event.objects.create(name='场景创建事件', map_id=maps.id)
                                        event = Event.objects.create(name='场景查看事件', map_id=maps.id)
                                        event = Event.objects.create(name='场景进入事件', map_id=maps.id)
                                        event = Event.objects.create(name='场景离开事件', map_id=maps.id)
                                        # messages = '{}------------<br/>'.format(messages)
                                messages = '{}批量创建成功'.format(messages)
                                if request.session['object_name'] == 'map':
                                    objects = GameMap.objects.filter(area_name=request.session['area_name'])
                                neyong = ''
                                bh = 0
                                for i in objects:
                                    bh = bh + 1
                                    if request.session['object_z_bh'] == '0':  # 正常显示
                                        neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '100':  # <!--放置电脑人物-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_npc/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '107':  # <!--批量放置电脑人物-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_npc_count/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '108':  # <!--出售物品-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_sell/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '101' or request.session[
                                        'object_z_bh'] == '102':  # <!--放置物品-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_item/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '103':  # <!--放置技能-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_skill/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '104':  # <!--任务NPC-->
                                        neyong = '{}{}.<a href="/wap_task_attribute/?iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '105':  # <!--任务NPC-->
                                        neyong = '{}{}.<a href="/wap_task_attribute/?npc_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '106':  # <!--任务物品-->
                                        neyong = '{}{}.<a href="/wap_task_attribute/?item_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '1000':  # <!--选择初始场景入口-->
                                        neyong = '{}{}.<a href="/wap_attribute_basic/?map_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    elif request.session['object_z_bh'] == '1001':  # <!--选择初始技能-->
                                        neyong = '{}{}.<a href="/wap_attribute_basic/?skill_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                    else:  # <!--地图出口1-4-->
                                        neyong = '{}{}.<a href="/wap_area_name_list_export/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                                            neyong, bh, i.id, get_tupian(i.name), i.id)
                                return render(request, 'wap_area_name_list.html', locals())

                            else:
                                messages = '请输入数字'
                        else:
                            messages = '请正确输入地图y'
                    else:
                        messages = '请输入数字'
                else:
                    messages = '请正确输入地图x'
            else:
                messages = '请正确输入地图名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        area_names = GameMap.objects.filter(area_name=request.session['area_name'])
    elif request.session['object_name'] == 'npc':
        area_names = GameNpc.objects.filter(area_name=request.session['area_name'])
    else:
        area_names = 0
    return render(request, 'wap_area_name_list_create_map.html', locals())


# 地图/NPC/宠物/物品查看
def wap_area_name_list_see(request):
    encryption = {}
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    if request.GET.get('object_id'):
        request.session['object_id'] = request.GET.get('object_id')
    request.session['object_z_id'] = '0'  # 选择地图出口时记录主地图ID
    request.session['object_z_bh'] = '0'  # 选择地图出口时记录编号
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 删除地图/NPC/宠物/物品
        if request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                if request.session['object_name'] == 'map':
                    objects = GameMap.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(map_id=objects.id)
                    operations = Operation.objects.filter(map_id=objects.id)
                    # 删除放置NPC
                    if GameMapPlaceNpc.objects.filter(map_id=objects.id).count() == 0:
                        pass
                    else:
                        npcs = GameMapPlaceNpc.objects.filter(map_id=objects.id)
                        for npc in npcs:
                            npc.delete()
                    if GameNpcs.objects.filter(map_id=objects.id).count() == 0:
                        pass
                    else:
                        npcs = GameNpcs.objects.filter(map_id=objects.id)
                        for npc in npcs:
                            npc.delete()
                    # 删除放置物品
                    if GameMapPlaceItem.objects.filter(map_id=objects.id).count() == 0:
                        pass
                    else:
                        items = GameMapPlaceItem.objects.filter(map_id=objects.id)
                        for item in items:
                            item.delete()
                    if ItemPlayer.objects.filter(map_id=objects.id).count() == 0:
                        pass
                    else:
                        items = ItemPlayer.objects.filter(map_id=objects.id)
                        for item in items:
                            item.delete()
                elif request.session['object_name'] == 'npc':
                    objects = GameNpc.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(npc_id=objects.id)
                    operations = Operation.objects.filter(npc_id=objects.id)
                    # 删除放置技能
                    if GameMapPlaceSkill.objects.filter(map_id=objects.id).count() == 0:
                        pass
                    else:
                        skills = GameMapPlaceSkill.objects.filter(map_id=objects.id)
                        for skill in skills:
                            skill.delete()
                elif request.session['object_name'] == 'chongwu':
                    objects = ChongWu.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(chongwu_id=objects.id)
                    operations = Operation.objects.filter(chongwu_id=objects.id)
                elif request.session['object_name'] == 'zuoqi':
                    objects = ZuoQi.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(zuoqi_id=objects.id)
                    operations = Operation.objects.filter(zuoqi_id=objects.id)
                elif request.session['object_name'] == 'item':
                    objects = Item.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(item_id=objects.id)
                    operations = Operation.objects.filter(item_id=objects.id)
                    deletes = ItemPlayer.objects.filter(item_id=request.session['object_id'])
                    for delete in deletes:
                        delete.delete()
                    auctions = Auction.objects.all()
                    for auction in auctions:
                        if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                            wap_zb = ItemPlayer.objects.get(id=auction.item_id)
                            if int(wap_zb.item_id) == int(request.session['object_id']):
                                auction.delete()
                        else:
                            if int(auction.item_id) == int(request.session['object_id']):
                                auction.delete()
                elif request.session['object_name'] == 'skill':
                    objects = Skill.objects.get(id=request.session['object_id'])
                    # 删除地图/NPC/宠物/物品事件
                    events = Event.objects.filter(skill_id=objects.id)
                    operations = Operation.objects.filter(skill_id=objects.id)
                else:
                    objects = ''
                    events = ''
                    operations = ''
                for event in events:
                    event_list = EventList.objects.filter(event=event.id)
                    event_list.delete()
                    event.delete()
                # 删除地图/NPC/宠物/物品操作
                for operation in operations:
                    event = Event.objects.filter(operation=operation.event)
                    event_list = EventList.objects.filter(event=operation.event)
                    event_list.delete()
                    event.delete()
                    operation.delete()
                # 删除地图/NPC/宠物/物品
                objects.delete()
                messages = '删除成功'
                if request.session['object_name'] == 'map':
                    objects = GameMap.objects.filter(area_name=request.session['area_name'])
                elif request.session['object_name'] == 'npc':
                    objects = GameNpc.objects.filter(area_name=request.session['area_name'])
                elif request.session['object_name'] == 'chongwu':
                    objects = ChongWu.objects.all()
                elif request.session['object_name'] == 'zuoqi':
                    objects = ZuoQi.objects.all()
                elif request.session['object_name'] == 'item':
                    objects = Item.objects.filter(area_name=request.session['area_name'])
                elif request.session['object_name'] == 'skill':
                    objects = Skill.objects.all()
                else:
                    pass
                neyong = ''
                bh = 0
                for i in objects:
                    bh = bh + 1
                    if request.session['object_z_bh'] == '0':  # 正常显示
                        neyong = '{}{}.<a href="/wap_area_name_list_see/?object_id={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '100':  # <!--放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '107':  # <!--批量放置电脑人物-->
                        neyong = '{}{}.<a href="/wap_area_name_list_npc_count/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '108':  # <!--出售物品-->
                        neyong = '{}{}.<a href="/wap_area_name_list_sell/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '101' or request.session[
                        'object_z_bh'] == '102':  # <!--放置物品-->
                        neyong = '{}{}.<a href="/wap_area_name_list_item/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '103':  # <!--放置技能-->
                        neyong = '{}{}.<a href="/wap_area_name_list_skill/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '104':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '105':  # <!--任务NPC-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?npc_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '106':  # <!--任务物品-->
                        neyong = '{}{}.<a href="/wap_task_attribute/?item_iid={}&object_name=task" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1000':  # <!--选择初始场景入口-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?map_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    elif request.session['object_z_bh'] == '1001':  # <!--选择初始技能-->
                        neyong = '{}{}.<a href="/wap_attribute_basic/?skill_iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                    else:  # <!--地图出口1-4-->
                        neyong = '{}{}.<a href="/wap_area_name_list_export/?iid={}" class="function_button">{}(ID：{})</a><br/>'.format(
                            neyong, bh, i.id, get_tupian(i.name), i.id)
                return render(request, 'wap_area_name_list.html', locals())
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        objects = GameObject(GameMap.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'npc':
        objects = GameObject(GameNpc.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'chongwu':
        objects = GameObject(ChongWu.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'zuoqi':
        objects = GameObject(ZuoQi.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'shibing':
        objects = GameObject(ShiBing.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'item':
        objects = GameObject(Item.objects.get(id=request.session['object_id']))
    elif request.session['object_name'] == 'skill':
        objects = GameObject(Skill.objects.get(id=request.session['object_id']))
    else:
        objects = ''
    # 进入场景加密地图ID
    u = GameObject(Player.objects.get(id=request.session['player_id']))
    map_get_into_see = parameter_create(encryption, value=request.session['object_id'])
    if not objects:
        objects_name = ''
    else:
        objects_name = get_tupian(objects.name)
    return render(request, 'wap_area_name_list_see.html', locals())


# NPC出售物品
def wap_area_name_list_sell(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    t = ImgValue()  # 代表图片
    if request.session['object_z_bh'] == '108':
        request.session['object_name'] = 'npc'
    objects = GameNpc.objects.get(id=request.session['object_id'])
    request.session['area_name'] = objects.area_name
    objects = GameObject(Item.objects.get(id=request.session['object_id']))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 添加出售物品
        if request.GET.get('iid'):
            if SellGoods.objects.filter(npc_id=objects.id, item_id=request.GET.get('iid')).count() == 0:
                SellGoods.objects.create(npc_id=objects.id, item_id=request.GET.get('iid'))
            else:
                messages = '此物品已在出售行列，无需重复添加'
        # 移除物品
        if request.GET.get('remove'):
            messages = '移除出售物品成功{}'.format(
                SellGoods.objects.filter(npc_id=objects.id, item_id=request.GET.get('sell_iid')).count())
            SellGoods.objects.filter(npc_id=objects.id, item_id=request.GET.get('sell_iid')).delete()
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 显示出售物品列表
    neyong = ''
    bh = 0
    sellgoods = SellGoods.objects.filter(npc_id=objects.id)
    for i in sellgoods:
        bh = bh + 1
        if Item.objects.filter(id=i.item_id).count() == 0:
            i.delete()
        else:
            item = Item.objects.get(id=i.item_id)
            neyong = '{}{}.{} <a href="/wap_area_name_list_sell/?remove=1&sell_iid={}">移除</a><br/>'.format(
                neyong, bh, item.name, i.item_id)
    return render(request, 'wap_area_name_list_sell.html', locals())


# 地图/NPC/宠物/物品属性
def wap_area_name_list_attribute(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('create_to_break_off'):
            maps = GameMap.objects.create(name='未定义', desc='未定义', area_name=request.session['area_name'])
            event = Event.objects.create(name='场景创建事件', map_id=maps.id)
            event = Event.objects.create(name='场景查看事件', map_id=maps.id)
            event = Event.objects.create(name='场景进入事件', map_id=maps.id)
            event = Event.objects.create(name='场景离开事件', map_id=maps.id)
            objects = GameMap.objects.get(id=request.session['object_id'])
            if request.GET.get('create_to_break_off') == '1':
                objects.map_upper = maps.id
                objects.save()
                maps.map_lower = objects.id
            elif request.GET.get('create_to_break_off') == '2':
                objects.map_left = maps.id
                objects.save()
                maps.map_right = objects.id
            elif request.GET.get('create_to_break_off') == '3':
                objects.map_right = maps.id
                objects.save()
                maps.map_left = objects.id
            elif request.GET.get('create_to_break_off') == '4':
                objects.map_lower = maps.id
                objects.save()
                maps.map_upper = objects.id
            maps.save()
            messages = '创建新地图链接成功'
            request.session['object_id'] = maps.id
        if request.method == "POST":
            if request.session['object_name'] == 'map':
                objects = GameMap.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'npc':
                objects = GameNpc.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'chongwu':
                objects = ChongWu.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'zuoqi':
                objects = ZuoQi.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'shibing':
                objects = ShiBing.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'item':
                objects = Item.objects.get(id=request.session['object_id'])
            elif request.session['object_name'] == 'skill':
                objects = Skill.objects.get(id=request.session['object_id'])
            else:
                objects = ''
            objects.params = '{}'
            objects.name = request.POST['name']
            objects.desc = request.POST['desc']
            if request.session['object_name'] == 'map' or request.session['object_name'] == 'npc':
                objects.area_name = request.POST['area_name']
            elif request.session['object_name'] == 'item':
                objects.pm_money = request.POST['pm_money']
                objects.money = request.POST['money']
                objects.area_name = request.POST['area_name']
                objects.duixiang = request.POST['duixiang']
                if objects.area_name == '装备' or objects.area_name == '武将装备' or objects.area_name == '士兵装备' or objects.area_name == '坐骑装备' or objects.area_name == '宠物装备':
                    if request.POST.get('type'):
                        objects.type = request.POST['type']
                        type_id = ItemType.objects.get(type=objects.type)
                        objects.type_id = type_id.id
                else:
                    objects.type = 0
            else:
                pass
            if request.session['object_name'] == 'npc':
                if request.POST.get('is_boss'):
                    objects.is_boss = request.POST['is_boss']
                else:
                    objects.is_boss = 0
                if request.POST.get('refresh_time'):
                    objects.refresh_time = request.POST['refresh_time']
                else:
                    objects.refresh_time = 0
            attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
            if request.session['object_name'] != 'zuoqi' and request.session['object_name'] != 'chongwu' and \
                    request.session['object_name'] != 'skill' and request.session['object_name'] != 'shibing':
                objects.area_name = request.POST['area_name']
            for attribute in attributes:
                if int(attribute.built_in) == 1:
                    pass
                else:
                    if int(attribute.type) == 1:
                        if request.POST['{}'.format(attribute.attribute)]:
                            objects.set('{}'.format(attribute.attribute),
                                        request.POST['{}'.format(attribute.attribute)])
                        else:
                            objects.set('{}'.format(attribute.attribute), 0)
                    elif int(attribute.type) == 2:
                        if request.POST['{}'.format(attribute.attribute)]:
                            objects.set('{}'.format(attribute.attribute),
                                        request.POST['{}'.format(attribute.attribute)])
                        else:
                            objects.set('{}'.format(attribute.attribute), 0)
                    elif int(attribute.type) == 3:
                        if request.POST['{}'.format(attribute.attribute)]:
                            objects.set('{}'.format(attribute.attribute),
                                        request.POST['{}'.format(attribute.attribute)])
                        else:
                            objects.set('{}'.format(attribute.attribute), 0)
                    else:
                        pass
            objects.save()
            messages = '保存成功'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        objects = GameMap.objects.get(id=request.session['object_id'])
        object_area = GameMapAreaName.objects.all()
    elif request.session['object_name'] == 'npc':
        objects = GameNpc.objects.get(id=request.session['object_id'])
        object_area = GameMapAreaName.objects.all()
    elif request.session['object_name'] == 'chongwu':
        objects = ChongWu.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'zuoqi':
        objects = ZuoQi.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'shibing':
        objects = ShiBing.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'item':
        objects = Item.objects.get(id=request.session['object_id'])
        object_area = ItemAreaName.objects.all()
        if objects.area_name == '装备' or objects.area_name == '武将装备' or objects.area_name == '士兵装备' or objects.area_name == '坐骑装备' or objects.area_name == '宠物装备':
            object_type = ItemType.objects.filter(item_areaname=objects.area_name).order_by('position')
    elif request.session['object_name'] == 'skill':
        objects = Skill.objects.get(id=request.session['object_id'])
    else:
        objects = ''
    objects = GameObject(objects)
    # 获得全局定义属性
    attributes = Attribute.objects.filter(duixiang=request.session['object_name']).order_by('position')
    # attributes = attributes.iterator()
    attributes = dict.fromkeys(attributes)
    neyong = ''
    name_count = 0
    name_desc = 0
    money_money = 0
    pm_money = 0
    for attribute in attributes:
        neyong = '{}{}:'.format(neyong, attribute.name)
        if attribute.attribute == 'name':
            name_count = 1
            neyong = '{}<textarea name="name"  maxlength="99999" rows="1" cols="40">{}</textarea><br/>'.format(
                neyong, objects.name)
        elif attribute.attribute == 'desc':
            name_desc = 1
            if objects.desc == 0 or objects.desc == '' or objects.desc == '0':
                neyong = '{}<textarea name="desc"  maxlength="99999" rows="4" cols="40"></textarea><br/>'.format(
                    neyong)
            else:
                neyong = '{}<textarea name="desc"  maxlength="99999" rows="4" cols="40">{}</textarea><br/>'.format(
                    neyong, objects.desc)
        elif attribute.attribute == 'money':
            money_money = 1
            neyong = '{}<input  name="money" size="12" value="{}"> {{c.money_name}}'.format(
                neyong, objects.money)
        elif attribute.attribute == 'pm_money':
            pm_money = 1
            neyong = '{}<input  name="pm_money" size="12" value="{}"> 金元'.format(
                neyong, objects.pm_money)
        else:
            if attribute.type == '1':
                if objects.get('{}'.format(attribute.attribute)) == 0:
                    neyong = '{}<input name={} value={} /><br/>'.format(neyong, attribute.attribute, attribute.value)
                else:
                    neyong = '{}<input name={} value={} /><br/>'.format(neyong, attribute.attribute,
                                                                        objects.get('{}'.format(attribute.attribute)))
            elif attribute.type == '2':
                neyong = '{}<select name={}>'.format(neyong, attribute.attribute)
                if attribute.value == '0' or attribute.value == "False" or attribute.value == "false":
                    if int(objects.get('{}'.format(attribute.attribute))) > 0:
                        neyong = '{}<option value = 0 >否</option>'.format(neyong)
                        neyong = '{}<option value = 1 selected="selected">是</option>'.format(neyong)
                    else:
                        neyong = '{}<option value = 0 selected="selected" >否</option>'.format(neyong)
                        neyong = '{}<option value = 1 >是</option>'.format(neyong)
                else:
                    if int(objects.get('{}'.format(attribute.attribute))) <= 0:
                        neyong = '{}<option value = 1 >是</option>'.format(neyong)
                        neyong = '{}<option value = 0 selected="selected" >否</option>'.format(neyong)
                    else:
                        neyong = '{}<option value = 1 selected="selected" >是</option>'.format(neyong)
                        neyong = '{}<option value = 0 >否</option>'.format(neyong)
                neyong = '{}</select><br/>'.format(neyong)
            elif attribute.type == '3':
                neyong = '{}<textarea name={}  maxlength="99999" rows="4" cols="40">{}</textarea><br/>'.format(neyong,
                                                                                                               attribute.attribute,
                                                                                                               objects.get(
                                                                                                                   '{}'.format(
                                                                                                                       attribute.attribute)))
            else:
                pass
            # var = re.findall(r"{{(.*?)}}", neyong)
            # bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
            # neyong = bbb.format(*map(eval, var))
    return render(request, 'wap_area_name_list_attribute.html', locals())


# 定义地图/NPC/宠物/物品操作
def wap_area_name_list_operation(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    operation = Operation.objects.create()
    if request.session['object_name'] == 'map':
        objects = GameMap.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'npc':
        objects = GameNpc.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'chongwu':
        objects = ChongWu.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'zuoqi':
        objects = ZuoQi.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'shibing':
        objects = ShiBing.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'item':
        objects = Item.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'skill':
        objects = Skill.objects.get(id=request.session['object_id'])
    else:
        objects = ''
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('create'):
            operation = Operation.objects.create()
            if request.session['object_name'] == 'map':
                operation.map_id = request.session['object_id']
            elif request.session['object_name'] == 'npc':
                operation.npc_id = request.session['object_id']
            elif request.session['object_name'] == 'chongwu':
                operation.chongwu_id = request.session['object_id']
            elif request.session['object_name'] == 'shibing':
                operation.shibing_id = request.session['object_id']
            elif request.session['object_name'] == 'zuoqi':
                operation.zuoqi_id = request.session['object_id']
            elif request.session['object_name'] == 'item':
                operation.item_id = request.session['object_id']
            elif request.session['object_name'] == 'skill':
                operation.skill_id = request.session['object_id']
            else:
                pass
            operation.content = '文本'
            operation.event = 'None'
            operation.save()
            request.session['operation_id'] = operation.id
            messages = '创建操作成功{}/{}'.format(request.session['object_name'], request.session['object_id'])
            return render(request, 'wap_area_name_list_operation_check.html', locals())
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    if request.session['object_name'] == 'map':
        operations = Operation.objects.filter(map_id=objects.id)
    elif request.session['object_name'] == 'npc':
        operations = Operation.objects.filter(npc_id=objects.id)
    elif request.session['object_name'] == 'chongwu':
        operations = Operation.objects.filter(chongwu_id=objects.id)
    elif request.session['object_name'] == 'zuoqi':
        operations = Operation.objects.filter(zuoqi_id=objects.id)
    elif request.session['object_name'] == 'shibing':
        operations = Operation.objects.filter(shibing_id=objects.id)
    elif request.session['object_name'] == 'item':
        operations = Operation.objects.filter(item_id=objects.id)
    elif request.session['object_name'] == 'skill':
        operations = Operation.objects.filter(skill_id=objects.id)
    else:
        operations = ''
    neyong = ''
    bh = 0
    for operation in operations:
        bh = bh + 1
        neyong = '{}{}.<a href="/wap_area_name_list_operation_check/?ck={}">{}</a><br/>'.format(neyong, bh,
                                                                                                operation.id,
                                                                                                operation.content)
    return render(request, 'wap_area_name_list_operation.html', locals())


# 地图操作查看
def wap_area_name_list_operation_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.session['object_name'] == 'map':
        objects = GameMap.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'npc':
        objects = GameNpc.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'chongwu':
        objects = ChongWu.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'zuoqi':
        objects = ZuoQi.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'shibing':
        objects = ShiBing.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'item':
        objects = Item.objects.get(id=request.session['object_id'])
    elif request.session['object_name'] == 'skill':
        objects = Skill.objects.get(id=request.session['object_id'])
    else:
        objects = ''
    if request.GET.get('ck'):
        request.session['operation_id'] = request.GET.get('ck')
    operation = Operation.objects.get(id=request.session['operation_id'])
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.session['object_name'] == 'map':
                operation.map_id = objects.id
            elif request.session['object_name'] == 'npc':
                operation.npc_id = objects.id
            elif request.session['object_name'] == 'chongwu':
                operation.chongwu_id = objects.id
            elif request.session['object_name'] == 'zuoqi':
                operation.zuoqi_id = objects.id
            elif request.session['object_name'] == 'shibing':
                operation.shibing_id = objects.id
            elif request.session['object_name'] == 'item':
                operation.item_id = objects.id
            elif request.session['object_name'] == 'skill':
                operation.Skill_id = objects.id
            else:
                pass
            operation.code = request.POST['code']
            operation.content = request.POST['content']
            if int(operation.position) == int(request.POST['position']):
                pass
            else:
                operation.position = int(request.POST['position'])
            # 检测显示条件是否为空，为空时附值为True
            if len(request.POST['display']) <= 0:
                operation.display = True
            else:
                operation.display = request.POST['display']
            # 检测内容是否为空，为空时附值为True
            if len(request.POST['content']) <= 0:
                operation.content = '文本'
            else:
                operation.content = request.POST['content']
            operation.save()
            messages = '保存操作成功'
            # 返回地图操作列表
            if request.session['object_name'] == 'map':
                operations = Operation.objects.filter(map_id=objects.id)
            elif request.session['object_name'] == 'npc':
                operations = Operation.objects.filter(npc_id=objects.id)
            elif request.session['object_name'] == 'chongwu':
                operations = Operation.objects.filter(chongwu_id=objects.id)
            elif request.session['object_name'] == 'zuoqi':
                operations = Operation.objects.filter(zuoqi_id=objects.id)
            elif request.session['object_name'] == 'shibing':
                operations = Operation.objects.filter(shibing_id=objects.id)
            elif request.session['object_name'] == 'item':
                operations = Operation.objects.filter(item_id=objects.id)
            elif request.session['object_name'] == 'skill':
                operations = Skill.objects.filter(item_id=objects.id)
            else:
                operations = ''
            neyong = ''
            bh = 0
            for operation in operations:
                bh = bh + 1
                neyong = '{}{}.<a href="/wap_area_name_list_operation_check/?ck={}">{}</a><br/>'.format(neyong, bh,
                                                                                                        operation.id,
                                                                                                        operation.content)
            return render(request, 'wap_area_name_list_operation.html', locals())
        elif request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                # 删除事件
                if operation.event == 'None':
                    pass
                else:
                    event = Event.objects.filter(id=operation.event)
                    event_list = EventList.objects.filter(event=operation.event)
                    event_list.delete()
                    event.delete()
                # 删除操作
                operation.delete()
                messages = '删除操作成功'
                # 返回地图操作列表
                if request.session['object_name'] == 'map':
                    operations = Operation.objects.filter(map_id=objects.id)
                elif request.session['object_name'] == 'npc':
                    operations = Operation.objects.filter(npc_id=objects.id)
                elif request.session['object_name'] == 'chongwu':
                    operations = Operation.objects.filter(chongwu_id=objects.id)
                elif request.session['object_name'] == 'zuoqi':
                    operations = Operation.objects.filter(zuoqi_id=objects.id)
                elif request.session['object_name'] == 'item':
                    operations = Operation.objects.filter(item_id=objects.id)
                elif request.session['object_name'] == 'shibing':
                    operations = Operation.objects.filter(shibing_id=objects.id)
                elif request.session['object_name'] == 'skill':
                    operations = Operation.objects.filter(skill_id=objects.id)
                else:
                    operations = ''
                neyong = ''
                bh = 0
                for operation in operations:
                    bh = bh + 1
                    neyong = '{}{}.<a href="/wap_area_name_list_operation_check/?ck={}">{}</a><br/>'.format(neyong, bh,
                                                                                                            operation.id,
                                                                                                            operation.content)
                return render(request, 'wap_area_name_list_operation.html', locals())
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_area_name_list_operation_check.html', locals())


# 定义地图/NPC/宠物/物品操作事件
def wap_area_name_list_operation_event(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('ck'):
        request.session['operation_id'] = request.GET.get('ck')
    operation = Operation.objects.get(id=request.session['operation_id'])
    if operation.event == 'None':
        pass
    else:
        event = Event.objects.get(id=operation.event)
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 保存事件
        if request.method == "POST":
            # 检测当前事件ID是否存在，存在则更改，否则创建新事件
            if operation.event == '0' or operation.event == 'None' or request.session['event_id'] == 0:
                event = Event.objects.create()
                event.operation = request.session['operation_id']
                event.page_name = 0
            else:
                event = Event.objects.get(id=request.session['event_id'])
            event.code = request.POST['code']
            event.save()
            operation.event = event.id
            operation.save()
            request.session['event_id'] = event.id
            messages = '保存事件成功'
        # 创建事件
        elif request.GET.get('create'):
            request.session['create'] = 0
            # 创建子事件
        elif request.GET.get('create_event'):
            eventlist = EventList.objects.create(event=request.session['event_id'])
            messages = '添加事件步骤成功'
            eventlist.position = EventList.objects.filter(event=request.session['event_id']).count() + 1
            eventlist.save()
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event_id']).extra(
                select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
            # 移动子事件位置
        elif request.GET.get('page'):
            eventlist = EventList.objects.get(id=request.GET.get('iid'))
            a = eventlist.position
            eventlist.position = int(request.GET.get('page'))
            eventlist.save()
            b = a + 1 if a < eventlist.position else a - 1
            messages = '{}移动到{}成功'.format(a, b)
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event']).extra(select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
        # 更改事件
        elif request.GET.get('event_id'):
            event = Event.objects.get(id=operation.event)
            request.session['event_id'] = event.id
        elif request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                event = Event.objects.get(id=request.session['event_id'])
                event_list = EventList.objects.filter(event=request.session['event_id'])
                event_list.delete()
                event.code = ''
                event.delete()
                operation.event = 'None'
                operation.save()
                messages = '删除事件成功'
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        elif request.GET.get('delete_eventlist'):
            if request.GET.get('delete_eventlist') == '0':
                request.session['delete_eventlist'] = 1
                request.session['event_list'] = request.GET.get('iid')
                eventlist = EventList.objects.get(id=request.GET.get('iid'))
            elif request.GET.get('delete_eventlist') == '1':
                request.session['delete_eventlist'] = 0
                event_list = EventList.objects.get(id=request.session['event_list'])
                event_list.delete()
                messages = '删除步骤成功'
            elif request.GET.get('delete_eventlist') == '2':
                request.session['delete_eventlist'] = 0
            else:
                pass
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 返回事件步骤列表
    event_list = ''
    if request.session.get('event_id', '0') != '0':
        event_lists = EventList.objects.filter(event=request.session['event_id']).extra(select={'num': 'position+0'})
        event_lists = event_lists.extra(order_by=["num"])
        bh = 0
        for i in event_lists:
            bh = bh + 1
            if bh != i.position:
                i.position = bh
                i.save()
            event_list = '{}{}.<a href="/wap_area_name_list_operation_event_list/?ck={}&xs=0">修改事件</a> '.format(
                event_list,
                i.position,
                i.id)
            event_list = '{}<a href="/wap_area_name_list_operation_event/?delete_eventlist=0&iid={}">删除</a> '.format(
                event_list, i.id)
            event_list = '{}<a href="/wap_area_name_list_operation_event/?page={}&iid={}">上移</a> '.format(event_list,
                                                                                                            int(
                                                                                                                i.position) - 2,
                                                                                                            i.id)
            event_list = '{}<a href="/wap_area_name_list_operation_event/?page={}&iid={}">下移</a><br/>'.format(
                event_list,
                int(
                    i.position) + 2,
                i.id)
    return render(request, 'wap_area_name_list_operation_event.html', locals())


# 地图/NPC/宠物/物品事件列表
def wap_area_name_list_operation_event_list(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('xs'):
        request.session['event_xs'] = request.GET.get('xs')
    if request.GET.get('key'):
        if request.GET.get('key') == '0':
            request.session['event_key'] = ''
        else:
            request.session['event_key'] = request.GET.get('key')
    if request.GET.get('value'):
        if request.GET.get('value') == '0':
            request.session['event_value'] = ''
        else:
            request.session['event_value'] = request.GET.get('value')
    if request.GET.get('ck'):
        request.session['event_list'] = request.GET.get('ck')
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.method == "POST":
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            eventlist.display = request.POST.get('display')
            eventlist.execute_display = request.POST.get('execute_display')
            eventlist.not_content = request.POST.get('not_content')
            eventlist.content = request.POST.get('content')
            eventlist.code = request.POST.get('code')
            eventlist.save()
            messages = '保存步骤成功'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    if request.session['event_xs'] == '2':
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            if request.GET.get('xs'):
                pass
            else:
                if request.GET.get('attribute_params_key') and request.GET.get('attribute_params_value'):
                    if request.session['event_key'] != '':
                        eventlist.params = ujson.loads(eventlist.params)
                        del eventlist.params[request.session['event_key']]
                        eventlist.save()
                        request.session['event_key'] = ''
                    eventlist = GameObject(eventlist)
                    if request.GET.get('attribute_params_key').split("{"):
                        eventlist.set(request.GET.get('attribute_params_key'),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    else:
                        eventlist.set('{}'.format(request.GET.get('attribute_params_key')),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    eventlist.save()
                    request.session['event_xs'] = '1'
                    messages = '保存成功'
                else:
                    if not request.GET.get('attribute_params_key') and not request.GET.get('attribute_params_value'):
                        request.session['event_xs'] = '1'
                        if request.session['event_key'] != '':
                            eventlist.params = ujson.loads(eventlist.params)
                            del eventlist.params[request.session['event_key']]
                            eventlist.save()
                            request.session['event_key'] = ''
                    elif request.GET.get('attribute_params_key'):
                        messages = '请输入属性值'
                    else:
                        messages = '请输入属性名'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    eventlist = EventList.objects.get(id=request.session['event_list'])
    params = ujson.loads(str(eventlist.params))
    count = len(params)
    if request.session['event_xs'] == '1' and params != '{}':
        params_list = ''
        #        params_code = ''
        #        data1, data2 = [], []
        #       for iiii, j in params.items():
        #            if len(iiii.split(".")) == 3:
        #               data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
        #           else:
        #               data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
        #          data2.append(f'{iiii.split(".")[0]}.save()')
        #      params_code = '\n'.join(data1 + [""] + list(set(data2)))
        for key, value in params.items():
            params_list = """{}<a href="/wap_area_name_list_operation_event_list/?xs=2&key={}&value={}">{} = {}</a><br/>""".format(
                params_list, key, value, key, str(value))
    operation = Operation.objects.get(event=eventlist.event)
    event = Event.objects.get(id=operation.event)
    eventlist = GameObject(eventlist)
    return render(request, 'wap_area_name_list_operation_event_list.html', locals())


# 地图/NPC/宠物/物品定义事件
def wap_area_name_list_event(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.session['object_name'] == 'map':
        events = Event.objects.filter(map_id=request.session['object_id'])
    elif request.session['object_name'] == 'npc':
        events = Event.objects.filter(npc_id=request.session['object_id'])
    elif request.session['object_name'] == 'chongwu':
        events = Event.objects.filter(chongwu_id=request.session['object_id'])
    elif request.session['object_name'] == 'zuoqi':
        events = Event.objects.filter(zuoqi_id=request.session['object_id'])
    elif request.session['object_name'] == 'shibing':
        events = Event.objects.filter(shibing_id=request.session['object_id'])
    elif request.session['object_name'] == 'item':
        events = Event.objects.filter(item_id=request.session['object_id'])
    elif request.session['object_name'] == 'skill':
        events = Event.objects.filter(skill_id=request.session['object_id'])
    else:
        events = ''
    neyong = ''
    for event in events:
        neyong = '{}<a href="/wap_area_name_list_event_check/?ck={}">{}</a><br/>'.format(neyong, event.id, event.name)
    return render(request, 'wap_area_name_list_event.html', locals())


# 定义地图事件查看
def wap_area_name_list_event_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('ck'):
        request.session['event_id'] = request.GET.get('ck')
    event = Event.objects.get(id=request.session['event_id'])
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 保存事件
        if request.method == "POST":
            event.code = request.POST['code']
            event.save()
            messages = '保存成功'
        # 创建子事件
        elif request.GET.get('create_event'):
            eventlist = EventList.objects.create(event=request.session['event_id'])
            messages = '添加事件步骤成功'
            eventlist.position = EventList.objects.filter(event=request.session['event_id']).count() + 1
            eventlist.save()
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event_id']).extra(
                select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
            # 移动子事件位置
        elif request.GET.get('page'):
            eventlist = EventList.objects.get(id=request.GET.get('iid'))
            a = eventlist.position
            eventlist.position = int(request.GET.get('page'))
            eventlist.save()
            b = a + 1 if a < eventlist.position else a - 1
            messages = '{}移动到{}成功'.format(a, b)
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event']).extra(select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
        elif request.GET.get('delete_eventlist'):
            if request.GET.get('delete_eventlist') == '0':
                request.session['delete_eventlist'] = 1
                request.session['event_list'] = request.GET.get('iid')
                eventlist = EventList.objects.get(id=request.GET.get('iid'))
            elif request.GET.get('delete_eventlist') == '1':
                request.session['delete_eventlist'] = 0
                event_list = EventList.objects.get(id=request.session['event_list'])
                event_list.delete()
                messages = '删除步骤成功'
            elif request.GET.get('delete_eventlist') == '2':
                request.session['delete_eventlist'] = 0
            else:
                pass
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 返回事件步骤列表
    event_lists = EventList.objects.filter(event=request.session['event_id']).extra(select={'num': 'position+0'})
    event_lists = event_lists.extra(order_by=["num"])
    bh = 0
    event_list = ''
    for i in event_lists:
        bh = bh + 1
        if bh != i.position:
            i.position = bh
            i.save()
        event_list = '{}{}.<a href="/wap_area_name_list_event_check_list/?ck={}&xs=0">修改事件</a> '.format(event_list,
                                                                                                            i.position,
                                                                                                            i.id)
        event_list = '{}<a href="/wap_area_name_list_event_check/?delete_eventlist=0&iid={}">删除</a> '.format(
            event_list,
            i.id)
        event_list = '{}<a href="/wap_area_name_list_event_check/?page={}&iid={}">上移</a> '.format(event_list,
                                                                                                    int(i.position) - 2,
                                                                                                    i.id)
        event_list = '{}<a href="/wap_area_name_list_event_check/?page={}&iid={}">下移</a><br/>'.format(event_list,
                                                                                                        int(
                                                                                                            i.position) + 2,
                                                                                                        i.id)

    return render(request, 'wap_area_name_list_event_check.html', locals())


# 定义地图事件查看步骤
def wap_area_name_list_event_check_list(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('xs'):
        request.session['event_xs'] = request.GET.get('xs')
    if request.GET.get('key'):
        if request.GET.get('key') == '0':
            request.session['event_key'] = ''
        else:
            request.session['event_key'] = request.GET.get('key')
    if request.GET.get('value'):
        if request.GET.get('value') == '0':
            request.session['event_value'] = ''
        else:
            request.session['event_value'] = request.GET.get('value')
    if request.GET.get('ck'):
        request.session['event_list'] = request.GET.get('ck')
    if request.method == "POST":
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            eventlist.display = request.POST.get('display')
            eventlist.execute_display = request.POST.get('execute_display')
            eventlist.not_content = request.POST.get('not_content')
            eventlist.content = request.POST.get('content')
            eventlist.code = request.POST.get('code')
            eventlist.save()
            messages = '保存步骤成功'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    if request.session['event_xs'] == '2':
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            if request.GET.get('xs'):
                pass
            else:
                if request.GET.get('attribute_params_key') and request.GET.get('attribute_params_value'):
                    if request.session['event_key'] != '':
                        eventlist.params = ujson.loads(eventlist.params)
                        del eventlist.params[request.session['event_key']]
                        eventlist.save()
                        request.session['event_key'] = ''
                    eventlist = GameObject(eventlist)
                    if request.GET.get('attribute_params_key').split("{"):
                        eventlist.set(request.GET.get('attribute_params_key'),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    else:
                        eventlist.set('{}'.format(request.GET.get('attribute_params_key')),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    eventlist.save()
                    request.session['event_xs'] = '1'
                    messages = '保存成功'
                else:
                    if not request.GET.get('attribute_params_key') and not request.GET.get(
                            'attribute_params_value'):
                        request.session['event_xs'] = '1'
                        if request.session['event_key'] != '':
                            eventlist.params = ujson.loads(eventlist.params)
                            del eventlist.params[request.session['event_key']]
                            eventlist.save()
                            request.session['event_key'] = ''
                    elif request.GET.get('attribute_params_key'):
                        messages = '请输入属性值'
                    else:
                        messages = '请输入属性名'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    eventlist = EventList.objects.get(id=request.session['event_list'])
    params = ujson.loads(str(eventlist.params))
    count = len(params)
    if request.session['event_xs'] == '1' and params != '{}':
        params_list = ''
        # params_code = ''
        # data1, data2 = [], []
        # for iiii, j in params.items():
        # if len(iiii.split(".")) == 3:
        #  data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
        # else:
        #  data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
        # data2.append(f'{iiii.split(".")[0]}.save()')
        # params_code = '\n'.join(data1 + [""] + list(set(data2)))
        for key, value in params.items():
            params_list = """{}<a href="/wap_area_name_list_operation_event_list/?xs=2&key={}&value={}">{} = {}</a><br/>""".format(
                params_list, key, value, key, str(value))
    event = Event.objects.get(id=request.session['event_id'])
    return render(request, 'wap_area_name_list_event_check_list.html', locals())


# 定义地图出口
def wap_area_name_list_export(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 接收选择地图出口
        if request.GET.get('iid'):
            request.session['object_id'] = request.session['object_z_id']
            objects = GameMap.objects.get(id=request.session['object_id'])
            request.session['area_name'] = objects.area_name
            messages = '选择出口成功'
            if request.session['object_z_bh'] == '1':  # 保存主地图对应上地图ID
                objects.map_upper = request.GET.get('iid')
                objects.save()
                maps = GameMap.objects.get(id=request.GET.get('iid'))
                if int(maps.map_lower) == 0:  # 链接上的地图加入主地图下链接
                    maps.map_lower = objects.id
                    maps.save()
            elif request.session['object_z_bh'] == '2':  # 保存主地图对应左地图ID
                objects.map_left = request.GET.get('iid')
                objects.save()
                maps = GameMap.objects.get(id=request.GET.get('iid'))
                if int(maps.map_right) == 0:  # 链接上的地图加入主地图右链接
                    maps.map_right = objects.id
                    maps.save()
            elif request.session['object_z_bh'] == '3':  # 保存主地图对应右地图ID
                objects.map_right = request.GET.get('iid')
                objects.save()
                maps = GameMap.objects.get(id=request.GET.get('iid'))
                if int(maps.map_left) == 0:  # 链接上的地图加入主地图左链接
                    maps.map_left = objects.id
                    maps.save()
            elif request.session['object_z_bh'] == '4':  # 保存主地图对应下地图ID
                objects.map_lower = request.GET.get('iid')
                objects.save()
                maps = GameMap.objects.get(id=request.GET.get('iid'))
                if int(maps.map_upper) == 0:  # 链接上的地图加入主地图上链接
                    maps.map_upper = objects.id
                    maps.save()
            else:
                pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    objects = GameMap.objects.get(id=request.session['object_id'])
    request.session['object_z_id'] = objects.id
    if user.is_designer == 'True':
        # 断开地图链接
        if request.GET.get('to_break_off') == '1':
            objects.map_upper = 0
            messages = '断开链接成功'
        elif request.GET.get('to_break_off') == '2':
            objects.map_left = 0
            messages = '断开链接成功'
        elif request.GET.get('to_break_off') == '3':
            objects.map_right = 0
            messages = '断开链接成功'
        elif request.GET.get('to_break_off') == '4':
            objects.map_lower = 0
            messages = '断开链接成功'
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    neyong = ''
    if int(objects.map_upper) > 0 and GameMap.objects.filter(id=objects.map_upper).count() == 1:
        maps = GameMap.objects.get(id=objects.map_upper)
        neyong = '{}上(北):<a href="/wap_area_name_list_see/?object_id={}" >{}</a> <a href="/wap_area_name_list_export/?to_break_off=1">断开</a><br/>'.format(
            neyong, maps.id, maps.name)
    else:
        objects.map_upper = 0
        neyong = '{}上(北):<a href="/wap_area_name/?object_name=map&object_z_bh=1">选择</a> <a href="/wap_area_name_list_attribute/?create_to_break_off=1">创建</a><br/>'.format(
            neyong)
    if int(objects.map_left) > 0 and GameMap.objects.filter(id=objects.map_left).count() == 1:
        maps = GameMap.objects.get(id=objects.map_left)
        neyong = '{}左(西):<a href="/wap_area_name_list_see/?object_id={}" >{}</a> <a href="/wap_area_name_list_export/?to_break_off=2">断开</a><br/>'.format(
            neyong, maps.id, maps.name)
    else:
        objects.map_left = 0
        neyong = '{}左(西):<a href="/wap_area_name/?object_name=map&object_z_bh=2">选择</a> <a href="/wap_area_name_list_attribute/?create_to_break_off=2">创建</a><br/>'.format(
            neyong)
    if int(objects.map_right) > 0 and GameMap.objects.filter(id=objects.map_right).count() == 1:
        maps = GameMap.objects.get(id=objects.map_right)
        neyong = '{}右(东):<a href="/wap_area_name_list_see/?object_id={}" >{}</a> <a href="/wap_area_name_list_export/?to_break_off=3">断开</a><br/>'.format(
            neyong, maps.id, maps.name)
    else:
        objects.map_right = 0
        neyong = '{}右(东):<a href="/wap_area_name/?object_name=map&object_z_bh=3">选择</a> <a href="/wap_area_name_list_attribute/?create_to_break_off=3">创建</a><br/>'.format(
            neyong)
    if int(objects.map_lower) > 0 and GameMap.objects.filter(id=objects.map_lower).count() == 1:
        maps = GameMap.objects.get(id=objects.map_lower)
        neyong = '{}下(南):<a href="/wap_area_name_list_see/?object_id={}" >{}</a> <a href="/wap_area_name_list_export/?to_break_off=4">断开</a><br/>'.format(
            neyong, maps.id, maps.name)
    else:
        objects.map_lower = 0
        neyong = '{}下(南):<a href="/wap_area_name/?object_name=map&object_z_bh=4">选择</a> <a href="/wap_area_name_list_attribute/?create_to_break_off=4">创建</a><br/>'.format(
            neyong)
    objects.save()

    return render(request, 'wap_area_name_list_export.html', locals())


# 放置电脑人物
def wap_area_name_list_npc(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 移除放置地图电脑人物
        if request.GET.get('remove_id'):
            npcs = GameMapPlaceNpc.objects.filter(map_id=request.session['object_id'],
                                                  npc_id=request.GET.get('remove_id'))
            npcs.delete()
            messages = '移除成功'
        # 接收选择放置的电脑人物
        if request.GET.get('iid'):
            request.session['object_id'] = request.session['object_z_id']
            objects = GameMap.objects.get(id=request.session['object_id'])
            request.session['area_name'] = objects.area_name
            if request.session['object_z_bh'] == '100':  # 保存主地图对应上地图ID
                if GameMapPlaceNpc.objects.filter(map_id=request.session['object_id'],
                                                  npc_id=request.GET.get('iid')).count() == 0:
                    placenpc = GameMapPlaceNpc.objects.create()
                    placenpc.map_id = objects.id
                    placenpc.npc_id = request.GET.get('iid')
                    placenpc.npc_code = 1
                    placenpc.save()
                    messages = '放置电脑人物成功'
                else:
                    messages = '放置失败，该电脑人物重复放置'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 放置电脑人物显示
    if GameMapPlaceNpc.objects.filter(map_id=request.session['object_id']).count() == 0:
        pass
    else:
        neyong = ''
        placenpcs = GameMapPlaceNpc.objects.filter(map_id=request.session['object_id'])
        for i in placenpcs:
            if GameNpc.objects.filter(id=i.npc_id).count() == 0:
                i.delete()
            else:
                npcs = GameNpc.objects.get(id=i.npc_id)
                neyong = '{}<a href = "/wap_area_name_list_npc_check/?iid={}" >{}({})</a> <a href = "/wap_area_name_list_npc/?remove_id={}" >移除</a><br/>'.format(
                    neyong, i.npc_id,
                    npcs.name,
                    i.npc_code, npcs.id)
    objects = GameMap.objects.get(id=request.session['object_id'])
    request.session['area_name'] = objects.area_name
    request.session['object_name'] = 'map'
    request.session['object_z_id'] = request.session['object_id']  # 放置电脑人物时地图主要ID
    return render(request, 'wap_area_name_list_npc.html', locals())


# 放置电脑人物公式
def wap_area_name_list_npc_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    # 接收电脑人物放置数量代码
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    npcs = GameNpc.objects.get(id=request.session['object_ck_id'])
    objects = GameMap.objects.get(id=request.session['object_id'])
    placenpc = GameMapPlaceNpc.objects.get(map_id=objects.id, npc_id=npcs.id)
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['npc_code']:
                if request.POST['npc_code'] == '0':
                    messages = '输入数量不能为0'
                else:
                    placenpc = GameMapPlaceNpc.objects.get(map_id=objects.id, npc_id=npcs.id)
                    placenpc.npc_code = request.POST['npc_code']
                    placenpc.save()
                    messages = '修改成功'
            else:
                messages = '请输入数量或者代码'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_area_name_list_npc_check.html', locals())


# 放置物品
def wap_area_name_list_item(request):
    t = ImgValue()  # 代表图片
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.session['object_z_bh'] == '101':
        request.session['object_name'] = 'map'
    elif request.session['object_z_bh'] == '102':
        request.session['object_name'] = 'npc'
    else:
        pass
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 移除放置地图物品
        if request.GET.get('remove_id') and request.session['object_name'] == 'map':
            items = GameMapPlaceItem.objects.filter(map_id=request.session['object_id'],
                                                    item_id=request.GET.get('remove_id'))
            items.delete()
            messages = '移除成功'
        if request.GET.get('remove_id') and request.session['object_name'] == 'npc':
            items = GameMapPlaceItem.objects.filter(npc_id=request.session['object_id'],
                                                    item_id=request.GET.get('remove_id'))
            items.delete()
            messages = '移除成功'
        # 接收选择放置的物品
        if request.GET.get('iid'):
            request.session['object_id'] = request.session['object_z_id']
            if request.session['object_z_bh'] == '101':
                objects = GameMap.objects.get(id=request.session['object_id'])
                request.session['object_name'] = 'map'
            elif request.session['object_z_bh'] == '102':
                request.session['object_name'] = 'npc'
                objects = GameNpc.objects.get(id=request.session['object_id'])
            else:
                objects = ''
            request.session['area_name'] = objects.area_name
            if request.session['object_z_bh'] == '101':  # 保存主地图对应上地图ID
                if GameMapPlaceItem.objects.filter(map_id=request.session['object_id'],
                                                   item_id=request.GET.get('iid')).count() == 0:
                    placenpc = GameMapPlaceItem.objects.create()
                    placenpc.map_id = objects.id
                    placenpc.item_id = request.GET.get('iid')
                    placenpc.item_code = 1
                    placenpc.save()
                    messages = '放置物品成功'
                else:
                    messages = '放置失败，该物品重复放置'
            elif request.session['object_z_bh'] == '102':  # 保存主地图对应上NPC_ID
                if GameMapPlaceItem.objects.filter(npc_id=request.session['object_id'],
                                                   item_id=request.GET.get('iid')).count() == 0:
                    placenpc = GameMapPlaceItem.objects.create()
                    placenpc.npc_id = objects.id
                    placenpc.item_id = request.GET.get('iid')
                    placenpc.item_code = 1
                    placenpc.save()
                    messages = '放置掉落物品成功'
                else:
                    messages = '放置失败，该掉落物品重复放置'
            else:
                pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 放置物品显示
    if request.session['object_name'] == 'map':
        if GameMapPlaceItem.objects.filter(map_id=request.session['object_id']).count() == 0:
            pass
        else:
            neyong = ''
            placenpcs = GameMapPlaceItem.objects.filter(map_id=request.session['object_id'])
            for i in placenpcs:
                if Item.objects.filter(id=i.item_id).count() == 0:
                    i.delete()
                else:
                    items = Item.objects.get(id=i.item_id)
                    neyong = '{}<a href = "/wap_area_name_list_item_check/?iid={}" >{}({})</a> <a href = "/wap_area_name_list_item/?remove_id={}" >移除</a><br/>'.format(
                        neyong, i.item_id,
                        items.name,
                        i.item_code, items.id)
        objects = GameMap.objects.get(id=request.session['object_id'])
        request.session['area_name'] = objects.area_name
        request.session['object_name'] = 'map'
        request.session['object_z_id'] = request.session['object_id']  # 放置物品时地图主要ID
    elif request.session['object_name'] == 'npc':
        if GameMapPlaceItem.objects.filter(npc_id=request.session['object_id']).count() == 0:
            pass
        else:
            neyong = ''
            placenpcs = GameMapPlaceItem.objects.filter(npc_id=request.session['object_id'])
            for i in placenpcs:
                if Item.objects.filter(id=i.item_id).count() == 0:
                    i.delete()
                else:
                    items = Item.objects.get(id=i.item_id)
                    neyong = '{}<a href = "/wap_area_name_list_item_check/?iid={}" >{}({})</a> <a href = "/wap_area_name_list_item/?remove_id={}" >移除</a><br/>'.format(
                        neyong, i.item_id,
                        get_tupian(items.name),
                        i.item_code, items.id)
        objects = GameNpc.objects.get(id=request.session['object_id'])
        request.session['area_name'] = objects.area_name
        request.session['object_name'] = 'npc'
        request.session['object_z_id'] = request.session['object_id']  # 放置物品时地图主要ID
    else:
        objects = ''
    if request.method == "POST":
        objects.set('mucai_expression', request.POST['mucai_expression'])
        objects.set('liangcao_expression', request.POST['liangcao_expression'])
        objects.set('shiliao_expression', request.POST['shiliao_expression'])
        objects.set('shengtie_expression', request.POST['shengtie_expression'])
        objects.set('exp_expression', request.POST['exp_expression'])
        objects.set('money_expression', request.POST['money_expression'])
        objects.set('lingqi_expression', request.POST['lingqi_expression'])
        objects.set('is_drop', request.POST['is_drop'])
        objects.save()
        messages = '保存成功'
    objects = GameObject(objects)
    return render(request, 'wap_area_name_list_item.html', locals())


# 放置技能
def wap_area_name_list_skill(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.session['object_z_bh'] == '103':
        request.session['object_name'] = 'npc'
    else:
        pass
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 移除放置技能
        if request.GET.get('remove_id') and request.session['object_name'] == 'npc':
            skill = GameMapPlaceSkill.objects.filter(npc_id=request.session['object_id'],
                                                     skill_id=request.GET.get('remove_id'))
            skill.delete()
            messages = '移除技能成功'
        # 接收选择放置的物品
        if request.GET.get('iid'):
            request.session['object_id'] = request.session['object_z_id']
            if request.session['object_z_bh'] == '103':
                request.session['object_name'] = 'npc'
                objects = GameNpc.objects.get(id=request.session['object_id'])
            else:
                objects = ''
            request.session['area_name'] = objects.area_name
            if request.session['object_z_bh'] == '103':  # 保存主地图对应上NPC_ID
                if GameMapPlaceSkill.objects.filter(npc_id=request.session['object_id'],
                                                    skill_id=request.GET.get('iid')).count() == 0:
                    placenpc = GameMapPlaceSkill.objects.create()
                    placenpc.npc_id = objects.id
                    placenpc.skill_id = request.GET.get('iid')
                    placenpc.save()
                    messages = '放置技能成功'
                else:
                    messages = '放置技能失败，技能重复放置'
            else:
                pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 放置技能显示
    if request.session['object_name'] == 'npc':
        if GameMapPlaceSkill.objects.filter(npc_id=request.session['object_id']).count() == 0:
            pass
        else:
            neyong = ''
            placenpcs = GameMapPlaceSkill.objects.filter(npc_id=request.session['object_id'])
            for i in placenpcs:
                if Skill.objects.filter(id=i.skill_id).count() == 0:
                    i.delete()
                else:
                    skills = Skill.objects.get(id=i.skill_id)
                    neyong = '{}{}(id:{}) <a href = "/wap_area_name_list_skill/?remove_id={}" >移除</a><br/>'.format(
                        neyong, skills.name, skills.id, skills.id)
        objects = GameNpc.objects.get(id=request.session['object_id'])
        request.session['area_name'] = objects.area_name
        request.session['object_name'] = 'npc'
        request.session['object_z_id'] = request.session['object_id']  # 放置物品时地图主要ID
    else:
        pass
    return render(request, 'wap_area_name_list_skill.html', locals())


# 放置物品公式
def wap_area_name_list_item_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    # 接收物品放置数量代码
    if request.GET.get('iid'):
        request.session['object_ck_id'] = request.GET.get('iid')
    items = Item.objects.get(id=request.session['object_ck_id'])
    if request.session['object_name'] == 'map':
        objects = GameMap.objects.get(id=request.session['object_id'])
        placenpc = GameMapPlaceItem.objects.get(map_id=objects.id, item_id=items.id)
    elif request.session['object_name'] == 'npc':
        objects = GameNpc.objects.get(id=request.session['object_id'])
        placenpc = GameMapPlaceItem.objects.get(npc_id=objects.id, item_id=items.id)
    else:
        objects = ''
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['item_code']:
                if request.POST['item_code'] == '0':
                    messages = '输入数量不能为0'
                else:
                    if request.session['object_name'] == 'map':
                        placenpc = GameMapPlaceItem.objects.get(map_id=objects.id, item_id=items.id)
                    else:
                        placenpc = GameMapPlaceItem.objects.get(npc_id=objects.id, item_id=items.id)
                    placenpc.item_code = request.POST['item_code']
                    placenpc.save()
                    messages = '修改成功'
            else:
                messages = '请输入数量或者代码'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_area_name_list_item_check.html', locals())


# --------------------定义全局属性----------------------------
def wap_attribute(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    return render(request, 'wap_attribute.html', locals())


# 定义地图全局属性
def wap_attribute_def(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    if request.GET.get('position'):
        objects = Attribute.objects.get(id=request.GET.get('iid'))
        objects.position = int(request.GET.get('position'))
        objects.save()
        messages = '移动位置成功'
    attributes = Attribute.objects.filter(duixiang=request.session['object_name']).extra(select={'num': 'position+0'})
    attributes = attributes.extra(order_by=["num"])
    # attributes = dict.fromkeys(attributes)
    neyong = ''
    bh = 0
    for i in attributes:
        bh = bh + 1
        i.position = bh
        i.save()
        neyong = '{}{}.<a href="/wap_attribute_def_check/?iid={}">{}[{}]</a> [ '.format(neyong, i.position, i.id,
                                                                                        i.name, i.attribute)
        if bh == 1:
            neyong = '{}上移 '.format(neyong)
        else:
            neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">上移</a> '.format(neyong, i.id, bh - 2)
        if bh == Attribute.objects.filter(duixiang=request.session['object_name']).count():
            neyong = '{}下移'.format(neyong)
        else:
            neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">下移</a>'.format(neyong, i.id, bh + 2)
        neyong = '{} ]<br/>'.format(neyong)
    return render(request, 'wap_attribute_def.html', locals())


# 添加地图全局属性
def wap_attribute_def_create(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if len(request.POST['name']) > 0:
                if len(request.POST['attribute']):
                    if Attribute.objects.filter(attribute=request.POST['attribute'],
                                                duixiang=request.session['object_name']).count() == 0:
                        if len(request.POST['value']):
                            count = Attribute.objects.filter(duixiang=request.session['object_name']).count()
                            attribute = Attribute.objects.create()
                            attribute.name = request.POST['name']
                            attribute.duixiang = request.session['object_name']
                            attribute.attribute = request.POST['attribute']
                            attribute.value = request.POST['value']
                            attribute.type = int(request.POST['type'])
                            attribute.position = int(count) + 1
                            attribute.save()
                            messages = '添加属性成功'
                            attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                            attributes = Attribute.objects.filter(duixiang=request.session['object_name']).extra(
                                select={'num': 'position+0'})
                            attributes = attributes.extra(order_by=["num"])
                            # attributes = dict.fromkeys(attributes)
                            neyong = ''
                            bh = 0
                            for i in attributes:
                                bh = bh + 1
                                i.position = bh
                                i.save()
                                neyong = '{}{}.<a href="/wap_attribute_def_check/?iid={}">{}[{}]</a> [ '.format(neyong,
                                                                                                                i.position,
                                                                                                                i.id,
                                                                                                                i.name,
                                                                                                                i.attribute)
                                if bh == 1:
                                    neyong = '{}上移 '.format(neyong)
                                else:
                                    neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">上移</a> '.format(
                                        neyong,
                                        i.id,
                                        bh - 2)
                                if bh == Attribute.objects.filter(duixiang=request.session['object_name']).count():
                                    neyong = '{}下移'.format(neyong)
                                else:
                                    neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">下移</a>'.format(
                                        neyong,
                                        i.id,
                                        bh + 2)
                                neyong = '{} ]<br/>'.format(neyong)
                            return render(request, 'wap_attribute_def.html', locals())
                        else:
                            messages = '请输入初始值，可以是0'
                    else:
                        messages = '属性重复，请定义其它属性'
                else:
                    messages = '请输入属性名'
            else:
                messages = '请输入属性名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_attribute_def_create.html', locals())


# 查看地图全局属性
def wap_attribute_def_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('iid'):
        request.session['iid'] = request.GET.get('iid')
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        # 删除地图属性
        if request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                map_attribute = Attribute.objects.get(id=request.session['iid'])
                map_attribute.delete()
                messages = '删除属性成功'
                attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                attributes = Attribute.objects.filter(duixiang=request.session['object_name']).extra(
                    select={'num': 'position+0'})
                attributes = attributes.extra(order_by=["num"])
                # attributes = dict.fromkeys(attributes)
                neyong = ''
                bh = 0
                for i in attributes:
                    bh = bh + 1
                    i.position = bh
                    i.save()
                    neyong = '{}{}.<a href="/wap_attribute_def_check/?iid={}">{}[{}]</a> [ '.format(neyong,
                                                                                                    i.position,
                                                                                                    i.id,
                                                                                                    i.name,
                                                                                                    i.attribute)
                    if bh == 1:
                        neyong = '{}上移 '.format(neyong)
                    else:
                        neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">上移</a> '.format(neyong,
                                                                                                       i.id,
                                                                                                       bh - 2)
                    if bh == Attribute.objects.filter(duixiang=request.session['object_name']).count():
                        neyong = '{}下移'.format(neyong)
                    else:
                        neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">下移</a>'.format(neyong,
                                                                                                      i.id,
                                                                                                      bh + 2)
                    neyong = '{} ]<br/>'.format(neyong)
                return render(request, 'wap_attribute_def.html', locals())
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        elif request.method == "POST":
            if len(request.POST['name']) > 0:
                if len(request.POST['attribute']):
                    attribute = Attribute.objects.get(id=request.session['iid'])
                    if Attribute.objects.filter(attribute=request.POST['attribute'], duixiang=request.session[
                        'object_name']).count() == 0 or attribute.attribute == request.POST['attribute']:
                        if len(request.POST['value']):
                            attribute.name = request.POST['name']
                            attribute.attribute = request.POST['attribute']
                            attribute.value = request.POST['value']
                            attribute.save()
                            messages = '保存属性成功'
                            attributes = Attribute.objects.filter(duixiang=request.session['object_name'])
                            attributes = Attribute.objects.filter(duixiang=request.session['object_name']).extra(
                                select={'num': 'position+0'})
                            attributes = attributes.extra(order_by=["num"])
                            # attributes = dict.fromkeys(attributes)
                            neyong = ''
                            bh = 0
                            for i in attributes:
                                bh = bh + 1
                                i.position = bh
                                i.save()
                                neyong = '{}{}.<a href="/wap_attribute_def_check/?iid={}">{}[{}]</a> [ '.format(neyong,
                                                                                                                i.position,
                                                                                                                i.id,
                                                                                                                i.name,
                                                                                                                i.attribute)
                                if bh == 1:
                                    neyong = '{}上移 '.format(neyong)
                                else:
                                    neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">上移</a> '.format(
                                        neyong,
                                        i.id,
                                        bh - 2)
                                if bh == Attribute.objects.filter(duixiang=request.session['object_name']).count():
                                    neyong = '{}下移'.format(neyong)
                                else:
                                    neyong = '{}<a href="/wap_attribute_def/?iid={}&position={}">下移</a>'.format(
                                        neyong,
                                        i.id,
                                        bh + 2)
                                neyong = '{} ]<br/>'.format(neyong)
                            return render(request, 'wap_attribute_def.html', locals())
                        else:
                            messages = '请输入初始值，可以是0'
                    else:
                        messages = '属性重复，请定义其它属性'
                else:
                    messages = '请输入属性名'
            else:
                messages = '请输入属性名'
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    attribute = Attribute.objects.get(id=request.session['iid'])
    return render(request, 'wap_attribute_def_check.html', locals())


# --------------------定义页面模板----------------------------
def wap_page(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    objects = PageName.objects.all().order_by('id')
    return render(request, 'wap_page.html', locals())


# 创建模板
def wap_page_create(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['page_name']:
                # 当此模板名称不重复时创建新模板
                if PageName.objects.filter(page_name=request.POST['page_name']).count() == 0:
                    if PageName.objects.all().count() == 0:
                        count = 0
                    else:
                        count = PageName.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if PageName.objects.all().count() == count:
                        page = PageName.objects.create(page_name=request.POST['page_name'])
                    else:
                        page_all = PageName.objects.all()
                        bh = 0
                        for pages in page_all:
                            bh = bh + 1
                            if int(pages.id) == bh:
                                pass
                            else:
                                pages = PageName.objects.create(id=bh, page_name=request.POST['page_name'])
                                break
                    messages = '创建模板:{}成功'.format(request.POST['page_name'])
                    # 重新获得所有模板名称并返回
                    objects = PageName.objects.all()
                    return render(request, 'wap_page.html', locals())
                else:
                    messages = '创建失败:{}模板名重复'.format(request.POST['page_name'])
            else:
                messages = '请正确输入模板名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_page_create.html', locals())


# 模板内容
def wap_page_list(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    userss = User.objects.get(name=user_name())
    user = GameObject(User.objects.get(id=request.session['user_id']))
    messages = ''
    if request.GET.get('page_name'):
        request.session['page_name'] = request.GET.get('page_name')
        if PageName.objects.filter(page_name=request.session['page_name']).count() == 0:
            PageName.objects.create(page_name=request.session['page_name'])
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['delete_page_list']:
                if request.POST['delete_page_list'] == 'yes':
                    operation = Operation.objects.filter(page_name=request.session['page_name']).extra(
                        select={'num': 'position+0'})
                    operation = operation.extra(order_by=["num"])
                    for i in operation:
                        if i.event == 'None' or i.event == 0 or i.event == '0':
                            pass
                        else:
                            if EventList.objects.filter(event=i.event).count() == 0:
                                pass
                            else:
                                event = Event.objects.get(id=i.event)
                                event.delete()
                            if EventList.objects.filter(event=i.event).count() == 0:
                                pass
                            else:
                                eventlists = EventList.objects.filter(event=i.event)
                                for eventlist in eventlists:
                                    eventlist.delete()
                        i.delete()
                    messages = '清空模板成功'
                else:
                    messages = '输入"yes"小写可清空此模板'
            else:
                messages = '输入"yes"小写可清空此模板'
        if request.GET.get('delete'):
            if Operation.objects.filter(page_name=request.session['page_name']).count() == 0:
                page_name = PageName.objects.get(page_name=request.session['page_name'])
                page_name.delete()
                messages = '删除模板成功'
                objects = PageName.objects.all()
                return render(request, 'wap_page.html', locals())
            else:
                messages = '请先清空模板里面所有内容'
        if request.GET.get('is_kill'):
            if request.GET.get('is_kill') == '0':
                if request.session['page_name'] == '场景':
                    c.set('changjing', 0)
                elif request.session['page_name'] == '状态':
                    c.set('zhuangtai', 0)
                else:
                    pass
                messages = '禁用模板成功'
            else:
                if request.session['page_name'] == '场景':
                    c.set('changjing', 1)
                elif request.session['page_name'] == '状态':
                    c.set('zhuangtai', 1)
                else:
                    pass
                messages = '启用模板成功'
            c.save()
    else:
        messages = '你还不是设计者'

    if request.GET.get('page_name'):
        request.session['page_name'] = request.GET.get('page_name')
    # 返回模板列表
    operation = Operation.objects.filter(page_name=request.session['page_name']).extra(select={'num': 'position+0'})
    operation = operation.extra(order_by=["num"])
    bh = 0
    neyong = ''
    if request.GET.get('update'):
        if user.is_designer == 'True':
            messages = '{}所有玩家模板缓存更新成功'.format(messages)
            c.set('page_update', 1)
            c.save()
        else:
            messages = '你还不是设计者'
    neyong = """{}<a href="/wap_page_list/?update=1&sj={}">更新所有玩家模板缓存</a><br/>""".format(neyong, sj())
    for i in operation:
        if int(i.is_input) == 2:
            neyong = '''
            {}＜style＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/style＞<br/>
            '''.format(neyong, i.id, i.position, i.content)
        elif int(i.is_input) == 3:
            neyong = '''
            {}＜script＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/script＞<br/>
            '''.format(neyong, i.id, i.position, i.content)
        else:
            neyong = '''
            {}<a href="/wap_page_list_check/?ck={}">{}.{}</a>
            '''.format(neyong, i.id, i.position, i.content)
    return render(request, 'wap_page_list.html', locals())


# 导出模板数据
def wap_page_list_export(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    neyong_export = ''
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('page_list_export'):
            request.session['object_ck_id'] = request.GET.get('page_list_export')
        if request.session['object_ck_id'] == '1':
            operation = Operation.objects.filter(page_name=request.session['page_name']).extra(
                select={'num': 'position+0'})
            operation = operation.extra(order_by=["num"])
            neyong_export = ''
            for i in operation:
                neyong_export = '''{}operation = Operation.objects.create()
operation.code = """{}"""
operation.content = """{}"""
operation.display = """{}"""
operation.page_name = request.session['page_name']
operation.map_id = """None"""
operation.npc_id = """None"""
operation.skill_id = """None"""
operation.item_id = """None"""
operation.chongwu_id = """None"""
operation.zuoqi_id = """None"""
operation.position = """{}"""
operation.event = """None"""
operation.is_input = """{}"""
'''.format(neyong_export, i.code, i.content, i.display, i.position, i.is_input)
                if i.event != 'None':
                    event = Event.objects.get(id=i.event)
                    neyong_export = '''{}event = Event.objects.create()
operation.event = event.id
event.page_name = request.session['page_name']
event.name = """None"""
event.operation = operation.id
event.map_id = """None"""
event.npc_id = """None"""
event.chongwu_id = """None"""
event.zuoqi_id = """None"""
event.item_id = """None"""
event.skill_id = """None"""
event.code = """{}"""
event.save()
'''.format(neyong_export, event.code)
                    if EventList.objects.filter(event=i.event).count() == 0:
                        pass
                    else:
                        eventLists = EventList.objects.filter(event=i.event)
                        for eventlist in eventLists:
                            neyong_export = '''{}eventlist = EventList.objects.create()
eventlist.event = event.id
eventlist.event_all = """None"""
eventlist.display = """{}"""
eventlist.execute_display = """{}"""
eventlist.content = """{}"""
eventlist.not_content = """{}"""
eventlist.code = """{}"""
eventlist.position = """{}"""
eventlist.save()
'''.format(neyong_export, eventlist.display, eventlist.execute_display,
           eventlist.content, eventlist.not_content, eventlist.code,
           eventlist.position)
                neyong_export = '''{}operation.save()
'''.format(neyong_export)
        elif request.session['object_ck_id'] == '2':
            neyong_export = ''
            neyong_export = '''{}# 导入此数据将会清除掉当前模板所有内容
# 清除模板
page_name = PageName.objects.all()
page_name.delete()
# 清除模板内操作及事件
operation = Operation.objects.exclude(page_name='None')
for i in operation:
    if i.event != 'None':
        if Event.objects.filter(id=i.event).count() > 0:
            event = Event.objects.filter(id=i.event)
            for ii in event:
                ii.delete()
        if EventList.objects.filter(event=i.event).count() > 0:
            eventlist = EventList.objects.filter(event=i.event)
            for iii in eventlist:
                iii.delete()
    i.delete()
'''.format(neyong_export)
            page_name = PageName.objects.all()
            for i in page_name:
                neyong_export = '''{}#创建模板
page_name = PageName.objects.create(page_name='{}')
'''.format(neyong_export, i.page_name)
                if i.page_name != '交易行物品列表' and i.page_name != '物品搜索列表' and i.page_name != '交易行' and i.page_name != '交易' and i.page_name != '出售物品' and i.page_name != '排行榜' and i.page_name != '帮派列表' and i.page_name != '排行列表' and i.page_name != '帮派信息' and i.page_name != '帮派成员' and i.page_name != '帮派任命':
                    operation = Operation.objects.filter(page_name=i.page_name).extra(select={'num': 'position+0'})
                    operation = operation.extra(order_by=["num"])
                    for ii in operation:
                        neyong_export = '''{}operation = Operation.objects.create()
operation.code = """{}"""
operation.content = """{}"""
operation.display = """{}"""
operation.page_name = """{}"""
operation.map_id = """None"""
operation.npc_id = """None"""
operation.skill_id = """None"""
operation.item_id = """None"""
operation.chongwu_id = """None"""
operation.zuoqi_id = """None"""
operation.position = """{}"""
operation.event = """None"""
operation.is_input = """{}"""
'''.format(neyong_export, ii.code, ii.content, ii.display, i.page_name, ii.position, ii.is_input)
                        if ii.event != 'None' and ii.event != 0 and ii.event != '0':
                            event = Event.objects.get(id=ii.event)
                            neyong_export = '''{}event = Event.objects.create()
operation.event = event.id
event.page_name = """{}"""
event.name = """None"""
event.operation = operation.id
event.map_id = """None"""
event.npc_id = """None"""
event.chongwu_id = """None"""
event.zuoqi_id = """None"""
event.item_id = """None"""
event.skill_id = """None"""
event.code = """{}"""
event.save()
'''.format(neyong_export, i.page_name, event.code)
                            if EventList.objects.filter(event=ii.event).count() == 0:
                                pass
                            else:
                                eventLists = EventList.objects.filter(event=ii.event)
                                for eventlist in eventLists:
                                    neyong_export = '''{}eventlist = EventList.objects.create()
eventlist.event = event.id
eventlist.event_all = """None"""
eventlist.display = """{}"""
eventlist.execute_display = """{}"""
eventlist.content = """{}"""
eventlist.not_content = """{}"""
eventlist.code = """{}"""
eventlist.position = """{}"""
eventlist.save()
'''.format(neyong_export, eventlist.display, eventlist.execute_display, eventlist.content,
           eventlist.not_content, eventlist.code, eventlist.position)
                        neyong_export = '''{}operation.save()
'''.format(neyong_export)

        else:
            pass
        #  neyong_export = re.sub('\s+', " ", neyong_export) # 去掉换行符，回车键
    else:
        messages = '你还不是设计'
        neyong = '你想要看什么呢，自己动手丰衣足食'
    return render(request, 'wap_page_list_export.html', locals())


# 导出模板数据
def wap_page_list_import(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('page_list_import'):
        request.session['object_ck_id'] = request.GET.get('page_list_import')
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.session['object_ck_id'] == '1':
                if Operation.objects.filter(page_name=request.session['page_name']).count() == 0:
                    if request.POST['code']:
                        exec(request.POST['code'])
                        messages = '数据导入成功'
                else:
                    messages = '空模板才能进行模板数据导入'
            elif request.session['object_ck_id'] == '2':
                if request.POST['code']:
                    exec(request.POST['code'])
                    messages = '数据导入成功'
            else:
                pass
    else:
        messages = '你还不是设计'
    return render(request, 'wap_page_list_import.html', locals())


# 导出/导入操作数据
def wap_operation_import(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    neyong_export = ''
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.GET.get('export'):
            operation = Operation.objects.get(id=request.session['operation_id'])
            neyong_export = '''
{}
operation = Operation.objects.get(id=request.session['operation_id'])
if operation.event != 'None':
    events = Event.objects.filter(id=operation.event)
    for i in events:
        i.delete()
    eventlists = EventList.objects.filter(event=operation.event)
    for i in eventlists:
        i.delete()
operation.code = """{}"""
operation.content = """{}"""
operation.display = """{}"""
operation.page_name = request.session['page_name']
operation.map_id = """None"""
operation.npc_id = """None"""
operation.skill_id = """None"""
operation.item_id = """None"""
operation.chongwu_id = """None"""
operation.zuoqi_id = """None"""
operation.event = """None"""
operation.is_input = """{}"""

                    '''.format(neyong_export, operation.code, operation.content, operation.display, operation.is_input)
            if operation.event != 'None':
                event = Event.objects.get(id=operation.event)
                neyong_export = '''
{}
event = Event.objects.create()
operation.event = event.id
event.page_name = """None"""
event.name = """None"""
event.operation = request.session['operation_id']
event.map_id = """None"""
event.npc_id = """None"""
event.chongwu_id = """None"""
event.zuoqi_id = """None"""
event.item_id = """None"""
event.skill_id = """None"""
event.code = """{}"""
event.save()
                       '''.format(neyong_export, event.code)
                if EventList.objects.filter(event=operation.event).count() == 0:
                    pass
                else:
                    eventLists = EventList.objects.filter(event=operation.event)
                    for eventlist in eventLists:
                        neyong_export = '''
{}
eventlist = EventList.objects.create()
eventlist.event = event.id
eventlist.event_all = """None"""
eventlist.display = """{}"""
eventlist.execute_display = """{}"""
eventlist.content = """{}"""
eventlist.not_content = """{}"""
eventlist.code = """{}"""
eventlist.position = """{}"""
eventlist.save()
                                               '''.format(neyong_export, eventlist.display, eventlist.execute_display,
                                                          eventlist.content, eventlist.not_content, eventlist.code,
                                                          eventlist.position)
            neyong_export = '''
{}
operation.save()
                               '''.format(neyong_export)
        if request.method == "POST":
            if request.POST['import']:
                try:
                    exec(request.POST['import'])
                    messages = '数据导入成功'
                except:
                    messages = '数据异常，无法导入'
            else:
                messages = '请输入导入数据'
    else:
        messages = '你还不是设计'
        neyong = '你想要看什么呢，自己动手丰衣足食'
    return render(request, 'wap_operation_import.html', locals())


# 创建模板内容
def wap_page_list_create(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('create'):
        request.session['create'] = request.GET.get('create')
        if request.session['create'] == '2':
            code = """<form action="/wap/" method = "GET">
【输入框标题】<br/>
<input type="hidden" name="ck" value={{parameter}}>
名字: <input name = "name" type = "text"/><br/>
<input name = "submit" type = "submit" title = "提交" value="提交" /><br/>
 </form>"""
    if request.session['page_name'] != '0' and request.session['page_name'] != 0 and user.is_designer == 'True':
        if request.method == "POST":
            operation = Operation.objects.create()
            operation.page_name = request.session['page_name']
            operation.code = request.POST['code']
            operation.content = request.POST['content']
            operation.position = Operation.objects.filter(page_name=request.session['page_name']).count() + 1
            # 检测显示条件是否为空，为空时附值为True
            operation.display = request.POST['display']
            if request.session['create'] == '2':
                operation.is_input = 1
            elif request.session['create'] == '3':
                operation.is_input = 2
            elif request.session['create'] == '4':
                operation.is_input = 3
            else:
                pass
            operation.save()
            messages = '添加操作成功'
            request.session['operation_id'] = operation.id
            operation = Operation.objects.get(id=request.session['operation_id'])
            '''
            # 返回模板列表
            operation = Operation.objects.filter(page_name=request.session['page_name']).extra(
                select={'num': 'position+0'})
            operation = operation.extra(order_by=["num"])
            bh = 0
            neyong = ''
            for i in operation:
                if int(i.is_input) == 2:
                    neyong = '{}＜style＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/style＞<br/>'.format(
                        neyong,
                        i.id,
                        i.position,
                        i.content)
                elif int(i.is_input) == 3:
                    neyong = '{}＜script＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/script＞<br/>'.format(
                        neyong,
                        i.id,
                        i.position,
                        i.content)
                else:
                    neyong = '{}<a href="/wap_page_list_check/?ck={}">{}.{}</a>'.format(neyong, i.id, i.position,
                                                                                        i.content)
            return render(request, 'wap_page_list.html', locals())
            '''
            return render(request, 'wap_page_list_check.html', locals())
    else:
        if request.session['page_name'] == '0' or request.session['page_name'] == 0:
            messages = '获取模板信息失败'
        elif user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_page_list_create.html', locals())


# 创建修改内容
def wap_page_list_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    userss = User.objects.get(name=user_name())
    user = GameObject(User.objects.get(id=request.session['user_id']))
    messages = ''
    neyong = ''
    if request.GET.get('update'):
        if user.is_designer == 'True':
            messages = '{}模板缓存更新成功'.format(messages)
            c.set('page_update', 1)
            c.save()
        else:
            messages = '你还不是设计者'
    neyong = """{}<a href="/wap_page_list_check/?update=1&sj={}">更新所有玩家模板缓存</a><br/>""".format(neyong, sj())
    if request.GET.get('ck'):
        request.session['operation_id'] = request.GET.get('ck')
    operation = Operation.objects.get(id=request.session['operation_id'])
    if request.session['page_name'] != '0' and request.session['page_name'] != 0 and user.is_designer == 'True':
        if request.method == "POST":
            operation.code = request.POST['code']
            operation.content = request.POST['content']
            if request.session['page_name'] != operation.page_name:
                operation.page_name = request.session['page_name']
            if int(operation.position) == int(request.POST['position']):
                pass
            else:
                operation.position = int(request.POST['position']) - 1
            operation.display = request.POST['display']
            operation.save()
            messages = '保存操作成功'
            # 返回模板列表
            operation = Operation.objects.filter(page_name=request.session['page_name']).extra(
                select={'num': 'position+0'})
            operation = operation.extra(order_by=["num"])
            bh = 0
            for i in operation:
                if i.position != bh:  # 当位置变更时重新排序
                    i.position = bh
                    i.save()
                bh = bh + 1
                if int(i.is_input) == 2:
                    neyong = '{}＜style＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/style＞<br/>'.format(
                        neyong,
                        i.id,
                        i.position,
                        i.content)
                elif int(i.is_input) == 3:
                    neyong = '{}＜script＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/script＞<br/>'.format(
                        neyong,
                        i.id,
                        i.position,
                        i.content)
                else:
                    neyong = '{}<a href="/wap_page_list_check/?ck={}">{}.{}</a>'.format(neyong, i.id, i.position,
                                                                                        i.content)
            return render(request, 'wap_page_list.html', locals())
        elif request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                # 删除事件
                if operation.event == 'None':
                    pass
                else:
                    event = Event.objects.filter(id=operation.event)
                    event_list = EventList.objects.filter(event=operation.event)
                    event_list.delete()
                    event.delete()
                # 删除操作
                operation.delete()
                messages = '删除操作成功'
                # 返回模板列表
                operation = Operation.objects.filter(page_name=request.session['page_name']).extra(
                    select={'num': 'position+0'})
                operation = operation.extra(order_by=["num"])
                bh = 0
                neyong = ''
                for i in operation:
                    if i.position != bh:  # 当位置变更时重新排序
                        i.position = bh
                        i.save()
                    bh = bh + 1
                    if int(i.is_input) == 2:
                        neyong = '{}＜style＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/style＞<br/>'.format(
                            neyong,
                            i.id,
                            i.position,
                            i.content)
                    elif int(i.is_input) == 3:
                        neyong = '{}＜script＞<br/><a href="/wap_page_list_check/?ck={}">{}.{}</a><br/>＜/script＞<br/>'.format(
                            neyong, i.id, i.position, i.content)
                    else:
                        neyong = '{}<a href="/wap_page_list_check/?ck={}">{}.{}</a>'.format(neyong, i.id, i.position,
                                                                                            i.content)
                return render(request, 'wap_page_list.html', locals())
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        else:
            pass
    else:
        if request.session['page_name'] == '0' or request.session['page_name'] == 0:
            messages = '获取模板信息失败'
        elif user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_page_list_check.html', locals())


def wap_page_list_event(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    operation = Operation.objects.get(id=request.session['operation_id'])
    if operation.event == 'None' or operation.event == '0' or not operation.event:
        event = 0
    else:
        event = Event.objects.get(id=operation.event)
        request.session['event'] = event.id
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.session['page_name'] != '0' and request.session['page_name'] != 0 and user.is_designer == 'True':
        # 保存事件
        if request.method == "POST":
            # 检测当前事件ID是否存在，存在则更改，否则创建新事件
            if operation.event == '0' or operation.event == 'None' or request.session['event'] == 0:
                event = Event.objects.create()
                event.operation = request.session['operation_id']
                event.page_name = request.session['page_name']
            else:
                event = Event.objects.get(id=request.session['event'])
            event.code = request.POST['code']
            event.save()
            operation.event = event.id
            operation.save()
            request.session['event'] = event.id
            messages = '保存事件成功'
        # 更改事件
        elif request.GET.get('event'):
            event = Event.objects.get(id=operation.event)
            request.session['event'] = event.id
        # 创建事件
        elif request.GET.get('create'):
            request.session['event'] = 0
        # 创建子事件
        elif request.GET.get('create_event'):
            eventlist = EventList.objects.create(event=request.session['event'])
            messages = '添加事件步骤成功'
            eventlist.position = EventList.objects.filter(event=request.session['event']).count() + 1
            eventlist.save()
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event']).extra(select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
        # 移动子事件位置
        elif request.GET.get('page'):
            eventlist = EventList.objects.get(id=request.GET.get('iid'))
            a = eventlist.position
            eventlist.position = int(request.GET.get('page'))
            eventlist.save()
            b = a + 1 if a < eventlist.position else a - 1
            messages = '{}移动到{}成功'.format(a, b)
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event=request.session['event']).extra(select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
        elif request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                eventlist = EventList.objects.filter(event=request.session['event'])
                eventlist.delete()
                event.code = ''
                event.delete()
                operation.event = 'None'
                operation.save()
                messages = '删除所有事件成功'
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
        elif request.GET.get('delete_eventlist'):
            if request.GET.get('delete_eventlist') == '0':
                request.session['delete_eventlist'] = 1
                request.session['event_list'] = request.GET.get('iid')
                eventlist = EventList.objects.get(id=request.GET.get('iid'))
            elif request.GET.get('delete_eventlist') == '1':
                request.session['delete_eventlist'] = 0
                event_list = EventList.objects.get(id=request.session['event_list'])
                event_list.delete()
                messages = '删除步骤成功'
            elif request.GET.get('delete_eventlist') == '2':
                request.session['delete_eventlist'] = 0
            else:
                pass
        else:
            pass
    else:
        if request.session['page_name'] == '0' or request.session['page_name'] == 0:
            messages = '获取模板信息失败'
        elif user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 返回事件步骤列表
    event_lists = EventList.objects.filter(event=request.session['event']).extra(select={'num': 'position+0'})
    event_lists = event_lists.extra(order_by=["num"])
    bh = 0
    event_list = ''
    for i in event_lists:
        bh = bh + 1
        if bh != i.position:
            i.position = bh
            i.save()
        event_list = '{}{}.<a href="/wap_page_list_event_list/?ck={}&xs=0">修改事件</a> '.format(event_list, i.position,
                                                                                                 i.id)
        event_list = '{}<a href="/wap_page_list_event/?delete_eventlist=0&iid={}">删除</a> '.format(event_list, i.id)
        event_list = '{}<a href="/wap_page_list_event/?page={}&iid={}">上移</a> '.format(event_list,
                                                                                         int(i.position) - 2,
                                                                                         i.id)
        event_list = '{}<a href="/wap_page_list_event/?page={}&iid={}">下移</a><br/>'.format(event_list,
                                                                                             int(i.position) + 2, i.id)
    return render(request, 'wap_page_list_event.html', locals())


# 事件列表查看
def wap_page_list_event_list(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('xs'):
        request.session['event_xs'] = request.GET.get('xs')
    if request.GET.get('key'):
        if request.GET.get('key') == '0':
            request.session['event_key'] = ''
        else:
            request.session['event_key'] = request.GET.get('key')
    if request.GET.get('value'):
        if request.GET.get('value') == '0':
            request.session['event_value'] = ''
        else:
            request.session['event_value'] = request.GET.get('value')
    if request.GET.get('ck'):
        request.session['event_list'] = request.GET.get('ck')
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.method == "POST":
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            eventlist.display = request.POST.get('display')
            eventlist.execute_display = request.POST.get('execute_display')
            eventlist.not_content = request.POST.get('not_content')
            eventlist.content = request.POST.get('content')
            eventlist.code = request.POST.get('code')
            eventlist.save()
            messages = '保存步骤成功'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    if request.session['event_xs'] == '2':
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            if request.GET.get('xs'):
                pass
            else:
                if request.GET.get('attribute_params_key') and request.GET.get('attribute_params_value'):
                    if request.session['event_key'] != '':
                        eventlist.params = ujson.loads(eventlist.params)
                        del eventlist.params[request.session['event_key']]
                        eventlist.save()
                        request.session['event_key'] = ''
                    eventlist = GameObject(eventlist)
                    if request.GET.get('attribute_params_key').split("{"):
                        eventlist.set(request.GET.get('attribute_params_key'),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    else:
                        eventlist.set('{}'.format(request.GET.get('attribute_params_key')),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    eventlist.save()
                    request.session['event_xs'] = '1'
                    messages = '保存成功'
                else:
                    if not request.GET.get('attribute_params_key') and not request.GET.get(
                            'attribute_params_value'):
                        request.session['event_xs'] = '1'
                        if request.session['event_key'] != '':
                            eventlist.params = ujson.loads(eventlist.params)
                            del eventlist.params[request.session['event_key']]
                            eventlist.save()
                            request.session['event_key'] = ''
                    elif request.GET.get('attribute_params_key'):
                        messages = '请输入属性值'
                    else:
                        messages = '请输入属性名'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    eventlist = EventList.objects.get(id=request.session['event_list'])
    params = ujson.loads(str(eventlist.params))
    count = len(params)
    if request.session['event_xs'] == '1' and params != '{}':
        params_list = ''
        #            data1, data2 = [], []
        #            for iiii, j in params.items():
        #                if len(iiii.split(".")) == 3:
        #                   data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
        #               else:
        #                   data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
        #               data2.append(f'{iiii.split(".")[0]}.save()')
        #          params_code = '\n'.join(data1 + [""] + list(set(data2)))
        for key, value in params.items():
            params_list = """{}<a href="/wap_page_list_event_list/?xs=2&key={}&value={}">{} = {}</a><br/>""".format(
                params_list, key, value, key, str(value))
    eventlist = EventList.objects.get(id=request.session['event_list'])
    operation = Operation.objects.get(event=eventlist.event)
    event = Event.objects.get(id=operation.event)
    return render(request, 'wap_page_list_event_list.html', locals())


# --------------------设计任务----------------------------
def wap_task_area_name(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.method == "POST":
        if request.POST['create_area_name']:
            if user.is_designer == 'True':
                if TaskAreaName.objects.filter(area_name=request.POST['create_area_name']).count() == 0:
                    create = TaskAreaName.objects.create(area_name=request.POST['create_area_name'])
                    messages = '添加任务分类成功'
                else:
                    messages = '任务分类名称重复'
            else:
                messages = '你还不是设计者'
    objects = TaskAreaName.objects.all()
    return render(request, 'wap_task_area_name.html', locals())


def wap_task(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    if request.GET.get('area_name'):
        request.session['area_name'] = request.GET.get('area_name')
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('create'):
        if user.is_designer == 'True':
            if Task.objects.all().count() == 0:
                count = 0
            else:
                count = Task.objects.all().count()
            if Task.objects.all().count() == count:
                task = Task.objects.create(name='未定义', desc='未定义', area_name=request.session['area_name'])
            else:
                task_all = Task.objects.all()
                bh = 0
                for task in task_all:
                    bh = bh + 1
                    if int(task.id) == bh:
                        pass
                    else:
                        task = Task.objects.create(id=bh, name='未定义', desc='未定义',
                                                   area_name=request.session['area_name'])
                        break
            messages = '添加任务成功'
        else:
            messages = '你还不是设计者'
    if request.GET.get('copy'):
        if user.is_designer == 'True':
            if Task.objects.all().count() == 0:
                count = 0
            else:
                count = Task.objects.all().count()
            task_copy = Task.objects.get(id=request.GET.get('task_id'))
            if Task.objects.all().count() == count:
                task = Task.objects.create()
                task.name = task_copy.name
                task.desc = task_copy.desc
                task.area_name = task_copy.area_name
                task.type = task_copy.type
                task.remove = task_copy.remove
                task.npc_id = task_copy.npc_id
                task.npc_name = task_copy.npc_name
                task.display = task_copy.display
                task.execute_display = task_copy.execute_display
                task.content = task_copy.content
                task.not_content = task_copy.not_content
                task.code = task_copy.code
                task.submit_code = task_copy.submit_code
                task.params = task_copy.params
                task.save()
            else:
                task_all = Task.objects.all()
                bh = 0
                for task in task_all:
                    bh = bh + 1
                    if int(task.id) == bh:
                        pass
                    else:
                        task = Task.objects.create(id=bh, name='未定义', desc='未定义',
                                                   area_name=request.session['area_name'])
                        task.name = '{}(复制)'.format(task_copy.name)
                        task.desc = task_copy.desc
                        task.area_name = task_copy.area_name
                        task.type = task_copy.type
                        task.remove = task_copy.remove
                        task.npc_id = task_copy.npc_id
                        task.npc_name = task_copy.npc_name
                        task.display = task_copy.display
                        task.execute_display = task_copy.execute_display
                        task.content = task_copy.content
                        task.not_content = task_copy.not_content
                        task.code = task_copy.code
                        task.submit_code = task_copy.submit_code
                        task.params = task_copy.params
                        task.save()
                        break
            messages = '复制任务成功'
        else:
            messages = '你还不是设计者'
    objects = Task.objects.filter(area_name=request.session['area_name']).order_by('-id')
    return render(request, 'wap_task.html', locals())


def wap_task_attribute(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    if request.GET.get('task_id'):
        request.session['object_id'] = request.GET.get('task_id')
    objects = Task.objects.get(id=request.session['object_id'])
    request.session['area_name'] = objects.area_name
    if user.is_designer == 'True':
        if request.GET.get('iid'):
            objects.npc_id = request.GET.get('iid')
            objects.save()
        elif request.GET.get('npc_iid'):
            if TaskItem.objects.filter(task_id=objects.id, npc_id=request.GET.get('npc_iid')).count() == 0:
                items = TaskItem.objects.create()
                items.task_id = objects.id
                items.npc_id = request.GET.get('npc_iid')
                items.npc_count = 1
                items.item_id = 0
                items.item_count = 0
                items.save()
            else:
                messages = '该怪物重复，无需添加'
        elif request.GET.get('item_iid'):
            if TaskItem.objects.filter(task_id=objects.id, item_id=request.GET.get('item_iid')).count() == 0:
                items = TaskItem.objects.create()
                items.task_id = objects.id
                items.item_id = request.GET.get('item_iid')
                items.item_count = 1
                items.npc_id = 0
                items.npc_count = 0
                items.save()
            else:
                messages = '该物品重复，无需添加'
        elif request.GET.get('delete'):
            if request.GET.get('delete') == '0' or request.GET.get('delete') == 0:
                task_item = TaskItem.objects.get(task_id=objects.id, npc_id=request.GET.get('delete_id'))
                messages = '移除怪物：{}成功'.format(task_item.npc_name)
                task_item.delete()
            elif request.GET.get('delete') == '1' or request.GET.get('delete') == 1:
                task_item = TaskItem.objects.get(task_id=objects.id, item_id=request.GET.get('delete_id'))
                messages = '移除物品：{}成功'.format(task_item.item_name)
                task_item.delete()
            elif request.GET.get('delete') == '100' or request.GET.get('delete') == 100:
                objects.npc_id = 0
                objects.save()
                messages = '取消绑定NPC触发任务成功'
            else:
                pass
        else:
            pass
    else:
        messages = '你还不是设计者'
    if request.method == "POST":
        if user.is_designer == 'True':
            objects.name = request.POST['name']
            objects.desc = request.POST['desc']
            objects.type = request.POST['type']
            objects.area_name = request.POST['area_name']
            objects.execute_display = request.POST['execute_display']
            objects.display = request.POST['display']
            objects.content = request.POST['content']
            objects.not_content = request.POST['not_content']
            objects.code = request.POST['code']
            objects.submit_code = request.POST['submit_code']
            objects.remove = request.POST['remove']
            objects.save()
            messages = '保存任务成功'
            if objects.type == 0 or objects.type == '0' or objects.type == 1 or objects.type == '1':
                task_items = TaskItem.objects.filter(task_id=objects.id)
                for task_item in task_items:
                    if task_item.npc_id == 0 or task_item.npc_id == '0':
                        task_item.delete()
                    else:
                        pass
            elif objects.type == 0 or objects.type == '0' or objects.type == 2 or objects.type == '2':
                task_items = TaskItem.objects.filter(task_id=objects.id)
                for task_item in task_items:
                    if task_item.item_id == 0 or task_item.item_id == '0':
                        task_item.delete()
                    else:
                        pass
            else:
                pass

        else:
            messages = '你还不是设计者'
    if objects.npc_id != 0 and objects.npc_id != '0':
        if GameNpc.objects.filter(id=objects.npc_id) == 0:
            objects.npc_id = 0
        else:
            npcs = GameNpc.objects.get(id=objects.npc_id)
            objects.npc_name = npcs.name
        objects.save()
    neyong = ''
    if objects.type == 0 or objects.type == '0':
        task_items = TaskItem.objects.filter(task_id=objects.id)
        for task_item in task_items:
            task_item.delete()
    elif objects.type == 1 or objects.type == '1':
        task_items = TaskItem.objects.filter(task_id=objects.id)
        bh = 0
        for task_item in task_items:
            if task_item.npc_id == 0 or task_item.npc_id == '0':
                task_item.delete()
            else:
                if GameNpc.objects.filter(id=task_item.npc_id).count() == 0:
                    task_item.delete()
                else:
                    items = GameNpc.objects.get(id=task_item.npc_id)
                    task_item.npc_name = items.name
                    task_item.save()
                    bh = bh + 1
                    neyong = '{}┣<a href="/wap_task_item/?object_name=npc&npc_id={}">{}*{}</a> - <a href="/wap_task_attribute/?delete=0&delete_id={}">移除</a><br/>'.format(
                        neyong, task_item.npc_id, task_item.npc_name, task_item.npc_count, task_item.npc_id)
    elif objects.type == 2 or objects.type == '2':
        task_items = TaskItem.objects.filter(task_id=objects.id)
        bh = 0
        for task_item in task_items:
            if task_item.item_id == 0 or task_item.item_id == '0':
                task_item.delete()
            else:
                if Item.objects.filter(id=task_item.item_id).count() == 0:
                    task_item.delete()
                else:
                    items = Item.objects.get(id=task_item.item_id)
                    task_item.item_name = items.name
                    task_item.save()
                    bh = bh + 1
                    neyong = '{}┣<a href="/wap_task_item/?object_name=item&item_id={}">{}*{}</a> - <a href="/wap_task_attribute/?delete=1&delete_id={}">移除</a><br/>'.format(
                        neyong, task_item.item_id, task_item.item_name, task_item.item_count, task_item.item_id)
    else:
        pass
    area_names = TaskAreaName.objects.all()
    return render(request, 'wap_task_attribute.html', locals())


def wap_task_item(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
        if request.GET.get('npc_id'):
            request.session['object_ck_id'] = request.GET.get('npc_id')
        elif request.GET.get('item_id'):
            request.session['object_ck_id'] = request.GET.get('item_id')
        else:
            pass
    if request.session['object_name'] == 'npc':
        objects = TaskItem.objects.get(task_id=request.session['object_id'], npc_id=request.session['object_ck_id'])
    elif request.session['object_name'] == 'item':
        objects = TaskItem.objects.get(task_id=request.session['object_id'], item_id=request.session['object_ck_id'])
    else:
        objects = ''
    if request.method == "POST":
        if user.is_designer == 'True':
            if request.session['object_name'] == 'npc':
                objects.npc_count = 1 if int(request.POST['npc_count']) <= 0 else request.POST['npc_count']
                objects.item_count = 0
                objects.item_id = 0
                objects.save()
            elif request.session['object_name'] == 'item':
                objects.item_count = 1 if int(request.POST['item_count']) <= 0 else request.POST['item_count']
                objects.npc_count = 0
                objects.npc_id = 0
                objects.save()
            else:
                pass
            messages = '修改数量成功'
        else:
            messages = '你还不是设计者'
    return render(request, 'wap_task_item.html', locals())


# -----------------------------公共表达式-----------------------
def wap_expression(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    expression = Expression.objects.all()
    """
    if request.GET.get('update'):
        expressionjson = ExpressionJson.objects.get(id=1)
        expressionjson.params = '{}'
        for i in expression:
            expressionjson.set('{}'.format(i.attribute), '{}'.format(i.expression))
        expressionjson.save()
        messages = '表达式更新成功'
    """
    bh = 0
    neyong = ''
    for i in expression:
        bh = bh + 1
        neyong = '{}<a href="/wap_expression_check/?ck={}">{}.{}({})</a><br/>'.format(neyong, i.id, bh, i.name,
                                                                                      i.attribute)
    return render(request, 'wap_expression.html', locals())


# 创建表达式
def wap_expression_create(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if user.is_designer == 'True':
        if request.method == "POST":
            if Expression.objects.filter(attribute=request.POST['attribute']).count() == 0 and len(
                    request.POST['attribute']) > 0:
                expression = Expression.objects.create()
                expression.name = request.POST['name']
                expression.attribute = request.POST['attribute']
                expression.expression = request.POST['expression']
                expression.save()
                messages = '创建表达式成功'
                """
                if ExpressionJson.objects.all().count() == 0:
                    expressionjson = ExpressionJson.objects.create()
                else:
                    expressionjson = ExpressionJson.objects.get(id=1)
                expressionjson.set('{}'.format(expression.attribute), expression.expression)
                expressionjson.save()
                """
                expression = Expression.objects.all()
                neyong = ''
                bh = 0
                for i in expression:
                    bh = bh + 1
                    neyong = '{}<a href="/wap_expression_check/?ck={}">{}.{}({})</a><br/>'.format(neyong, i.id, bh,
                                                                                                  i.name,
                                                                                                  i.attribute)
                return render(request, 'wap_expression.html', locals())
            else:
                if Expression.objects.filter(attribute=request.POST['attribute']).count() == 1:
                    messages = '表达式属性重复，请更改表达式属性或者到对应表达式属性里更改'
                else:
                    messages = '输入不能为空'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_expression_create.html', locals())


# 查看修改表达式
def wap_expression_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('ck'):
        request.session['object_id'] = request.GET.get('ck')
    expression = Expression.objects.get(id=request.session['object_id'])
    if user.is_designer == 'True':
        if request.method == "POST":
            expression.name = request.POST['name']
            expression.expression = request.POST['expression']
            expression.save()
            """
            expressionjson = ExpressionJson.objects.get(id=1)
            expressionjson.set('{}'.format(expression.attribute), expression.expression)
            expressionjson.save()
            """
            messages = '保存表达式成功'
        if request.GET.get('delete'):
            if request.GET.get('delete') == '0':
                request.session['delete1'] = 1
            elif request.GET.get('delete') == '1':
                request.session['delete1'] = 0
                """
                expressionjson = ExpressionJson.objects.get(id=1)
                expressionjson.set('{}'.format(expression.attribute), 0)
                expressionjson.save()
                """
                expression.delete()
                messages = '删除表达式成功'
                expression = Expression.objects.all()
                neyong = ''
                bh = 0
                for i in expression:
                    bh = bh + 1
                    neyong = '{}<a href="/wap_expression_check/?ck={}">{}.{}({})</a><br/>'.format(neyong, i.id, bh,
                                                                                                  i.name,
                                                                                                  i.attribute)
                return render(request, 'wap_expression.html', locals())
            elif request.GET.get('delete') == '2':
                request.session['delete1'] = 0
            else:
                pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_expression_check.html', locals())


# -----------------------------公共事件-----------------------
def wap_event(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    return render(request, 'wap_event.html', locals())


# 公共事件列表
def wap_event_list(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    objects = EventAll.objects.filter(area_name=request.session['object_name'])
    return render(request, 'wap_event_list.html', locals())


# 公共事件列表查看修改
def wap_event_list_check(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('event_id'):
        request.session['event_id'] = request.GET.get('event_id')
    event = EventAll.objects.get(id=request.session['event_id'])
    if user.is_designer == 'True':
        if request.method == "POST":
            event.code = request.POST['code']
            event.save()
            messages = '保存成功'
        # 创建子事件
        elif request.GET.get('create_event'):
            eventlist = EventList.objects.create(event_all=request.session['event_id'])
            messages = '添加事件步骤成功'
            eventlist.position = EventList.objects.filter(event_all=request.session['event_id']).count() + 1
            eventlist.save()
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event_all=request.session['event_id']).extra(
                select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
            # 移动子事件位置
        elif request.GET.get('page'):
            eventlist = EventList.objects.get(id=request.GET.get('iid'))
            a = eventlist.position
            eventlist.position = int(request.GET.get('page'))
            eventlist.save()
            b = a + 1 if a < eventlist.position else a - 1
            messages = '{}移动到{}成功'.format(a, b)
            # 返回事件步骤列表
            event_lists = EventList.objects.filter(event_all=request.session['event']).extra(
                select={'num': 'position+0'})
            event_lists = event_lists.extra(order_by=["num"])
            bh = 0
            event_list = ''
            for i in event_lists:
                bh = bh + 1
                if bh != i.position:
                    i.position = bh
                    i.save()
        elif request.GET.get('delete_eventlist'):
            if request.GET.get('delete_eventlist') == '0':
                request.session['delete_eventlist'] = 1
                request.session['event_list'] = request.GET.get('iid')
                eventlist = EventList.objects.get(id=request.GET.get('iid'))
            elif request.GET.get('delete_eventlist') == '1':
                request.session['delete_eventlist'] = 0
                event_list = EventList.objects.get(id=request.session['event_list'])
                event_list.delete()
                messages = '删除步骤成功'
            elif request.GET.get('delete_eventlist') == '2':
                request.session['delete_eventlist'] = 0
            else:
                pass
        else:
            pass
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    # 返回事件步骤列表
    event_lists = EventList.objects.filter(event_all=request.session['event_id']).extra(select={'num': 'position+0'})
    event_lists = event_lists.extra(order_by=["num"])
    bh = 0
    event_list = ''
    for i in event_lists:
        bh = bh + 1
        if bh != i.position:
            i.position = bh
            i.save()
        event_list = '{}{}.<a href="/wap_event_list_check_list/?ck={}&xs=0">修改事件</a> '.format(event_list,
                                                                                                  i.position,
                                                                                                  i.id)
        event_list = '{}<a href="/wap_event_list_check/?delete_eventlist=0&iid={}">删除</a> '.format(event_list, i.id)
        event_list = '{}<a href="/wap_event_list_check/?page={}&iid={}">上移</a> '.format(event_list,
                                                                                          int(i.position) - 2,
                                                                                          i.id)
        event_list = '{}<a href="/wap_event_list_check/?page={}&iid={}">下移</a><br/>'.format(event_list,
                                                                                              int(i.position) + 2, i.id)

    return render(request, 'wap_event_list_check.html', locals())


def wap_event_list_check_list(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('xs'):
        request.session['event_xs'] = request.GET.get('xs')
    if request.GET.get('key'):
        if request.GET.get('key') == '0':
            request.session['event_key'] = ''
        else:
            request.session['event_key'] = request.GET.get('key')
    if request.GET.get('value'):
        if request.GET.get('value') == '0':
            request.session['event_value'] = ''
        else:
            request.session['event_value'] = request.GET.get('value')
    if request.GET.get('ck'):
        request.session['event_list'] = request.GET.get('ck')
    if request.method == "POST":
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            eventlist.display = request.POST.get('display')
            eventlist.execute_display = request.POST.get('execute_display')
            eventlist.not_content = request.POST.get('not_content')
            eventlist.content = request.POST.get('content')
            eventlist.code = request.POST.get('code')
            eventlist.save()
            messages = '保存步骤成功'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    if request.session['event_xs'] == '2':
        if user.is_designer == 'True':
            eventlist = EventList.objects.get(id=request.session['event_list'])
            if request.GET.get('xs'):
                pass
            else:
                if request.GET.get('attribute_params_key') and request.GET.get('attribute_params_value'):
                    if request.session['event_key'] != '':
                        eventlist.params = ujson.loads(eventlist.params)
                        del eventlist.params[request.session['event_key']]
                        eventlist.save()
                        request.session['event_key'] = ''
                    eventlist = GameObject(eventlist)
                    if request.GET.get('attribute_params_key').split("{"):
                        eventlist.set(request.GET.get('attribute_params_key'),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    else:
                        eventlist.set('{}'.format(request.GET.get('attribute_params_key')),
                                      '{}'.format(request.GET.get('attribute_params_value')))
                    eventlist.save()
                    request.session['event_xs'] = '1'
                    messages = '保存成功'
                else:
                    if not request.GET.get('attribute_params_key') and not request.GET.get(
                            'attribute_params_value'):
                        request.session['event_xs'] = '1'
                        if request.session['event_key'] != '':
                            eventlist.params = ujson.loads(eventlist.params)
                            del eventlist.params[request.session['event_key']]
                            eventlist.save()
                            request.session['event_key'] = ''
                    elif request.GET.get('attribute_params_key'):
                        messages = '请输入属性值'
                    else:
                        messages = '请输入属性名'
        else:
            if user.is_designer != 'True':
                messages = '你还不是设计者'
            else:
                pass
    eventlist = EventList.objects.get(id=request.session['event_list'])
    params = ujson.loads(str(eventlist.params))
    count = len(params)
    if request.session['event_xs'] == '1' and params != '{}':
        params_list = ''
        #            data1, data2 = [], []
        #            for iiii, j in params.items():
        #                if len(iiii.split(".")) == 3:
        #                    data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
        #                else:
        #                    data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
        #                data2.append(f'{iiii.split(".")[0]}.save()')
        #            params_code = '\n'.join(data1 + [""] + list(set(data2)))
        for key, value in params.items():
            params_list = """{}<a href="/wap_area_name_list_operation_event_list/?xs=2&key={}&value={}">{} = {}</a><br/>""".format(
                params_list, key, value, key, str(value))
    event_all = EventAll.objects.get(id=request.session['event_id'])
    return render(request, 'wap_event_list_check_list.html', locals())


# -----------------------------装备类型-----------------------
def wap_type(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    neyong = ''
    objects = ItemType.objects.all().extra(select={'num': 'position+0'})
    objects = objects.extra(order_by=["num"])
    bh = 0
    if user.is_designer == 'True':
        if request.GET.get('delete'):
            item_type = ItemType.objects.get(position=request.GET.get('delete'))
            item_type.delete()
            messages = '删除成功'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    for i in objects:
        bh = bh + 1
        if int(i.position) != bh:
            i.position = bh
            i.save()
        neyong = '{}<a href="/wap_type_check/?object_name={}">{}.{}(ID:{})</a>[{}] <a href="/wap_type/?delete={}">删除</a><br/>'.format(
            neyong, i.type, bh, i.type, i.id,i.item_areaname, bh)
    return render(request, 'wap_type.html', locals())


# 创建装备类型
def wap_type_create(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['type']:
                if ItemType.objects.filter(type=request.POST['type']).count() == 0:
                    if ItemType.objects.all().count() == 0:
                        count = 0
                    else:
                        count = ItemType.objects.latest('id')  # 获取某个模型字段最新一行数据
                        count = count.id
                    if ItemType.objects.all().count() == count:
                        area_name = ItemType.objects.create(type=request.POST['type'], position=100,
                                                            item_areaname=request.POST['item_areaname'])
                    else:
                        map_all = ItemType.objects.all()
                        bh = 0
                        for maps in map_all:
                            bh = bh + 1
                            if int(maps.id) == bh:
                                pass
                            else:
                                area_name = ItemType.objects.create(id=bh, type=request.POST['type'], position=100)
                                break
                    messages = '创建装备类型:{}成功'.format(request.POST['type'])
                    # 重新获得所有区域名称并返回
                    neyong = ''
                    objects = ItemType.objects.all().extra(select={'num': 'position+0'})
                    objects = objects.extra(order_by=["num"])
                    bh = 0
                    for i in objects:
                        bh = bh + 1
                        if int(i.position) != bh:
                            i.position = bh
                            i.save()
                        neyong = '{}<a href="/wap_type_check/?object_name={}">{}.{}</a>[{}] <a href="/wap_type/?delete={}">删除</a><br/>'.format(
                            neyong, i.type, bh, i.type, i.item_areaname, bh)
                    return render(request, 'wap_type.html', locals())
                else:
                    messages = '创建失败:{}装备类型名重复'.format(request.POST['type'])
            else:
                messages = '请正确输入装备类型名'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    item_areanames = ItemAreaName.objects.all()
    return render(request, 'wap_type_create.html', locals())


# 装备类型查看，及位置移动
def wap_type_check(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if request.GET.get('object_name'):
        request.session['object_name'] = request.GET.get('object_name')
    objects = ItemType.objects.get(type=request.session['object_name'])
    if user.is_designer == 'True':
        if request.method == "POST":
            if request.POST['position']:
                objects = ItemType.objects.get(type=request.session['object_name'])
                objects.position = int(request.POST['position'])
                objects.item_areaname = request.POST['item_areaname']
                objects.save()
                messages = '移动成功'
                neyong = ''
                objects = ItemType.objects.all().extra(select={'num': 'position+0'})
                objects = objects.extra(order_by=["num"])
                bh = 0
                for i in objects:
                    bh = bh + 1
                    if int(i.position) != bh:
                        i.position = bh
                        i.save()
                    neyong = '{}<a href="/wap_type_check/?object_name={}">{}.{}</a>[{}] <a href="/wap_type/?delete={}">删除</a><br/>'.format(
                        neyong, i.type, bh, i.type, i.item_areaname, bh)
                return render(request, 'wap_type.html', locals())
            else:
                messages = '请正确输入移动位置'
    else:
        if user.is_designer != 'True':
            messages = '你还不是设计者'
        else:
            pass
    return render(request, 'wap_type_check.html', locals())


# 加密解密失败
def wap_rest(request):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    return render(request, 'wap_rest.html', locals())


# 物品列表页
def wap_item_choice(request):
    encryption = {}
    u = GameObject(Player.objects.get(id=request.session['player_id']))
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    u.set('shua_xing', request.session['pmwap_shua_xing'])
    if request.GET.get('equip_bh'):
        u.set('jm_parameter', request.GET.get('equip_bh'))
        if parameter_check(request, name=u.jm_parameter) == 'False':
            messages = 'equip_area_name装备类别参数异常'
        else:
            # 参数校验
            item_id = parameter_check(request, name=u.jm_parameter)
            u.set('jm_parameter', request.GET.get('equip_bh'))
            item_bh = parameter_check(request, name=u.jm_parameter)
            u.set('jm_parameter', request.GET.get('equip_page'))
            item_page = parameter_check(request, name=u.jm_parameter)
            if request.GET.get('pets_up_page'):
                u.set('jm_parameter', request.GET.get('pets_up_page'))
                request.session['up_page'] = parameter_check(request, name=u.jm_parameter)
            if request.GET.get('use_up_page'):
                u.set('jm_parameter', request.GET.get('use_up_page'))
                request.session['up_page'] = parameter_check(request, name=u.jm_parameter)
            if request.GET.get('duixiang'):
                u.set('jm_parameter', request.GET.get('duixiang'))
                request.session['duixiang'] = parameter_check(request, name=u.jm_parameter)
            if request.GET.get('equip_area_name'):
                u.set('jm_parameter', request.GET.get('equip_area_name'))
                request.session['equip_area_name'] = parameter_check(request, name=u.jm_parameter)
            equip_area_name = request.session['equip_area_name']
            duixiang = request.session['duixiang']
            up_page = request.session['up_page']
            page = int(item_page)
            if equip_area_name == '装备':
                max_count = ItemPlayer.objects.filter(player_id=u.id, type_id=item_bh, duixiang=duixiang).count()
                max_page = (max_count - 1) / 10
                page_start = page * 10
                page_end = page * 10 + 10 if max_count >= page * 10 + 10 else int(max_count)
                item_lists = ItemPlayer.objects.filter(player_id=u.id, type_id=item_bh, duixiang=duixiang)[
                             page_start:page_end]
            else:
                max_count = ItemPlayer.objects.filter(player_id=u.id, area_name=equip_area_name,
                                                      duixiang=duixiang).count()
                max_page = (max_count - 1) / 10
                page_start = page * 10
                page_end = page * 10 + 10 if max_count >= page * 10 + 10 else int(max_count)
                item_lists = ItemPlayer.objects.filter(player_id=u.id, area_name=equip_area_name, duixiang=duixiang)[
                             page_start:page_end]
            if max_count >= 1:
                item_list = '---<br/>'
            else:
                item_list = '---<br/>暂无物品<br/>'
            bh = page * 10
            item_list = ''
            if request.GET.get('use_up_page'):
                u.set('jm_parameter', request.GET.get('use_up_page'))
            else:
                u.set('jm_parameter', request.GET.get('pets_up_page'))
            page_name1 = parameter_check(request, name=u.jm_parameter)
            page_name = parameter_create(encryption, value=page_name1)
            for i in item_lists:
                bh = bh + 1
                parameter = parameter_create(encryption, value=i.id)
                if equip_area_name == '装备':
                    if request.GET.get('use_up_page'):
                        item_list = '{}{}.<a href="/wap/?use_equip={}&use_up_page={}">{}*{}</a><br/>'.format(item_list,
                                                                                                             bh,
                                                                                                             parameter,
                                                                                                             page_name,
                                                                                                             i.name,
                                                                                                             i.count)
                    else:
                        item_list = '{}{}.<a href="/wap/?pets_equip={}&pets_up_page={}">{}*{}</a><br/>'.format(
                            item_list,
                            bh,
                            parameter,
                            page_name,
                            i.name,
                            i.count)
                else:
                    page_name = parameter_create(encryption, value=request.session['up_page'])
                    page_ys_id = parameter_create(encryption, value=i.item_id)
                    page_bangding = parameter_create(encryption, value=i.bangding)
                    item_list = '{}{}.<a href="/wap/?set_gemstone_id={}&set_gemstone_ys_id={}&set_gemstone_bangding={}&page_name={}">{}*{}</a><br/>'.format(
                        item_list,
                        bh,
                        parameter, page_ys_id, page_bangding,
                        page_name,
                        i.name,
                        i.count)
            if page > 0 or page < max_page:
                item_list = '{}---<br/>'.format(item_list)
            if page > 0:
                a = page - 1
                parameter_bh = parameter_create(encryption, value=item_bh)
                parameter_area_name = parameter_create(encryption, value=equip_area_name)
                parameter_page = parameter_create(encryption, value=a)
                parameter_up_page = parameter_create(encryption, value=page_name1)
                if request.GET.get('use_up_page'):
                    item_list = '{}<a href="/wap_item_choice/?equip_area_name={}&equip_bh={}&equip_page={}&use_up_page={}">上一页</a>'.format(
                        item_list, parameter_area_name, parameter_bh, parameter_page, parameter_up_page)
                else:
                    item_list = '{}<a href="/wap_item_choice/?equip_area_name={}&equip_bh={}&equip_page={}&pets_up_page={}">上一页</a>'.format(
                        item_list, parameter_area_name, parameter_bh, parameter_page, parameter_up_page)
            if page > 0 or page < max_page:
                item_list = '{} | '.format(item_list)
            if page < int(max_page):
                a = page + 1
                parameter_bh = parameter_create(encryption, value=item_bh)
                parameter_area_name = parameter_create(encryption, value=equip_area_name)
                parameter_page = parameter_create(encryption, value=a)
                parameter_up_page = parameter_create(encryption, value=page_name1)
                if request.GET.get('use_up_page'):
                    item_list = '{}<a href="/wap_item_choice/?equip_area_name={}&equip_bh={}&equip_page={}&use_up_page={}">下一页</a>'.format(
                        item_list, parameter_area_name, parameter_bh, parameter_page, parameter_up_page)
                else:
                    item_list = '{}<a href="/wap_item_choice/?equip_area_name={}&equip_bh={}&equip_page={}&pets_up_page={}">下一页</a>'.format(
                        item_list, parameter_area_name, parameter_bh, parameter_page, parameter_up_page)
            if page > 0 or page < int(max_page):
                item_list = '{}<br/>'.format(item_list)
            messages = u.message
            u.set('message', '')
            u.save()
    return render(request, 'wap_item_choice.html', locals())


# 操作文字提示
def wap_message(request):
    user = GameObject(User.objects.get(id=request.session['user_id']))
    c = GameObject(GameAttributeNew.objects.get(id=1))
    u = GameObject(Player.objects.get(id=request.session['player_id']))
    if request.GET.get('wap_message_next'):
        if u.wap_message_count > u.wap_message_bh:
            u.change('wap_message_bh', 1, True)
            u.set('wap_message', u.get('wap_message_{}'.format(u.wap_message_bh)), True)
        else:
            u.set('wap_message', 0, True)
    return render(request, 'wap_message.html', locals())


def wap_xiaoshou(request):
    userss = User.objects.get(name=user_name())
    user = GameObject(User.objects.get(id=request.session['user_id']))
    if request.GET.get('is_settlement'):
        cards = Card.objects.filter(zhuangtai=1, is_settlement=0)
        for card in cards:
            card.is_settlement = 1
            card.settlement_tiem = '{}-{}-{}'.format(date('年'), date('月'), date('日'))
            card.save()

    xiaoshou_z = 0
    cards = Card.objects.filter(zhuangtai=1)
    for card in cards:
        xiaoshou_z = xiaoshou_z + int(card.money)
    xiaoshou_jiesuan = 0
    cards = Card.objects.filter(zhuangtai=1, is_settlement=1)
    for card in cards:
        xiaoshou_jiesuan = xiaoshou_jiesuan + int(card.money)
    xiaoshou_daijiesuan = 0
    cards = Card.objects.filter(zhuangtai=1, is_settlement=0)
    for card in cards:
        xiaoshou_daijiesuan = xiaoshou_daijiesuan + int(card.money)
    xiaoshou_list = '总销售:{}<br/>已结算:{}<br/>待结算:{}<br/>'.format(xiaoshou_z, xiaoshou_jiesuan,
                                                                        xiaoshou_daijiesuan)
    return render(request, 'wap_xiaoshou.html', locals())

