{% extends 'wap_base.html' %}

{% block title %}
{% if request.session.object_name == 'player' %}
人物公共事件
{% elif request.session.object_name == 'item' %}
物品公共事件
{% elif request.session.object_name == 'all' %}
前置事件
{% elif request.session.object_name == 'npc' %}
电脑人物公共事件
{% elif request.session.object_name == 'chongwu' %}
宠物公共事件
{% elif request.session.object_name == 'zuoqi' %}
坐骑公共事件
{% elif request.session.object_name == 'shibing' %}
士兵公共事件
{% elif request.session.object_name == 'map' %}
场景公共事件
{% elif request.session.object_name == 'xitong' %}
系统公共事件
{% elif request.session.object_name == 'pk' %}
战斗公共事件
{% else %}
{% endif %}
{% endblock %}

{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.object_name == 'player' %}
[人物公共事件]<br/>
{% elif request.session.object_name == 'item' %}
[物品公共事件]<br/>
{% elif request.session.object_name == 'all' %}
[前置事件(在所有页面前执行)]<br/>
{% elif request.session.object_name == 'npc' %}
[电脑人物公共事件]<br/>
{% elif request.session.object_name == 'chongwu' %}
[宠物公共事件]<br/>
{% elif request.session.object_name == 'zuoqi' %}
[坐骑公共事件]<br/>
{% elif request.session.object_name == 'shibing' %}
[士兵公共事件]<br/>
{% elif request.session.object_name == 'map' %}
[场景公共事件]<br/>
{% elif request.session.object_name == 'xitong' %}
[系统公共事件]<br/>
{% elif request.session.object_name == 'pk' %}
[战斗公共事件]<br/>
{% else %}
{% endif %}
---<br/>
{% for object in objects %}
    <a href="/wap_event_list_check/?event_id={{object.id}}">{{object.name}}(id:{{object.id}})</a><br/>
{% endfor %}
{{ neyong }}

---<br/>
<a href="/wap_event/" class="function_button">返回上级</a><br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}