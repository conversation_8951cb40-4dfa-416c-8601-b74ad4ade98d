{% extends 'base.html' %}

{% block title %}注册{% endblock %}
{% block content %}


{% if neyong == '' or neyong == '0' %}

<form action="/register/" method="GET">
    <h1>注册账号</h1>
     {% if messages %}<div style="color:red">{{ messages }}</div>{% endif %}
    <input type="hidden" name="register" value="register"><!--隐藏，用于区分注册还是登陆提交-->
    填写账号:<input  name="username_1"  onclick="showTip(this)" onmouseout="hideTip()" title="请输入帐号"><br/>
    填写密码:<input  name="password_1"  onclick="showTip(this)" onmouseout="hideTip()" title="请输入密码"><br/>
    确认密码:<input  name="password_2"  onclick="showTip(this)" onmouseout="hideTip()" title="再次确认密码"><br/>
    手机号码:<input  name="number_1"  onclick="showTip(this)" onmouseout="hideTip()" title="请输入手机号，用于找回密码"><br/>
    安全码:<input  name="security_1"  onclick="showTip(this)" onmouseout="hideTip()" title="牢记安全码用于重置密码"><br/>
    <input name="submit" type="submit" title="注册" value="注册"/> | <button type="reset">重置</button><br/>
</form>
     <div id="tip"></div>
<a href="/index/">网站首页</a>
<script>
function showTip(obj) {
  var tip = document.getElementById("tip");
  tip.innerHTML = obj.title;
  tip.style.display = "block";
  tip.style.left = obj.offsetLeft + "px";
  tip.style.top = obj.offsetTop + obj.offsetHeight - 58 + "px";
}
function hideTip() {
  var tip = document.getElementById("tip");
  tip.style.display = "none";
}
</script>
<style>
#tip {
  position: absolute;
  /*position: fixed;*/
  /*background-color: #fff;*/
  background-color: rgba(0, 0, 0, 0.75);
  border: 1px solid #ccc;
  padding: 5px;
  display: none;
  color:#fff;
}
</style>
{% else %}
{{neyong | safe}}
<style>
{{c.css_register | safe}}
</style>
<script>
{{c.js_register | safe}}
</script>
{% endif %}
<!-- /container -->
 {% endblock %}
