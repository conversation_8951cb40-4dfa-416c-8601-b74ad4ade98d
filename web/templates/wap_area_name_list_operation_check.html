{% extends 'wap_base.html' %}

{% block title %}操作{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.delete1 == 0 %}
[操作ID:{{operation.id}}]<br/>
<form action="/wap_area_name_list_operation_check/" method="post">
    {% csrf_token %}
    代码:<textarea name="code"  maxlength="99999" rows="4" cols="40">{{operation.code | safe}}</textarea><br/>
    内容:<textarea name="content"  maxlength="99999" rows="4" cols="40">{{operation.content | safe}}</textarea><br/>
    显示:<textarea name="display"  maxlength="99999" rows="4" cols="40">{{operation.display | safe}}</textarea><br/>
    {% if not operation %}
    位置:<input name="position" type="text" value=0 size="4" maxlength="6"/><br/>
    {% else %}
        {% if operation.event == 'None' and user.is_designer == 'True' %}
        事件:<a href="/wap_area_name_list_operation_event/?create=1" class="function_button">定义事件</a><br/>
        {% elif operation.event != 'None' %}
        事件:<a href="/wap_area_name_list_operation_event/?event_id=1" class="function_button">修改事件</a><br/>
        {% else %}
        {% endif %}
    位置:<input name="position" type="text" value={{operation.position}} size="4" maxlength="6"/><br/>
    {% endif %}
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
<br/>
<a href="/wap_area_name_list_operation_check/?delete=0" class="function_button">删除操作</a><br/>
---<br/>
<a href="/wap_area_name_list_operation/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% else  %}
删除:{{operation.content}}<br/>
---<br/>
<a href="/wap_area_name_list_operation_check/?delete=1" class="function_button">确定</a> | <a href="/wap_area_name_list_operation_check/?delete=2" class="function_button">取消</a><br/>
{% endif  %}
<!-- /container -->
 {% endblock %}
