{% extends 'wap_base.html' %}

{% block title %}修改本地图片{% endblock %}
{% block content %}
<form action="/wap_tupian_area_name_list_change/" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:blue;"><b>修改本地图片</b></th>
    <tr>
        <td>
            {% if messages %}<a style="color:red;">{{messages | safe}}</a>{% else %}{% endif %}<br/>
        </td>
    </tr>
    <tr>
        <td>
            标识:<input style="height:20px" type="text"  name="create_attribute"  value="{{objects.attribute}}">
        </td>
    </tr>
    <tr>
        <td>
            名称:<input style="height:20px" type="text"  name="create_name"  value="{{objects.name}}">
        </td>
    </tr>
    <tr>
        <td>
    分类:<select style="height:20px" name="create_area_name">
    {% for area_name in area_names %}
        {% if objects.area_name == area_name.area_name %}
            <option style="height:20px" value = {{area_name.area_name}} selected="selected">{{area_name.area_name}}</option>
        {% else %}
            <option style="height:20px" value = {{area_name.area_name}} >{{area_name.area_name}}</option>
        {% endif %}
    {% endfor %}
    </select>
        </td>
    </tr>
    <tr>
        <td>
            {% if objects.code == "0" or objects.code == 0 or objects.code == "" or objects.code == " " %}
            代码:<input style="height:20px;width:80%;" type="text"  name="create_code"   placeholder='style="width:50px;height:50px;" id="1"'/>
            {% else %}
            代码:<input style="height:20px;width:80%;" type="text"  name="create_code"   value="{{objects.code}}"/>
            {% endif %}
        </td>
    </tr>
    <tr>
        <td>
            {{img_neyong | safe}}
        </td>
    </tr>
    <tr>
        <td>
            <input style="height:30px" name="submit" type="submit" title="确定修改" value="确定修改"/>
        </td>
    </tr>
</table>
</form>
<br/>
<a href="/wap_tupian_area_name_list/?delete=1&iid={{objects.id}}" class="function_button">删除图片</a><br/>
<br/>
<a href="/wap_tupian_area_name_list/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
