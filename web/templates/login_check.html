<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>ajax登陆页面</title>
    <script src="https://cdn.staticfile.org/jquery/1.10.2/jquery.min.js"></script>
    <script>
        $(function(){
            $('#btnLogin').click(function () {
                // 获取用户名和密码
                nameuser = $('#nameuser').val()
                passqord = $('#password').val()
                // 发起post ajax 请求
                $.ajax({
                    'url':'login_check',
                    'type':'post',
                    'data':{'username':nameuser, 'password':passqord},
                    'datatype':'json'
                }).success(function (data) {
                    // 登录成功{'res':1}
                    // 登录失败{'res':0}
                    if (data.res === 0){
                        $('#errmsg').show().html('用户名输入错误')
                    }
                    else if(data.res === 2){
                        $('#errmsg').show().html('密码输入错误')
                    }
                    else {
                        // 跳转到图书界面
                        location.href = '/books'
                    }
                })
            })
        })
    </script>
    <style>
        #errmsg{
            display: none;
            color: red;
        }
    </style>
</head>
<body>
<div>
    用户名:<input type="text" id="nameuser"><br/>
    密码:<input type="password" id="password"><br/>
    <input type="button" id="btnLogin" value="登录">
    <div id="errmsg"></div>
</div>
</body>
</html>
