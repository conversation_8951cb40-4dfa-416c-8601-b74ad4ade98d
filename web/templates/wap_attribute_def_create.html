{% extends 'wap_base.html' %}

{% block title %}添加属性{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[添加属性]<br/>
---<br/>
<form action="/wap_attribute_def_create/" method="post">
    {% csrf_token %}
    名称:<input name="name" /><br/>
    属性:<input name="attribute" /><br/>
    类型:<select name="type">
    <option value ="1" selected="selected">整型</option>
    <option value ="2">布尔值</option>
    <option value ="3">文本表达式</option>
    </select><br/>
    初始值:<input name="value" size="9" value="0"><br/>
    <input name="submit" type="submit" title="添加属性" value="添加属性"/><br/>
</form>

---<br/>
<a href="/wap_attribute_def/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
