{% extends 'wap_base.html' %}

{% block title %}查看属性{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.delete1 == 0 %}
[查看属性]<br/>
---<br/>
<form action="/wap_attribute_def_check/" method="post">
    {% csrf_token %}
    {% if attribute.built_in == '0' %}
    名称:<input name="name" value={{attribute.name}} /><br/>
    属性:<input name="attribute" value={{attribute.attribute}} /><br/>
    类型:{% if attribute.type == '1' %}数值{% elif attribute.type == '2' %}布尔值{% else %}字符串/表达式{% endif %}<br/>
    {% if attribute.type == '3' %}
    <textarea name=value  maxlength="99999" rows="4" cols="30">{{attribute.value}}</textarea><br/>
    {% else %}
    初始值:<input name="value" size="9" value={{attribute.value}} /><br/>
    {% endif %}
    <input name="submit" type="submit" title="保存属性" value="保存属性" /><br/>
    {% else %}
    名称:{{attribute.name}}<br/>
    属性:{{attribute.attribute}}<br/>
    类型:{% if attribute.type == '1' %}数值{% elif attribute.type == '2' %}布尔值{% else %}字符串{% endif %}<br/>
    初始值:{{attribute.value}}<br/>
    {% endif %}
</form>
{% if attribute.built_in == '0' %}
<br/>
<a href="/wap_attribute_def_check/?delete=0" class="function_button">删除属性</a><br/>
{% else %}
{% endif %}
---<br/>
<a href="/wap_attribute_def/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% else  %}
删除:{{attribute.name}}[{{attribute.attribute}}]<br/>
---<br/>
<a href="/wap_attribute_def_check/?delete=1" class="function_button">确定</a> | <a href="/wap_attribute_def_check/?delete=2" class="function_button">取消</a><br/>
{% endif  %}
<!-- /container -->
 {% endblock %}
