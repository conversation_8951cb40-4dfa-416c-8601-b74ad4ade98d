{% extends 'wap_base.html' %}

{% block title %}基础属性{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[基础属性]
<form action="/wap_attribute_basic/" method="post">
    {% csrf_token %}
    游戏名称:<input name="name" value={{c.name}} ><br/>
    游戏状态:<select name="is_state">
    {% if c.is_state == 0 %}
    <option value = 0 selected="selected">设计中</option>
    <option value = 1 >正常</option>
    <option value = 2 >维护</option>
    {% elif c.is_state == 1 %}
    <option value = 0>设计中</option>
    <option value = 1 selected="selected">正常</option>
    <option value = 2 >维护</option>
    {% else %}
    <option value = 0 >设计中</option>
    <option value = 1>正常</option>
    <option value = 2  selected="selected">维护</option>
    {% endif %}
    </select><br/>
    <font color="red">是否公开展示</font>:<select name="is_backstage">
    {% if c.is_backstage == 0 %}
    <option value = 0 selected="selected">禁止</option>
    <option value = 1 >允许</option>
    {% else %}
    <option value = 0 >禁止</option>
    <option value = 1 selected="selected">展示</option>
    {% endif %}
    </select>(非展示请选禁止)<br/>
    提示设置:[<font color="red">提示语代码：messages]</font><br/>
    <textarea name="html_messages"  maxlength="99999" rows="4" cols="50">{{c.html_messages  | safe}}</textarea><br/>
    提示设置:[<font color="red">提示语代码：tishi_messages]【推荐速度快】</font><br/>
    <textarea name="html_tishi_messages"  maxlength="99999" rows="4" cols="50">{{c.html_tishi_messages  | safe}}</textarea><br/>
    游戏描述:<br/>
    <textarea name="desc"  maxlength="99999" rows="4" cols="50">{{c.desc  | safe}}</textarea><br/>
    货币名称:<input name="money_name" value={{c.money_name}} ><br/>
    组队上限:<input name="team_max_count" value={{c.team_max_count}} ><br/>
    经验/潜力公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="exp_expression" maxlength="99999" rows="3" cols="50">{{c.exp_expression}}</textarea><br/>
    灵气公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="lingqi_expression" maxlength="99999" rows="3" cols="50">{{c.lingqi_expression}}</textarea><br/>
    金钱公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="money_expression" maxlength="99999" rows="3" cols="50">{{c.money_expression}}</textarea><br/>
    粮食公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="liangcao_expression" maxlength="99999" rows="3" cols="50">{{c.liangcao_expression}}</textarea><br/>
    生铁公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="shengtie_expression" maxlength="99999" rows="3" cols="50">{{c.shengtie_expression}}</textarea><br/>
    石料公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="shiliao_expression" maxlength="99999" rows="3" cols="50">{{c.shiliao_expression}}</textarea><br/>
    木材公式(怪物掉落没有定义时则执行此公式):<br/>
    <textarea name="mucai_expression" maxlength="99999" rows="3" cols="50">{{c.mucai_expression}}</textarea><br/>
    游戏内全局CSS:<a href="/wap_page_list/?page_name=全局css">(修改{{css_count}})</a><br/>
    游戏内全局script:<a href="/wap_page_list/?page_name=全局script">(修改{{script_count}})</a><br/>
    全局样式(设计后台):<br/>
    <textarea name="css" maxlength="99999" rows="10" cols="50">{{c.css}}</textarea><br/>
    全局脚本(设计后台):<br/>
    <textarea name="js" maxlength="99999" rows="10" cols="50">{{c.js}}</textarea><br/>
    个人全局代码:<br/>
    <textarea name="info_message" maxlength="99999" rows="10" cols="50">{{c.info_message}}</textarea><br/>
    入口地图:
    {% if c.map_id == 0 %}
    <a href="/wap_area_name/?object_name=map&object_z_bh=1000">选择场景</a><br/>
    {% else %}
    <a href="/wap_area_name/?object_name=map&object_z_bh=1000">{{maps.name}}({{maps.id}})</a><br/>
    {% endif %}
    默认技能:
    {% if c.skill_id == 0 %}
    <a href="/wap_area_name_list/?object_name=skill&object_z_bh=1001">选择技能</a><br/>
    {% else %}
    <a href="/wap_area_name_list/?object_name=skill&object_z_bh=1001">{{skills.name}}({{skills.id}})</a><br/>
    {% endif %}
    <input name="submit" type="submit" title="保存" value="保存"/><br/>
</form>
---<br/>
<a href="/wap_admin/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
