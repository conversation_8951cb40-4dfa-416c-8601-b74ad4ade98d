{% extends 'wap_base.html' %}

{% block title %}公共表达式{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.delete1 == 0 %}
[表达式]<br/>
<form action="/wap_expression_check/" method="post">
    {% csrf_token %}
    表达式名称:<input type="text" name="name" value={{expression.name}} ><br/>
    表达式属性:{{expression.attribute}}<br/>
    表达式内容:<textarea name="expression"  maxlength="99999" rows="4" cols="40">{{expression.expression}}</textarea><br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
<br/>
<a href="/wap_expression_check/?delete=0"  class="function_button">删除表达式</a><br/>
---<br/>
<a href="/wap_expression/"  class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% else  %}
删除:{{expression.attribute}}<br/>
---<br/>
<a href="/wap_expression_check/?delete=1"  class="function_button">确定</a> | <a href="/wap_expression_check/?delete=2"  class="function_button">取消</a><br/>
{% endif  %}
<!-- /container -->
 {% endblock %}
