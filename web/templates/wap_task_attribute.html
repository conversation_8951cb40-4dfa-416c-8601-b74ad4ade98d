{% extends 'wap_base.html' %}

{% block title %}定义任务{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[任务属性](任务ID:{{objects.id}})<br/>
---<br/>
<form action="/wap_task_attribute/" method="post">
    {% csrf_token %}
    名称:<input name="name" value={{objects.name}}><br/>
    类型:<select name="type">
    {% if objects.type == 0 or objects.type == '0' %}
    <option value = 0 selected="selected">办事任务</option>
    <option value = 1 >杀怪任务</option>
    <option value = 2 >寻物任务</option>
    {% elif objects.type == 1 or objects.type == '1' %}
    <option value = 0 >办事任务</option>
    <option value = 1 selected="selected">杀怪任务</option>
    <option value = 2 >寻物任务</option>
    {% else %}
    <option value = 0 >办事任务</option>
    <option value = 1 >杀怪任务</option>
    <option value = 2 selected="selected">寻物任务</option>
    {% endif %}
    </select><br/>
    放弃:<select name="remove">
    {% if objects.remove == 0 or objects.remove == '0' %}
    <option value = 0  selected="selected">不可以</option>
    <option value = 1 >可以</option>
    {% else %}
    <option value = 0>不可以</option>
    <option value = 1  selected="selected">可以</option>
    {% endif %}
    </select><br/>
    分类:<select name="area_name">
    {% for area_name in area_names %}
    {% if objects.area_name == area_name.area_name %}
    <option value = "{{area_name.area_name}}" selected="selected">{{area_name.area_name}}</option>
    {% else %}
    <option value = "{{area_name.area_name}}" >{{area_name.area_name}}</option>
    {% endif %}
    {% endfor %}
    </select><br/>
    任务NPC:
    {% if objects.npc_id == 0 or objects.npc_id == '0' %}
    <a href="/wap_area_name/?object_name=npc&object_z_bh=104">选择NPC</a><br/>
    {% else %}
    <a href="/wap_area_name/?object_name=npc&object_z_bh=104">{{objects.npc_name}}({{objects.npc_id}})</a> <a href="/wap_task_attribute/?delete=100">取消</a><br/>
    {% endif %}
    触发条件:<textarea name="display"  maxlength="99999" rows="5" cols="40">{{objects.display  | safe}}</textarea><br/>
    执行条件:<textarea name="execute_display"  maxlength="99999" rows="5" cols="40">{{objects.execute_display  | safe}}</textarea><br/>
    满足提示:<textarea name="content"  maxlength="99999" rows="5" cols="40">{{objects.content  | safe}}</textarea><br/>
    不满提示:<textarea name="not_content"  maxlength="99999" rows="5" cols="40">{{objects.not_content  | safe}}</textarea><br/>
    任务描述:<textarea name="desc"  maxlength="99999" rows="5" cols="40">{{objects.desc  | safe}}</textarea><br/><br/>
    满足执行代码:<br/><textarea name="code"  maxlength="99999" rows="10" cols="51">{{objects.code  | safe}}</textarea><br/>
    完成执行代码:<br/><textarea name="submit_code"  maxlength="99999" rows="10" cols="51">{{objects.submit_code  | safe}}</textarea><br/>
    {% if objects.type == 1 or objects.type == '1' %}
    ┏杀怪任务:<a href="/wap_area_name/?object_name=npc&object_z_bh=105">添怪</a><br/>
    {{neyong | safe}}
    {% elif objects.type == 2 or objects.type == '2' %}
    ┏寻物任务:<a href="/wap_area_name/?object_name=item&object_z_bh=106">添物</a><br/>
    {{neyong | safe}}
    {% else %}
    {% endif %}
    <br/>
    <input name="submit" type="submit" title="保存" value="保存"/><br/>
</form>
---<br/>
<a href="/wap_task/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}

