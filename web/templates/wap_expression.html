{% extends 'wap_base.html' %}

{% block title %}公共表达式{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[公共表达式]<br/>
---<br/>
{{neyong |safe}}
<br/>
---<br/>
<a href="/wap_expression_create/?create=1"  class="function_button">添加表达式</a><br/>
{% if not expression %}
{% elif sjk == 5 %}
<a href="/wap_expression/?update=1"  class="function_button">更新所有表达式</a><br/>
{% else %}
{% endif %}
---<br/>
<a href="/wap_admin/"  class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}