{% extends 'wap_base.html' %}

{% block title %}{{maps.name}}{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[定义属性]<br/>
---<br/>
<form action="/wap_area_name_list_attribute/" method="post">
    {% csrf_token %}
    {% if request.session.object_name == 'map' or request.session.object_name == 'npc' or request.session.object_name == 'item' %}
    分类:<select name="area_name">
    {% for object_areas in object_area %}

    {% if objects.area_name == object_areas.area_name %}
    <option value ={{object_areas.area_name}} selected="selected">{{object_areas.area_name}}</option>
    {% else %}
    <option value ={{object_areas.area_name}} >{{object_areas.area_name}}</option>
    {% endif %}
    {% endfor %}
    </select><br/>
    {% else %}
    {% endif %}
    {% if name_count == 0 %}
        名称:<textarea name="name"  maxlength="99999" rows="1" cols="40">{{objects.name  | safe}}</textarea><br/>
    {% else %}
    {% endif %}
    {% if name_desc == 0 %}
        描述:<textarea name="desc"  maxlength="99999" rows="4" cols="40">{% if objects.desc != 0 and objects.desc != '0' and objects.desc != ''  %}{{objects.desc  | safe}}{% else %}{% endif %}</textarea><br/>
    {% else %}
    {% endif %}
    {% if objects.area_name == '装备' or objects.area_name == '武将装备' or objects.area_name == '士兵装备' or objects.area_name == '坐骑装备' or objects.area_name == '宠物装备' %}
    类型:<select name="type">
    {% for object_types in object_type %}
    {% if objects.type == object_types.type %}
    <option value ={{object_types.type}} selected="selected">{{object_types.type}}</option>
    {% else %}
    <option value ={{object_types.type}} >{{object_types.type}}</option>
    {% endif %}
    {% endfor %}
    </select><br/>
    {% else %}
    {% endif %}
    {% if request.session.object_name == 'item' %}
        {% if pm_money == 0 %}
            商城:<input  name="pm_money" size="12" value="{{objects.pm_money}}"> 金元<br/>
        {% else %}
        {% endif %}
        {% if money_money == 0 %}
            商城:<input  name="money" size="12" value="{{objects.money}}"> {{c.money_name}}<br/>
        {% else %}
        {% endif %}
    {% elif request.session.object_name == 'npc' and objects.is_kill == '1' or request.session.object_name == 'npc' and objects.is_kill == 1 %}
        {% if objects.is_boss == '0' or objects.is_boss == 0 %}
            是否BOSS:<select name="is_boss">
            <option value =0 selected="selected">否</option>
            <option value =1 >是</option>
            </select><br/>
        {% else %}
            是否BOSS:<select name="is_boss">
            <option value =0 >否</option>
            <option value =1 selected="selected">是</option>
            </select><br/>
            BOSS刷新:<input  name="refresh_time" size="12" value="{{objects.refresh_time}}"> 秒<br/>
        {% endif %}
    {% else %}
    {% endif %}
    {% if request.session.object_name == 'item' %}
        对像:<select name="duixiang">
        {% if objects.duixiang == 0 or objects.duixiang == '0' or objects.duixiang == '' %}
            <option value = 0 selected="selected">不限</option>
            <option value = 1 >人物</option>
            <option value = 4 >坐骑</option>
            <option value = 3 >士兵</option>
            <option value = 2 >宠物/武将</option>
            <option value = 5 >宠物/武将+士兵</option>
        {% elif objects.duixiang == 1 or objects.duixiang == '1' %}
            <option value = 0 >不限</option>
            <option value = 1 selected="selected">人物</option>
            <option value = 4 >坐骑</option>
            <option value = 3 >士兵</option>
            <option value = 2 >宠物/武将</option>
            <option value = 5 >宠物/武将+士兵</option>
        {% elif objects.duixiang == 2 or objects.duixiang == '2' %}
            <option value = 0 >不限</option>
            <option value = 1 >人物</option>
            <option value = 4 >坐骑</option>
            <option value = 3 >士兵</option>
            <option value = 2 selected="selected">宠物/武将</option>
            <option value = 5 >宠物/武将+士兵</option>
        {% elif objects.duixiang == 3 or objects.duixiang == '3' %}
            <option value = 0 >不限</option>
            <option value = 1 >人物</option>
            <option value = 4 >坐骑</option>
            <option value = 3 selected="selected">士兵</option>
            <option value = 2 >宠物/武将</option>
            <option value = 5 >宠物/武将+士兵</option>
        {% elif objects.duixiang == 4 or objects.duixiang == '4' %}
            <option value = 0 >不限</option>
            <option value = 1 >人物</option>
            <option value = 4 selected="selected">坐骑</option>
            <option value = 3 >士兵</option>
            <option value = 2 >宠物/武将</option>
            <option value = 5 >宠物/武将+士兵</option>
        {% elif objects.duixiang == 5 or objects.duixiang == '5' %}
            <option value = 0 >不限</option>
            <option value = 1 >人物</option>
            <option value = 4 >坐骑</option>
            <option value = 3 >士兵</option>
            <option value = 2 >宠物/武将</option>
            <option value = 5 selected="selected">宠物/武将+士兵</option>
        {% else %}
            <option value = 100 >出错了{{objects.duixiang}}</option>
        {% endif %}
        </select><br/>
    {% else %}
    {% endif %}
    {{neyong| safe}}
    <input name="submit" type="submit" title="保存" value="保存"/><br/>
</form>
---<br/>
<a href="/wap_area_name_list_see/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}