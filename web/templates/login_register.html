{% extends 'base.html' %}
{% block title %}/{% endblock %}
{% block content %}

{% if neyong == '' or neyong == '0' %}
<form action="/login_register/" method="GET">
    <h1>登陆</h1>
     {% if messages %}<div style="color:red">{{ messages }}</div>{% endif %}
    <input type="hidden" name="login" value="login"><!--隐藏，用于区分注册还是登陆提交-->
    账号:<input  name="username"><br/>
    密码:<input type="password" name="password"><br/>
    <input name="submit" type="submit" title="确定登陆" value="确定登陆"/><br/>
</form>
<form action="/login_register/" method="GET">
    <h1>注册账号</h1>
     {% if messages %}<div style="color:red">{{ messages }}</div>{% endif %}
    <input type="hidden" name="register" value="register"><!--隐藏，用于区分注册还是登陆提交-->
    填写账号:<input  name="username_1"><br/>
    填写密码:<input  name="password_1"><br/>
    确认密码:<input  name="password_2"><br/>
    手机号码:<input  name="number_1"><br/>
    安全码:<input  name="security_1"><br/>
    <input name="submit" type="submit" title="确定注册" value="确定注册"/><br/>
</form>
<a href="/index/">返回首页</a>

{% else %}
{{neyong | safe}}
<style>
{{c.css_login_register | safe}}
</style>
<script>
{{c.js_login_register | safe}}
</script>
{% endif %}

{% endblock %}