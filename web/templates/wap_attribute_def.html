{% extends 'wap_base.html' %}

{% block title %}定义属性{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.object_name == 'map' %}
[地图属性o]<br/>
{% elif request.session.object_name == 'npc' %}
[NPC属性o]<br/>
{% elif request.session.object_name == 'u' %}
[人物属性u]<br/>
{% elif request.session.object_name == 'chongwu' %}
[宠物/武将属性o]<br/>
{% elif request.session.object_name == 'zuoqi' %}
[坐骑属性o]<br/>
{% elif request.session.object_name == 'shibing' %}
[士兵属性o]<br/>
{% elif request.session.object_name == 'item' %}
[物品属性o]<br/>
{% elif request.session.object_name == 'skill' %}
[技能属性m]<br/>
{% else %}
{% endif %}
---<br/>
{{neyong | safe}}
<!--
{% for attribute in attributes %}
<a href="/wap_attribute_def_check/?iid={{attribute.id}}">{{attribute.name}}[{{attribute.attribute}}]</a><br/>
{% endfor %}
-->
<br/>
<a href="/wap_attribute_def_create/?" class="function_button">添加属性</a><br/>
---<br/>
<a href="/wap_attribute/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
