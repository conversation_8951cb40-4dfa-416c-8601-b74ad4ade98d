{% extends 'base.html' %}
{% block title %}主页{% endblock %}
{% block content %}
{% if messages %}<div style="color:red">{{ messages | safe}}</div>{% endif %}
{% if request.session.is_login %}
    {% if neyong == '' or neyong == '0' %}
        你好,{{ request.session.user_name }}(ID:{{ request.session.user_id }})！欢迎回来！<br />
         ---<br/>
         {% if user.sid == '1' or user.sid == ''  or user.sid == 1 %}
             <a href="/wap_index/?username={{user.name}}&password={{user.password}}&fuwuqi_xianlu=1&sj={{sj}}">进入游戏</a> <br/>
             <a href="/logout/">退出登陆</a><br />
         {% else %}
             <a href="/wap_index/?user_name={{user.name}}&sid={{user.sid}}&fuwuqi_xianlu=1&sj={{sj}}">进入游戏</a> <br/>
             <a href="/logout/">退出登陆</a><br />
         {% endif %}
             <a href="/logout/">退出登陆</a><br />
<!-- <a href="https://3gqq.cn/game/g102/">3GQQ家园</a><br />-->
<!-- 
         {% if fuwuqi_xianlu2 < 200 %}
            <a href="http://zhsh.xintm.net:33333/wap_index/?username={{user.name}}&password={{user.password}}&fuwuqi_xianlu=2&sj={{sj}}">线路二</a> (
             {% if fuwuqi_xianlu2 < 60 %}
            优秀
            {% elif fuwuqi_xianlu2 < 150 %}
            良好
            {% elif fuwuqi_xianlu2 < 180 %}
            拥挤
             {% else %}
            爆满
             {% endif %}
         {% else %}
            线路二 (爆满
         {% endif %}
        {{fuwuqi_xianlu2_count}}%)<br/>
        
        -->
    {% else %}
        {{neyong | safe}}
        <style>
        {{c.css_index_2 | safe}}
        </style>
        <script>
        {{c.js_index_2 | safe}}
        </script>
    {% endif %}
{% else %}
    {% if neyong == '' or neyong == '0' %}
        <a href="/login/">登录</a> | <a href="/register/">注册</a><!-- |  <a href="https://3gqq.cn/game/g102/">3GQQ家园</a>--><br />
        <a href="/set_password/">忘记密码</a><br />
        你尚未登录<br />
    {% else %}
        {{neyong | safe}}
        <style>
        {{c.css_index_1 | safe}}
        </style>
        <script>
        {{c.js_index_1 | safe}}
        </script>
    {% endif %}
{% endif %}
{% if user.is_designer == 'True' and user.name == '13427940188'%}
<br/>
<form action="/index/" method="post">
    {% csrf_token %}
    游戏状态:<select name="game_area_name">
    <option value = '运营中' selected="selected">运营中</option>
    <option value = '公测中' >公测中</option>
    <option value = '内测中' >内测中</option>
    <option value = '开发中' >开发中</option>
    <option value = '展示中' >展示中</option>
    </select><br/>
    游戏名称:<input type="text" name="game_name"><br/>
    标题图片:<input type="text" name="img_0_code"><br/>
    网站地址:<input type="text" name="game_http"><br/>
    游戏ID:<input type="text" name="game_id"><br/>
    游戏描述:<textarea name="game_desc"  maxlength="99999" rows="4" cols="40"></textarea><br/>
    图片URL1:<input type="text" name="img_1_code"/><br/>
    图片URL2:<input type="text" name="img_2_code"/><br/>
    图片URL3:<input type="text" name="img_3_code"/><br/>
    图片URL4:<input type="text" name="img_4_code"/><br/>
    图片URL5:<input type="text" name="img_5_code"/><br/>
    图片URL6:<input type="text" name="img_6_code"/><br/>
    图片URL7:<input type="text" name="img_7_code"/><br/>
    图片URL8:<input type="text" name="img_8_code"/><br/>
    图片URL9:<input type="text" name="img_9_code"/><br/>
    图片URL10:<input type="text" name="img_10_code"/><br/>
    <input name="submit" type="submit" title="修改" value="修改"/><br/>
</form>
{% else %}
{% endif %}
<link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.css">
<link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
<script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.min.js"></script>
<script src="https://unpkg.com/swiper/swiper-bundle.js"> </script>
<script src="https://unpkg.com/swiper/swiper-bundle.min.js"> </script>
<script src="https://unpkg.com/swiper/swiper-bundle.min.js.map"> </script>
<script>
    var mySwiper = new Swiper ('.swiper', {
    autoplay: true,
    touchRatio:1,
    speed:50000,  //匀速时间
    loop : true,
    freeMode:true,  //设置为true则变为free模式
    slidesPerView:1,
    slidesPerGroup:1,
})

</script>
{% endblock %}