{% extends 'wap_base.html' %}

{% block title %}设计者管理{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[设计者管理]<br/>
---<br/>
{{neyong | safe }}
<br/>
{% if userss.id == user.id or user.id == 1 or user.id == '1' %}
<form action="/wap_is_designer/" method="GET">
    <table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <tr>
        <td>
    帐号:<input style="height:20px" type="text"  name="is_designer">
    <input style="height:30px" name="submit" type="submit" title="添加设计" value="添加设计"/><br/>
    </td>
    </tr>
</table>
</form>
{% else %}

{% endif %}
<br/>
[论坛版主]<br/>
---<br/>
{{forum_neyong | safe }}
{% if userss.id == user.id or user.id == 1 or user.id == '1' or user.is_designer == 'True' %}
<br/>
<form action="/wap_is_designer/" method="GET">
    <table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <tr>
        <td>
    帐号:<input style="height:20px" type="text"  name="forum_designer">
    <input style="height:30px" name="submit" type="submit" title="添加版主" value="添加版主"/><br/>
        </td>
    </tr>
</table>
</form>

{% else %}

{% endif %}
<br/>
{% if userss.id == user.id %}
---<br/>
<form action="/wap_is_designer/" method="POST">
    {% csrf_token %}
    <table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:blue;"><b>修改密码</b></th>
    <tr>
        <td>
    帐号:<input style="height:20px" type="text"  name="username">
        </td>
    </tr>
    <tr>
        <td>
    密码:<input style="height:20px" type="text"  name="password">
        </td>
    </tr>
    <tr>
        <td>
            <input style="height:30px" name="submit" type="submit" title="重置密码" value="重置密码"/>
        </td>
    </tr>

</table>
</form>
{% else %}

{% endif %}
---<br/>
<a href="/wap_admin/" class="function_button">返回上级</a><br />
<!-- /container -->
 {% endblock %}