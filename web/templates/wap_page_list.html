{% extends 'wap_base.html' %}

{% block title %}模板内容{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[{{request.session.page_name}}模板]<br/>
---<br/>
{{neyong | safe}}
<br/>
---<br/>
{% if request.session.page_name == '全局css' %}
<a href="/wap_page_list_create/?create=3" class="function_button">添加style</a><br/>
<font color="red"><b>执行顺序:显示条件-->CSS</b>(显示条件未达成则不执行)<br/></font>
---<br/>
<a href="/wap_attribute_basic/">返回上级</a><br/>
{% elif request.session.page_name == '全局script' %}
<a href="/wap_page_list_create/?create=4" class="function_button">添加script</a><br/>
<font color="red"><b执行顺序:显示条件-->script</b>(显示条件未达成则不执行)<br/></font>
---<br/>
<a href="/wap_attribute_basic/">返回上级</a><br/>
{% else %}
    {% if request.session.page_name != '0' and request.session.page_name != '' and request.session.page_name != 0 %}
      <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan1">
         <div class="nav1">基本操作</div>
         <div class="nav2">
    <a href="/wap_page_list_create/?create=1" class="function_button">添加操作/文本</a>
    <a href="/wap_page_list_create/?create=2" class="function_button">添加输入框</a>
    <a href="/wap_page_list_create/?create=3" class="function_button">添加style</a>
    <a href="/wap_page_list_create/?create=4" class="function_button">添加script</a>
         </div>
      </div>
    <br/>
      <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan2">
         <div class="nav1">进阶操作</div>
         <div class="nav2">
             {% if request.session.page_name == '场景' and c.changjing == 0 or request.session.page_name == '状态' and c.zhuangtai == 0 %}
            <a href="/wap_page_list/?is_kill=1" class="function_button">启用模板</a>
            {% elif request.session.page_name == '场景' and c.changjing == 1  or request.session.page_name == '状态' and c.zhuangtai == 1 %}
           <a href="/wap_page_list/?is_kill=0" class="function_button">禁用模板</a>
            {% else %}
            {% endif %}
         <a href="/wap_page_list/?delete=1" class="function_button">删除模板</a>
             <a>
                <form action="/wap_page_list/" method="POST">
                {% csrf_token %}
                清空模板:<input name="delete_page_list"  size="4" />
               <input name="submit" type="submit" title="清空" value="清空"/>【输入yes清除】<br/>
             </form>
             </a>
         </div>
      </div>
    <br/>
    {% if userss.id == request.session.user_id %}
      <div class="nav-wrapper" onmouseover="tan(this.id)" id="xuan3">
         <div class="nav1">高阶操作</div>
         <div class="nav2">
    <a href="/wap_page_list_export/?page_list_export=1" class="function_button">导出当前模板数据</a>
    <a href="/wap_page_list_import/?page_list_import=1" class="function_button">导入当前模板数据</a>
    <a href="/wap_page_list_export/?page_list_export=2" class="function_button">导出所有模板数据</a>
    <a href="/wap_page_list_import/?page_list_import=2" class="function_button">导入所有模板数据</a>
         </div>
      </div>
      <br/>
    {% else %}
    {% endif %}
    {% else %}
    {% endif %}
    <font color="red"><b>操作显示执行顺序:操作代码-->显示条件-->操作内容</b><br/></font>
    ---<br/>
    <a href="/wap_page/" class="function_button">返回上级</a><br/>
{% endif %}

<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}