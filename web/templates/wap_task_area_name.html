{% extends 'wap_base_wap.html' %}

{% block title %}设计任务{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[任务分类]<br/>
---<br/>
{% for object in objects %}
<a href="/wap_task/?area_name={{object.area_name}}">{{object.area_name}}</a><br/>
{% endfor %}
<br/>
<br>
<form action="/wap_task_area_name/" method="POST">
     {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <tr>
        <td>
            任务分类：<input style="height:20px" type="text"  name="create_area_name"  placeholder="任务分类名称">
            <input style="height:30px" name="submit" type="submit" title="确定创建" value="确定创建"/>
        </td>
    </tr>
</table>
</form>
<br/>
---<br/>
<a href="/wap_admin/" class="function_button">返回上级</a><br/>
<!-- /container -->
 {% endblock %}
