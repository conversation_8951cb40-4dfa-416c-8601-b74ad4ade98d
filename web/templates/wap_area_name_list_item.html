{% extends 'wap_base.html' %}

{% block title %}
{% if request.session.object_name == 'item'%}
放置物品
{% elif request.session.object_name == 'npc'%}
掉落物品
{% else %}
{% endif %}
{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.object_name == 'map'%}
放置物品<br/>
{% elif request.session.object_name == 'npc'%}
[掉落定义]<br/>
<form action="/wap_area_name_list_item/" method="post">
    {% csrf_token %}
    粮草(liangcao)表达式:<textarea name="liangcao_expression" maxlength="99999" rows="3" cols="40">{{objects.liangcao_expression}}</textarea><br/>
    石料(shiliao)表达式:<textarea name="shiliao_expression" maxlength="99999" rows="3" cols="40">{{objects.shiliao_expression}}</textarea><br/>
    生铁(shengtie)表达式:<textarea name="shengtie_expression" maxlength="99999" rows="3" cols="40">{{objects.shengtie_expression}}</textarea><br/>
    木材(mucai)表达式:<textarea name="mucai_expression" maxlength="99999" rows="3" cols="40">{{objects.mucai_expression}}</textarea><br/>
    经验/潜能(exp)表达式:<textarea name="exp_expression" maxlength="99999" rows="3" cols="40">{{objects.exp_expression}}</textarea><br/>
    灵气(lingqi)表达式:<textarea name="lingqi_expression" maxlength="99999" rows="3" cols="40">{{objects.lingqi_expression}}</textarea><br/>
    银两(money)表达式:<textarea name="money_expression" maxlength="99999" rows="3" cols="40">{{objects.money_expression}}</textarea><br/>
    物品掉落选择:<select style="height:25px" name="is_drop">
    {% if objects.is_drop == 0 or objects.is_drop == '0' %}
    <option  value = 0 selected="selected">掉到背包</option>
    <option  value = 1 >掉到地上</option>
    {% else %}
    <option  value = 0 >掉到背包</option>
    <option  value = 1 selected="selected">掉到地上</option>
    {% endif %}
    </select><br/>
    <input style="height:25px" name="submit" type="submit" title="保存设置" value="保存设置"/><br/>
</form>
<br/>
掉落物品<br/>
    {% else %}
    {% endif %}
---<br/>
{{neyong | safe}}
<br/>
{% if request.session.object_name == 'map'%}
<a href="/wap_area_name/?object_name=item&object_z_bh=101" class="function_button">放置物品</a><br/>
{% elif request.session.object_name == 'npc'%}
<a href="/wap_area_name/?object_name=item&object_z_bh=102" class="function_button">掉落物品</a><br/>
{% else %}
{% endif %}
---<br/>
<a href="/wap_area_name_list_see/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}