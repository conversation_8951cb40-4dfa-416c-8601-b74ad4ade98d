{% extends 'base.html' %}
{% load static %}
{% block title %}登录{% endblock %}
{% block css %}
<link rel="stylesheet" href="{% static 'css/login.css' %}">
{% endblock %}
{% block content %}
    {% if messages %}<div style="color:red">{{ messages | safe}}</div>{% endif %}
{% if neyong == '' or neyong == '0' %}
{{t.zhsh | safe}}<br/>
还记得那年夏天这款十分好玩的wap文字游戏吗？现在的你是不是很怀念？实在怀念那个无忧无虑的年纪，还是在怀念那年夏天的ta……
       哆哆四海以大航海时代故事为背景，为玩家构建了一个富有深度和乐趣的冒险故事主线在。这款游戏中玩家可以化身成为一名冒险者，玩家互动，自由交易，种菜偷菜，成立自己的帮会，攻城略地争霸四海。诸多特殊BOSS、副本探险等热血沸腾的特色玩法与游戏时代的天地巨变完美结合，让你在热闹忙碌的都市也能感受到四海世界的宁静与澎湃……<br/><br/>
<form action="/login/" method="GET">
    <input type="hidden" name="login" value="login"><!--隐藏，用于区分注册还是登陆提交-->
    账号:<input  name="username"  onclick="showTip(this)" onmouseout="hideTip()" title="请输入帐号"><br/>
    密码:<input type="password" name="password"  onclick="showTip(this)" onmouseout="hideTip()" title="请输入密码"><br/>
    <input name="submit" type="submit" title="登陆" value="登陆"/> |  <button type="reset">重置</button><br/>
</form>
<div id="tip"></div>
<a href="/register/">注册帐号</a> | <a href="/index/">网站首页</a> <!--| <a href="https://3gqq.cn/game/g102/">3GQQ家园</a>--><br/>
<script>
function showTip(obj) {
  var tip = document.getElementById("tip");
  tip.innerHTML = obj.title;
  tip.style.display = "block";
  tip.style.left = obj.offsetLeft + "px";
  tip.style.top = obj.offsetTop + obj.offsetHeight - 58 + "px";
}
function hideTip() {
  var tip = document.getElementById("tip");
  tip.style.display = "none";
}
</script>
<style>
#tip {
  position: absolute;
  /*background-color: #fff;*/
  background-color: rgba(0, 0, 0, 0.75);
  border: 1px solid #ccc;
  padding: 5px;
  display: none;
  color:#fff;
}
</style>
{% else %}
{{neyong | safe}}
<style>
{{c.css_login | safe}}
</style>
<script>
{{c.js_login | safe}}
</script>
{% endif %}
{% endblock %}