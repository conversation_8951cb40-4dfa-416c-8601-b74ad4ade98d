{% extends 'wap_base.html' %}

{% block title %}操作{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
<font color="red">【导入模板数据】</font><br/>
<form action="/wap_page_list_import/" method="post">
    {% csrf_token %}
    <textarea name="code"  maxlength="999999" rows="20" cols="80"></textarea><br/>
    <br/>
    {% if request.session.object_ck_id == '2' %}
<font color="red">所有数据导入不可逆，所有模板及内容将会被清除，请谨慎操作</font><br/>
{% else %}
{% endif %}
     <input name="submit" type="submit" title="导入数据" value="导入数据"/><br/>
</form>
---<br/>
<a href="/wap_page_list/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
