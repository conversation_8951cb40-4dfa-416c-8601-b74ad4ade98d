{% extends 'wap_base.html' %}

{% block title %}放置NPC数量{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
<form action="/wap_task_item/" method="post">
    {% csrf_token %}
    {% if request.session.object_name == 'npc' %}
    {{objects.npc_name}} 数量:<input name="npc_count"  value={{objects.npc_count}} size="5">
    {% elif request.session.object_name == 'item' %}
    {{objects.item_name}} 数量:<input name="item_count"  value={{objects.item_count}} size="5">
    {% else %}
    {% endif %}
    <input name="submit" type="submit" title="修改" value="修改"/><br/>
</form>
---<br/>
<a href="/wap_task_attribute/?object_name=task" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
