{% extends 'wap_base.html' %}

{% block title %}分区设置{% endblock %}
{% block content %}



<form action="/wap_game_area_name_change/" method="post">
    {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    {% if request.session.object_z_bh == '0' %}
    <tr><td style="text-align:center;"><b>【创建分区】</b></td></tr>
    <tr><td  style="height:16px;">{{messages | safe}}</td></tr>
    <tr>
        <td>分区名称:<input type="text"  name="area_name" placeholder="初入江湖"></td>
    </tr>
    <tr>
        <td>开区时间:<input type="text"  name="start_time" placeholder="2020/08/08 16:20">格式2020/08/08 16:20</td>
    </tr>
    {% else %}
    <tr><td style="text-align:center;"><b>【修改{{objects.area_id}}区[{{objects.area_name}}]】</b></td></tr>
    <tr><td  style="height:16px;">{{messages | safe}}</td></tr>
    <tr>
        <td>分区名称:<input type="text"  name="area_name" value="{{objects.area_name}}"></td>
    </tr>
    <tr>
        <td>开区时间:<input type="text"  name="start_time" value="{{objects.start_time}}">格式2020/08/08 16:20</td>
    </tr>
    {% endif %}
    {% if request.session.object_z_bh == '0'%}
    <tr>
        <td><input type="checkbox" name="is_neice" id="id_is_neice" /><label for="id_is_neice">是否为内测区</label></td></tr>
    {% elif  objects.is_neice == 'True' %}
    <tr>
        <td><input type="checkbox" name="is_neice" id="id_is_neice1" checked="checked"/><label for="id_is_neice1">是否为内测区</label></td></tr>
    {% else %}
     <tr>
        <td><input type="checkbox" name="is_neice" id="id_is_neice2" /><label for="id_is_neice2">是否为内测区</label></td></tr>
    {% endif %}
    {% if request.session.object_z_bh == '0' %}
    <tr>
        <td><input name="submit" type="submit" title="创建分区" value="创建分区" style="font-size:16px;"/><br/></td>
    </tr>
    {% else %}
    <tr>
        <td><input name="submit" type="submit" title="修改分区" value="修改分区" style="font-size:16px;"/><br/></td>
    </tr>
    {% endif %}
</table>
</form>
---<br/>
<a href="/wap_game_area_name/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
