{% extends 'wap_base.html' %}

{% block title %}排行榜管理{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[排行榜设置]<br/>
---<br/>
{{neyong | safe}}
---<br/>
<br>
<form action="/wap_ranking_area_name/" method="POST">
    {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:red;">创建排行榜</th>
    <tr><td>排行名称：<input style="height:20px" type="text"  name="create_ranking_name"  placeholder="等级榜"></td></tr>
    <tr><td>排行属性：<input style="height:20px" type="text"  name="create_ranking_sx"  placeholder="lvl"></td></tr>
    <tr><td>显示条件：<input style="height:20px" type="text"  name="create_ranking_expression"  placeholder="u.lvl > 10"></td></tr>
    <tr><td>显示数量：<input style="height:20px" type="text"  name="create_ranking_count"  placeholder="10"></td></tr>
    <tr><td>
        显示对象：
    <select style="height:20px" name="create_ranking_object">
    <option value = "u" selected="selected">人物</option>
    <option value = "chongwu" >宠物</option>
    </select>
    </td></tr>
    <tr><td><input style="height:30px" name="submit" type="submit" title="创建排行榜" value="创建排行榜"/></td></tr>
</table>
</form>
<br/>
<a href="/wap_admin/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
