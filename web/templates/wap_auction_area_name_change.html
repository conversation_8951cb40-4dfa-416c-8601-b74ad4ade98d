{% extends 'wap_base.html' %}

{% block title %}修改交易行{% endblock %}
{% block content %}
<form action="/wap_auction_area_name_change/" method="POST">
     {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:blue;"><b>修改【<a style="color:red;">{{objects.auction_name}}</a>】</b></th>
    <tr><td>{% if messages %}<a style="color:red;">{{messages | safe}}</a>{% else %}{% endif %}<br/></td></tr>
    <tr><td>币种名称：<input style="height:20px" type="text"  name="create_money_name"  value="{{objects.money_name}}"></td></tr>
    <tr><td>币种属性：<input style="height:20px" type="text"  name="create_money_sx"  value="{{objects.money_sx}}"></td></tr>
    <tr><td>成交费用：<input style="height:20px" type="text"  name="create_service_charge"  value="{{objects.service_charge}}"> %</td></tr>
    <tr><td>收费每分：<input style="height:20px" type="text"  name="create_money_minute"  value="{{objects.money_minute}}">币/分</td></tr>
    <tr><td>最大时间：<input style="height:20px" type="text"  name="create_max_minute"  value="{{objects.max_minute}}"> 分</td></tr>
    <tr><td><input style="height:30px" name="submit" type="submit" title="确定修改" value="确定修改"/></td></tr>
</table>
</form>
<a href="/wap_auction_area_name/?delete=0&auction_name={{objects.auction_name}}" class="function_button">删除交易行</a><br/>
<br/>
<a href="/wap_auction_area_name/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
