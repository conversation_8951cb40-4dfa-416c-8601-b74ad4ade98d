{% extends 'wap_base.html' %}

{% block title %}定义表达式{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[定义表达式]<br/>
<form action="/wap_expression_create/" method="post">
    {% csrf_token %}
    表达式名称:<input type="text" name="name" /><br/>
    表达式属性:<input type="text" name="attribute" /><br/>
    表达式内容:<textarea name="expression"  maxlength="99999" rows="4" cols="40"></textarea><br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
---<br/>
<a href="/wap_expression/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
