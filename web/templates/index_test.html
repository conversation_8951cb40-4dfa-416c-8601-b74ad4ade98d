<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
</head>
<body>
<input id = "in_" >
<div id = "out_"></div>
<button name="subject" type="submit" value="submit" onclick=run_on()>go</button>
<script>
    function run_on() {
        $.ajax({
            type: "GET",
            url: "/index_test/",
            data: {aa: $("#in_").val(), jquery_ajax:1},
            dataType: "json",
            success: function (data) {
                alert(data.a);
                 $("#out_").text(data.a); 
            }
        });

    }

</script>
</body>
</html>