{% extends 'wap_base.html' %}

{% block title %}
{% if request.session.object_name == 'player' %}
人物公共事件
{% elif request.session.object_name == 'item' %}
物品公共事件
{% elif request.session.object_name == 'npc' %}
电脑人物公共事件
{% elif request.session.object_name == 'chongwu' %}
宠物公共事件
{% elif request.session.object_name == 'zuoqi' %}
座骑公共事件
{% elif request.session.object_name == 'shibing' %}
士兵公共事件
{% elif request.session.object_name == 'map' %}
场景公共事件
{% elif request.session.object_name == 'xitong' %}
系统公共事件
{% elif request.session.object_name == 'pk' %}
战斗公共事件
{% else %}
{% endif %}
{% endblock %}
{% block content %}
{% if request.session.delete_eventlist == 0 %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[{{event.name}}(ID:{{event.id}})]<br/>
<form action="/wap_event_list_check/" method="post">
    {% csrf_token %}
    事件全局代码(变量在以下所有步骤中均有效，属性也能保存):<br/>
    <textarea name="code"  maxlength="99999" rows="10" cols="50">{{event.code  | safe}}</textarea><br/>
    <br/><input name="submit" type="submit" title="保存代码" value="保存代码"/><br/><br/>
</form>
{% if event_list != '' %}
---<br/>
{{event_list | safe}}
---<br/>
{% else %}
{% endif %}
<a href="/wap_event_list_check/?create_event=0"  class="function_button">添加事件步骤</a><br/>
---<br/>
<a href="/wap_event_list/"  class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% else  %}
删除事件{{eventlist.position}}<br/>
---<br/>
<a href="/wap_event_list_check/?delete_eventlist=1"  class="function_button">确定</a> | <a href="/wap_event_list_check/?delete_eventlist=2"  class="function_button">取消</a><br/>
{% endif %}
<!-- /container -->
 {% endblock %}
