﻿{% extends 'wap_base.html' %}

{% block title %}事件{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.delete1 == 0 and request.session.delete_eventlist == 0 %}
[操作id:{{operation.id}}]-[事件id:{{event.id}}]<br/>
<form action="/wap_page_list_event/" method="post">
    {% csrf_token %}
    事件全局代码(变量在以下所有步骤中均有效，属性也能保存):<br/>
    <textarea name="code"  maxlength="99999" rows="10" cols="50">{{event.code  | safe}}</textarea><br/>
    <br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
{% if not event.id %}
{% else %}
{% if event_list != '' %}
---<br/>
{{event_list | safe}}
---<br/>
{% else %}
{% endif %}
<br/>
<a href="/wap_page_list_event/?create_event=0" class="function_button">添加事件步骤</a><br/>
<a href="/wap_page_list_event/?delete=0" class="function_button">删除所有事件</a><br/>
{% endif %}
---<br/>
<a href="/wap_page_list_check/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% elif request.session.delete_eventlist == 1 %}
删除事件{{eventlist.position}}<br/>
---<br/>
<a href="/wap_page_list_event/?delete_eventlist=1" class="function_button">确定</a> | <a href="/wap_page_list_event/?delete_eventlist=2" class="function_button">取消</a><br/>
{% else  %}
删除:{{event.code  | safe}}<br/>
---<br/>
<a href="/wap_page_list_event/?delete=1" class="function_button">确定</a> | <a href="/wap_page_list_event/?delete=2" class="function_button">取消</a><br/>
{% endif  %}
<!-- /container -->
 {% endblock %}
