{% extends 'wap_base.html' %}
{% block title %}
   {% if request.session.object_name == 'map' or request.session.object_name == 'npc'%}
   {{request.session.area_name}}
   {% elif request.session.object_name == 'chongwu' %}
   宠物列表
   {% elif request.session.object_name == 'zuoqi' %}
   坐骑列表
   {% elif request.session.object_name == 'shibing' %}
   士兵列表
   {% elif request.session.object_name == 'item'%}
   物品列表
   {% elif request.session.object_name == 'skill'%}
   技能列表
   {% else %}
   {% endif %}
{% endblock %}
{% block content %}
{% if messages %}{{messages |safe}}<br/>{% else %}{% endif %}
<form action="/wap_area_name_list/" method="GET">
     模糊查找:<input name="ck_name" type="text" size="10"/>
    {% if request.session.object_name == 'map' or request.session.object_name == 'npc' or request.session.object_name == 'item' %}
    <select name="ck_type">
    <option value = "全部"  >全部</option>
    <option value = "{{request.session.area_name}}" selected="selected">{{request.session.area_name}}</option>
    </select>
    {% else %}
    {% endif %}
    <input style="height:20px" name="submit" type="submit" title="查找" value="查找"/><br/>
</form>
{% if request.session.object_name == 'map'%}
[{{request.session.area_name}}]地图列表<br/>
{% elif request.session.object_name == 'npc'%}
[{{request.session.area_name}}]NPC列表<br/>
{% elif request.session.object_name == 'chongwu'%}
宠物列表<br/>
{% elif request.session.object_name == 'zuoqi'%}
坐骑列表<br/>
{% elif request.session.object_name == 'shibing'%}
士兵列表<br/>
{% elif request.session.object_name == 'item'%}
{% if request.session.area_name == '装备' or request.session.area_name == '坐骑装备' or request.session.area_name == '士兵装备' or request.session.area_name == '武将装备' %}
<form action="/wap_area_name_list/" method="POST">
     {% csrf_token %}
        装备类型: <select name="type" style="height:20px">
     {% if request.session.type == '' or request.session.type == '全部' %}
        <option value = "全部" selected="selected"> 全部 </option>
     {% else %}
        <option value = "全部"> 全部 </option>
     {% endif %}
    {% for itemtype in itemtypes %}
        {% if itemtype.item_areaname == request.session.area_name %}
            {% if itemtype.type == request.session.type %}
                <option value = "{{itemtype.type}}" selected="selected"> {{itemtype.type}} </option>
            {% else %}
                <option value = "{{itemtype.type}}"> {{itemtype.type}} </option>
            {% endif %}
        {% else %}
        {% endif %}
    {% endfor %}
    </select>
    <input style="height:20px" name="submit" type="submit" title="查找" value="查找"/>
</form>

{% else %}
物品列表<br/>
{% endif %}
{% elif request.session.object_name == 'skill'%}
技能列表<br/>
{% else %}
{% endif %}
---<br/>
{{neyong | safe}}
<!--
{% block objects %}
{% for object in objects %}
    {% if request.session.object_z_bh == '0' %}
        <a href="/wap_area_name_list_see/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '100' %}<!-放置电脑人物->
        <a href="/wap_area_name_list_npc/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '107' %}<!-批量放置电脑人物->
        <a href="/wap_area_name_list_npc_count/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '101' or request.session.object_z_bh == '102'%}<!-放置物品->
        <a href="/wap_area_name_list_item/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '103' %}<!-放置技能->
        <a href="/wap_area_name_list_skill/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '104' %}<!-任务NPC->
        <a href="/wap_task_attribute/?iid={{object.id}}&object_name=task" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '105' %}<!-任务NPC->
        <a href="/wap_task_attribute/?npc_iid={{object.id}}&object_name=task" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '106' %}<!-任务物品->
        <a href="/wap_task_attribute/?item_iid={{object.id}}&object_name=task" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '1000' %}<!-选择初始场景入口->
        <a href="/wap_attribute_basic/?map_iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% elif request.session.object_z_bh == '1001' %}<!-选择初始技能->
        <a href="/wap_attribute_basic/?skill_iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% else %}<!-地图出口1-4->
        <a href="/wap_area_name_list_export/?iid={{object.id}}" class="function_button">{{object.name}}(ID：{{object.id}})</a><br/>
    {% endif %}
{% endfor %}
{% endblock %}
-->
<br/>
{% if request.session.object_z_bh == '0' %}
    {% if request.session.object_name == 'map'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加地图</a><br/>
        <a href="/wap_area_name_list_create_map/?create=2" class="function_button">批量添加地图</a><br/>
        <a href="/wap_area_name/?object_name=npc&object_z_bh=107&area_name={{request.session.area_name}}" class="function_button">批量放置电脑人物</a><br/>
        <a href="/wap_area_name_change_map/?change=1" class="function_button">修改地图区域名</a><br/>
        <a href="/wap_area_name_list/?delete=1" class="function_button">删除此区域名称</a><br/>
    {% elif request.session.object_name == 'npc'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加电脑人物</a><br/>
        <a href="/wap_area_name_change_map/?change=1" class="function_button">修改区域名称</a><br/>
        <a href="/wap_area_name_list/?delete=1" class="function_button">删除区域名称</a><br/>
    {% elif request.session.object_name == 'chongwu'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加宠物</a><br/>
    {% elif request.session.object_name == 'zuoqi'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加坐骑</a><br/>
    {% elif request.session.object_name == 'shibing'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加兵种</a><br/>
    {% elif request.session.object_name == 'skill'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加技能</a><br/>
    {% elif request.session.object_name == 'item' and request.session.area_name != '全部'%}
        <a href="/wap_area_name_list/?create=1" class="function_button">添加物品</a><br/>
        <a href="/wap_area_name_change_map/?change=1" class="function_button">修改物品类别</a><br/>
    {% else %}
    {% endif %}
{% else %}
{% endif %}
---<br/>
{% if request.session.object_z_bh == '1001' %}
    <a href="/wap_attribute_basic/" class="function_button">返回上级</a><br/>
{% elif request.session.object_z_bh == '103' %}
    <a href="/wap_area_name_list_skill/" class="function_button">返回上级</a><br/>
{% elif request.session.object_name == 'shibing' or request.session.object_name == 'zuoqi' or request.session.object_name == 'chongwu' or request.session.object_name == 'skill' and request.session.object_z_bh != '1001' %}
    <a href="/wap_admin/" class="function_button">返回上级</a><br/>
{% else %}
    <a href="/wap_area_name/?object_name={{request.session.object_name}}" class="function_button">返回上级</a><br/>
{% endif %}
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
