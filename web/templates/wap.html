{% extends 'base_wap.html' %}
{% block title %}{{c.name}}{% endblock %}
{% block content %}
<!--<div id="zhezhao_11111" onclick="next_11111()" style="display:none"></div>
<div id="header_11111" style="display:none"></div>步骤提示语弹窗提示
</div>-->
<div id="wap_class">
{{ neyong | safe}} 
{{neyong_css_js | safe}}
<!-- /container -->
</div>
<script>
    //当为ajax时
//document.getElementById(v_id).style.pointer-events = 'none'
//$('input[type="submit"]').on('click',function(){
//$(this).attr('disabled','true')
//document.getElementsByName('submit')[0].style.display = 'none';
//$(this).parent().submit()
//})
$('form').submit(function(){
var $btn = $('form').find('input[type="submit"]');
$btn.attr('disabled', 'disabled');
return true
});
$(document).click(function run_on(e) {
    v_id=e.target.id; //当前点击的元素ID
    // key = $("#"+ v_id).attr('key')
    //document.getElementById(v_id).style.disabled = true;
    //$("a").attr("disabled", true)
    //document.getElementById(v_id).innerHTML = '1111'
    //console.log(v_id)
    if(e.target.localName == "a"){
        $("a").css("pointer-events","none");
        console.log('执行A标签')
    }else{console.log('非点击链接')}
if(v_id!='wap_class' && v_id!= '' && v_id!='zhezhao_11111'){
        wap_ajax = '{'+'"jquery_ajax":"1",' + $("#"+ v_id).val() + '}'
        console.log(wap_ajax)
        //alert(wap_ajax)弹窗
        wap_ajax = JSON.parse(wap_ajax);
        $.ajax({
            type: "GET",
            url: "/wap/",
            data: wap_ajax,
            dataType: "json",
            success: function (data) {
                clearInterval(my_Timerdjs)//停止某个js
               $("#wap_class").html(data.neyong + data.neyong_css_js); 
            }
        });
}else{
}
    //$(#v_id).removeAttr('href');//去掉a标签中的href属性
    //$('a').removeAttr('onclick');//去掉a标签中的onclick事件
   // $('a').attr('disabled',"true")
    });
</script>
 {% endblock %}