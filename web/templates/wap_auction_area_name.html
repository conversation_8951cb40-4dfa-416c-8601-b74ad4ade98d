{% extends 'wap_base.html' %}

{% block title %}交易管理{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[交易行设置]<br/>
---<br/>
{{neyong | safe}}
---<br/>
<br>
<form action="/wap_auction_area_name/" method="POST">
    {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:red;">创建交易行</th>
    <tr><td>交易行名：<input style="height:20px" type="text"  name="create_auction_name"  placeholder="金钱交易行"></td></tr>
    <tr><td>币种名称：<input style="height:20px" type="text"  name="create_money_name"  placeholder="金钱"></td></tr>
    <tr><td>币种属性：<input style="height:20px" type="text"  name="create_money_sx"  placeholder="money"></td></tr>
    <tr><td>成交费用：<input style="height:20px" type="text"  name="create_service_charge"  placeholder="5 交易成功后收取费用"> %</td></tr>
    <tr><td>收费每分：<input style="height:20px" type="text"  name="create_money_minute"  placeholder="100 每分收取上架费用">币/分</td></tr>
    <tr><td>最大时间：<input style="height:20px" type="text"  name="create_max_minute"  placeholder="5000 最大上架时间"> 分</td></tr>
    <tr><td><input style="height:30px" name="submit" type="submit" title="创建交易行" value="创建交易行"/></td></tr>
</table>
</form>
<br/>
<a href="/wap_admin/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
