{% extends 'wap_base_wap.html' %}

{% block title %}{{c.name}}{% endblock %}
{% block content %}
{% if messages %}{{messages | safe}}<br/>{% else %}{% endif %}
{% if c.is_backstage == 1 %}
<a href="/wap_admin/">设计后台(展示中)</a><br/>
{% else %}
{% endif %}
{% if neyong == '' or neyong == '0' %}
{{c.desc |safe}}
---<br/>
{{进入游戏 |safe}}
{{设计游戏 |safe}}
---<br/>
<a href="/wap_index/">返回上级</a><br />
<a href="/logout/">退出登陆</a><br />
<!-- /container -->

{% else %}
{{neyong | safe}}
<style>
{{c.css_wap_choose | safe}}
</style>
<script>
{{c.js_wap_choose | safe}}
</script>
{% endif %}
 {% endblock %}
