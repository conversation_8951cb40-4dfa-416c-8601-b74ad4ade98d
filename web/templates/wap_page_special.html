{% extends 'wap_base.html' %}

{% block title %}特殊页面{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
<form action="/wap_page_special/" method="post">
    {% csrf_token %}

    <b>【网站首页页面】登陆前</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_index_1" maxlength="99999" rows="10" cols="60">{{c.html_index_1}}</textarea><br/>
    style<textarea name="css_index_1" maxlength="99999" rows="5" cols="60">{{c.css_index_1}}</textarea><br/>
    script<textarea name="js_index_1" maxlength="99999" rows="5" cols="60">{{c.js_index_1}}</textarea><br/>
    <br/>

    <b>【网站首页页面】登陆后</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_index_2" maxlength="99999" rows="10" cols="60">{{c.html_index_2}}</textarea><br/>
    style<textarea name="css_index_2" maxlength="99999" rows="5" cols="60">{{c.css_index_2}}</textarea><br/>
    script<textarea name="js_index_2" maxlength="99999" rows="5" cols="60">{{c.js_index_2}}</textarea><br/>
    <br/>
    <b>【登陆+注册页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_login_register" maxlength="99999" rows="10" cols="60">{{c.html_login_register}}</textarea><br/>
    style<textarea name="css_login_register" maxlength="99999" rows="5" cols="60">{{c.css_login_register}}</textarea><br/>
    script<textarea name="js_login_register" maxlength="99999" rows="5" cols="60">{{c.js_login_register}}</textarea><br/>
    <br/>

    <b>【登陆页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_login" maxlength="99999" rows="10" cols="60">{{c.html_login}}</textarea><br/>
    style<textarea name="css_login" maxlength="99999" rows="5" cols="60">{{c.css_login}}</textarea><br/>
    script<textarea name="js_login" maxlength="99999" rows="5" cols="60">{{c.js_login}}</textarea><br/>
    <br/>

    <b>【注册页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_register" maxlength="99999" rows="10" cols="60">{{c.html_register}}</textarea><br/>
    style<textarea name="css_register" maxlength="99999" rows="5" cols="60">{{c.css_register}}</textarea><br/>
    script<textarea name="js_register" maxlength="99999" rows="5" cols="60">{{c.js_register}}</textarea><br/>
    <br/>

    <b>【重置密码/忘记密码页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_set_password" maxlength="99999" rows="10" cols="60">{{c.html_set_password}}</textarea><br/>
    style<textarea name="css_set_password" maxlength="99999" rows="5" cols="60">{{c.css_set_password}}</textarea><br/>
    script<textarea name="js_set_password" maxlength="99999" rows="5" cols="60">{{c.js_set_password}}</textarea><br/>
    <br/>

    <b>【修改密码页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_change_password" maxlength="99999" rows="10" cols="60">{{c.html_change_password}}</textarea><br/>
    style<textarea name="css_change_password" maxlength="99999" rows="5" cols="60">{{c.css_change_password}}</textarea><br/>
    script<textarea name="js_change_password" maxlength="99999" rows="5" cols="60">{{c.js_change_password}}</textarea><br/>
    <br/>

    <b>【修改安全码页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_change_security" maxlength="99999" rows="10" cols="60">{{c.html_change_security}}</textarea><br/>
    style<textarea name="css_change_security" maxlength="99999" rows="5" cols="60">{{c.css_change_security}}</textarea><br/>
    script<textarea name="js_change_security" maxlength="99999" rows="5" cols="60">{{c.js_change_security}}</textarea><br/>
    <br/>

    ---<br/>
    <b>【游戏首页/分区选择页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_wap_index" maxlength="99999" rows="10" cols="60">{{c.html_wap_index}}</textarea><br/>
    style<textarea name="css_wap_index" maxlength="99999" rows="5" cols="60">{{c.css_wap_index}}</textarea><br/>
    script<textarea name="js_wap_index" maxlength="99999" rows="5" cols="60">{{c.js_wap_index}}</textarea><br/>
    <br/>

    <b>【分区信息页面】</b><br/>
    <font color="red"><b>禁止使用｛%  %｝</b>此类符号</font>style/script不用写标签:<br/>
    html<textarea name="html_wap_choose" maxlength="99999" rows="10" cols="60">{{c.html_wap_choose}}</textarea><br/>
    style<textarea name="css_wap_choose" maxlength="99999" rows="5" cols="60">{{c.css_wap_choose}}</textarea><br/>
    script<textarea name="js_wap_choose" maxlength="99999" rows="5" cols="60">{{c.js_wap_choose}}</textarea><br/>
    <br/>

    ---<br/>
    <input name="submit" type="submit" title="保存" value="保存"/><br/>
</form>
---<br/>
<a href="/wap_admin/" class="function_button">返回上级</a>
<br />
<!-- /container -->
 {% endblock %}
