{% extends 'wap_base.html' %}

{% block title %}操作{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[操作ID:{{operation.id}}]<br/>
{% if request.session.create == '1' %}
<form action="/wap_page_list_create/" method="post">
    {% csrf_token %}
    代码:<textarea name="code"  maxlength="99999" rows="4" cols="40">{{operation.code | safe}}</textarea><br/>
    内容:<textarea name="content"  maxlength="99999" rows="4" cols="40">{{operation.content | safe}}</textarea><br/>
    显示:<textarea name="display"  maxlength="99999" rows="4" cols="40">{{operation.display | safe}}</textarea><br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
{% else %}
<form action="/wap_page_list_create/" method="post">
    {% csrf_token %}
    代码:<textarea name="code"  maxlength="99999" rows="4" cols="40">{{operation.code | safe}}</textarea><br/>
    内容:<textarea name="content"  maxlength="99999" rows="4" cols="40">{{code}}</textarea><br/>
    显示:<textarea name="display"  maxlength="99999" rows="4" cols="40">{{operation.display | safe}}</textarea><br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
{% endif %}
---<br/>
<a href="/wap_page_list/" class="function_button">返回上级</a>
<br />
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
