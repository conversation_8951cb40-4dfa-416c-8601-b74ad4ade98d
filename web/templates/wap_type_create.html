{% extends 'wap_base.html' %}

{% block title %}添加装备类型{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
[添加类型]<br/>
<form action="/wap_type_create/" method="post">
    {% csrf_token %}
    装备类型:<input type="text"  name="type"><br/>
    装备归类:<select name="item_areaname">
    {% for item_areaname in item_areanames %}
    <option value ={{item_areaname.area_name}} selected="selected">{{item_areaname.area_name}}</option>
    {% endfor %}
    </select><br/>
    <input name="submit" type="submit" title="确定" value="确定"/><br/>
</form>
---<br/>
<a href="/wap_type/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
