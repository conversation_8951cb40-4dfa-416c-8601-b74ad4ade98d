{% extends 'wap_base.html' %}

{% block title %}{{request.session.area_name}}{% endblock %}
{% block content %}
{% if messages %}<a style="color:red;">{{messages | safe}}</a><br/>{% else %}{% endif %}
{{neyong | safe}}

<br>
<form action="/wap_tupian_area_name_list/" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:blue;">添加本地图片</th>
    <tr>
        <td>
            标识:<input style="height:20px" type="text"  name="create_attribute"  placeholder="图片标识"/>
        </td>
    </tr>
    <tr>
        <td>
            名称:<input style="height:20px" type="text"  name="create_name"  placeholder="图片名称"/>
        </td>
    </tr>
    <tr>
        <td>
            地址:<input style="height:20px;width:80%;" type="file"  name="create_value"/>
        </td>
    </tr>
    <tr>
        <td>
            代码:<input style="height:20px;width:80%;" type="text"  name="create_code"  placeholder='style="width:50px;height:50px;" id="1"'/>
        </td>
    </tr>
    <tr>
        <td>
            <input style="height:30px" name="submit" type="submit" title="添加图片" value="添加图片"/>
        </td>
    </tr>
</table>
</form>
<br>
<form action="/wap_tupian_area_name_list/" method="GET">
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <tr>
        <td>
            图片分类：<input style="height:20px" type="text"  name="create_area_name"  placeholder="更改后图片分类名称">
            <input style="height:30px" name="submit" type="submit" title="修改分类名称" value="修改分类名称"/>
        </td>
    </tr>
</table>
</form>
<br/>
<br/>
<a href="/wap_tupian_area_name/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
