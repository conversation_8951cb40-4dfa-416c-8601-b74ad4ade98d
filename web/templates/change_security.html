{% extends 'base.html' %}

{% block title %}修改安全码{% endblock %}
{% block content %}
{% if neyong == '' or neyong == '0' %}
【修改安全码】<br/>
<form action="/change_security/" method="GET">
     {% if messages %}<div style="color:red">{{ messages }}</div>{% endif %}
    <input type="hidden" name="change_security" value="change_security"><!--隐藏-->
    旧安全码:<input  name="security"><br/>
    新安全码:<input  name="security_1"><br/>
    再次确认:<input  name="security_2"><br/>
    <input name="submit" type="submit" title="修改安全码" value="修改安全码"/><br/>
</form>
<a href="/index/">返回上级</a><br />
{% else %}
{{neyong | safe}}
<style>
{{c.css_change_security | safe}}
</style>
<script>
{{c.js_change_security | safe}}
</script>
{% endif %}
 {% endblock %}
