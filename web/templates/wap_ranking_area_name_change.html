{% extends 'wap_base.html' %}

{% block title %}修改排行榜{% endblock %}
{% block content %}
<form action="/wap_ranking_area_name_change/" method="POST">
     {% csrf_token %}
<table style="border:1px solid black;width:100%;padding:10px 10px 10px;">
    <th style="color:blue;"><b>修改【<a style="color:red;">{{objects.ranking_name}}</a>】</b></th>
    <tr><td>{% if messages %}<a style="color:red;">{{messages | safe}}</a>{% else %}{% endif %}<br/></td></tr>
    <tr><td>排行名称：<input style="height:20px" type="text"  name="create_ranking_name"  value="{{objects.ranking_name}}"></td></tr>
    <tr><td>排行属性：<input style="height:20px" type="text"  name="create_ranking_sx"  value="{{objects.ranking_sx}}"></td></tr>
    <tr><td>显示条件：<input style="height:20px" type="text"  name="create_ranking_expression"  value="{{objects.ranking_expression}}"></td></tr>
    <tr><td>显示数量：<input style="height:20px" type="text"  name="create_ranking_count"  value="{{objects.ranking_count}}"></td></tr>
    <tr><td>
        显示对象：
    <select style="height:20px" name="create_ranking_object">
    {% if objects.ranking_object == 'u' %}
        <option value = "u" selected="selected">人物</option>
        <option value = "chongwu" >宠物</option>
    {% else %}
        <option value = "u">人物</option>
        <option value = "chongwu" selected="selected">宠物</option>
    {% endif %}
    </select>
    </td></tr>
    <tr><td><input style="height:30px" name="submit" type="submit" title="确定修改" value="确定修改"/></td></tr>
</table>
</form>
<a href="/wap_ranking_area_name/?delete=0&ranking_id={{objects.id}}" class="function_button">删除排行榜</a><br/>
<br/>
<a href="/wap_ranking_area_name/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
<!-- /container -->
 {% endblock %}
