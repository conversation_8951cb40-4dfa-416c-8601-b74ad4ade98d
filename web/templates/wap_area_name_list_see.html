{% extends 'wap_base.html' %}

{% block title %}{{objects.name}}{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.delete1 == 0 %}
[{{objects_name |safe}}](ID:{{objects.id}}/{{request.session.object_id}})<br/>
---<br/>
<a href="/wap_area_name_list_attribute/?attribute=1" class="function_button">定义属性</a><br/>
{% if request.session.object_name == 'zuoqi' or request.session.object_name == 'chongwu' or request.session.object_name == 'skill' %}
{% else %}
<a href="/wap_area_name_list_operation/?operation=1" class="function_button">定义操作</a><br/>
{% endif %}
<a href="/wap_area_name_list_event/?def_event=1" class="function_button">定义事件</a><br/>
{% if request.session.object_name == 'map'%}
<a href="/wap_area_name_list_export/?export=1" class="function_button">定义出口</a><br/>
<a href="/wap_area_name_list_npc/" class="function_button">放置电脑人物</a><br/>
<a href="/wap_area_name_list_item/" class="function_button">地上物品</a><br/>
<!--进入地图-->
<a href = "/wap/?map_get_into_see={{map_get_into_see}}"  class="function_button">进入地图</a><br/>
<a href="/wap_area_name_list/?copy=0" class="function_button">复制地图</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除地图</a><br/>
<a href="/wap/?map_update=1" class="function_button">更新进入</a><br/>
{% elif request.session.object_name == 'npc'%}
<a href="/wap_area_name_list_sell/" class="function_button">出售物品</a><br/>
{% if objects.is_kill == '1' or  objects.is_kill == 1 %}
<a href="/wap_area_name_list_item/" class="function_button">怪物掉落</a><br/>
<a href="/wap_area_name_list_skill/" class="function_button">怪物技能</a><br/>
{% else %}
怪物掉落(NPC无此选项)<br/>
怪物技能(NPC无此选项)<br/>
{% endif %}
<a href="/wap_area_name_list/?copy=0" class="function_button">复制电脑人物</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除电脑人物</a><br/>
{% elif request.session.object_name == 'chongwu'%}
<a href="/wap_area_name_list/?copy=0" class="function_button">复制宠物</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除宠物</a><br/>
{% elif request.session.object_name == 'zuoqi'%}
<a href="/wap_area_name_list/?copy=0" class="function_button">复制坐骑</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除坐骑</a><br/>
{% elif request.session.object_name == 'item'%}
<a href="/wap_area_name_list/?copy=0" class="function_button">复制物品</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除物品</a><br/>
{% elif request.session.object_name == 'skill'%}
<a href="/wap_area_name_list/?copy=0" class="function_button">复制技能</a><br/>
<a href="/wap_area_name_list_see/?delete=0" class="function_button">删除技能</a><br/>
{% else %}
{% endif %}
---<br/>
<a href="/wap_area_name_list/" class="function_button">返回上级</a><br/>
<a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% else  %}
删除:{{objects.name}}[{{request.session.object_id}}]<br/>
---<br/>
<a href="/wap_area_name_list_see/?delete=1" class="function_button">确定</a> | <a href="/wap_area_name_list_see/?delete=2" class="function_button">取消</a><br/>
{% endif  %}
<!-- /container -->
 {% endblock %}
