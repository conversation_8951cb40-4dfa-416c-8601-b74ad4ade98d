{% extends 'wap_base.html' %}
{% block title %}步骤{% endblock %}
{% block content %}
{% if messages %}{{messages}}<br/>{% else %}{% endif %}
{% if request.session.event_xs == '0' %}
    [操作id:{{operation.id}}]-[事件id:{{event.id}}]-[步骤:{{eventlist.position}}]<br/>
    <form action="/wap_area_name_list_event_check_list/" method="post">
        {% csrf_token %}
        触发条件:<textarea name="display"  maxlength="99999" rows="4" cols="40" >{% if eventlist.display != 0 %}{{eventlist.display  | safe}}{% else %}{% endif %}</textarea><br/><br/>
        执行条件:<textarea name="execute_display"  maxlength="99999" rows="4" cols="40">{% if eventlist.execute_display != 0 %}{{eventlist.execute_display  | safe}}{% else %}{% endif %}</textarea><br/><br/>
        满足提示:<textarea name="content"  maxlength="99999" rows="4" cols="40">{% if eventlist.content != 0 %}{{eventlist.content | safe}}{% else %}{% endif %}</textarea><br/><br/>
        不满提示:<textarea name="not_content"  maxlength="99999" rows="4" cols="40">{% if eventlist.not_content != 0 %}{{eventlist.not_content  | safe}}{% else %}{% endif %}</textarea><br/><br/>
        执行代码:<textarea name="code"  maxlength="99999" rows="15" cols="40">{% if eventlist.code != 0 %}{{eventlist.code  | safe}}{% else %}{% endif %}</textarea><br/><br/>
        定义属性:<a href="/wap_area_name_list_event_check_list/?xs=1">设置({{count}})</a><br/>
        <br/>
        <input name="submit" type="submit" title="保存步骤" value="保存步骤"/><br/>
    </form>
    <br/>
    ---<br/>
    <a href="/wap_area_name_list_event_check/" class="function_button">返回上级</a><br/>
    <a href="/wap_admin/" class="function_button">设计大厅</a><br/>
{% elif request.session.event_xs == '1' %}
    定义属性:<br/>
    {{params_list | safe}}
    <br/>
<!--
    属性源代码:<br/>
    <textarea name="code"  maxlength="99999" rows="15" cols="40">{{params_code | safe}}</textarea><br/><br/>
    <br/>
-->
    <a href="/wap_area_name_list_event_check_list/?xs=2&key=0&value=0">添加属性</a><br/>
    <a href="/wap_area_name_list_event_check_list/?xs=0&key=0&value=0">返回上级</a><br/>
{% elif request.session.event_xs == '2' %}
    <form action="/wap_area_name_list_event_check_list/" method="get">
        属性名:<input type="text"  name="attribute_params_key" value={{request.session.event_key}}><br/>
        属性值:<input type="text"  name="attribute_params_value" value="{{request.session.event_value}}"><br/>
        字符串请加上引号，如'张三'<br/>
        <br/>
        <input name="submit" type="submit" title="确定" value="确定"/><br/>
    </form>
    <a href="/wap_area_name_list_event_check_list/?xs=1&key=0&value=0">返回上级</a><br/>
{% else %}
{% endif %}

<!-- /container -->
 {% endblock %}
