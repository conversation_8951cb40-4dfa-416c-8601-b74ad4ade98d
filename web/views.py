from django.shortcuts import render, redirect
import ujson
import math
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.gzip import gzip_page  # 数据压缩
from django_redis import get_redis_connection  # redis缓存
from django.core.cache import cache  # 引入缓存模块
from django.db.models import Sum
import urllib.request
# from django_redis import get_redis_connection  # 视图中使用redis缓存数据库
# from django.views.decorators.cache import cache_page  # 单独视图缓存,配合@使用
from web.models import GameMap, GameObject, GameMapAreaName, Attribute, PageName, Operation, Event, \
    GameNpc, ItemAreaName, ChongWu, Expression, GameMapPlaceNpc, EventAll, Item, ItemType, GetValue, \
    GameValue, GameMapPlaceItem, ChatAreaName, EventList, Skill, GameMapPlaceSkill, Task,GameAttributeNew, \
    TaskItem, Forum, ForumReply, GameImg, GameImgAreaName, ImgValue, SellGoods, BoothArea, \
    AuctionAreaName, TaskAreaName, RankingAreaName, ZuoQi, ShiBing, SystemAttribute, Img, \
    User, Player, GameNpcs, ItemPlayer, UseEquip, ChatMessage, Pets, Skills, QuickSkill, Team, InTeam, Mount, Ranking, \
    GangsTangkou, SanBu, Friends, \
    GangsMember, InGangs, Gangs, PetsOut, Card, ItemMap, Monster, AttackInfo, CangKuPlayer, Soldier, Battle, \
    PlayerAttribute, UtPlayer, \
    TaskPlayer, TaskItemPlayer, Auction, PublicAttribute, ZhuangYuan, OpenGame, Card,  \
    PublicAttributeNew, ImgAreaName, Statue, Booth, BoothPlayer, Enemy, GameAreaName, Suo, HouseList, House,RegisterNumber,InBangHui,BangHuiNumber,BangHui,CdkNumber,Transaction,BossRanking,BangHuiItem,UhPlayer,Plant
# PlayerAttribute GameAttribute

from random import choice
from django.db.models import Q
# from .forms import UserForm, RegisterForm
import hashlib
import re
# import datetime
import datetime, time
# from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
import threading
import calendar
from datetime import timedelta
import time
import string
import random
import tkinter
import base64

from django.http import JsonResponse

# 计算剩余时间
def is_time(n):
    if n > 0:
        day = ''
        if int(n / 86400) >= 1:
            day = '{}天'.format(int(n / 86400))
            n = n - int(n / 86400) * 86400
        if int(n / 3600) >= 1:
            day = '{}{}小时'.format(day, int(n / 3600))
            n = n - int(n / 3600) * 3600
        if int(n / 60) >= 1:
            day = '{}{}分'.format(day, int(n / 60))
            n = n - int(n / 60) * 60
        day = '{}{}秒'.format(day, int(n))
        return day
    else:
        return 0
def chongwu(iid):
    cw = ChongWu.objects.get(id=iid)
    return cw


def team_online(param):
    teams = Team.objects.filter(team_id=param)
    bh = 0
    for team in teams:
        if player_online(team.player_id) == '在线':
            bh = bh + 1
    return bh


def player_online(param):
    try:
        players = GameObject(Player.objects.get(id=param))
        if players.time + 1800 > int(time.time()):
            return '在线'
        else:
            return '离线'
    except:
        return '玩家不存在'


def index_test(request):
    a = 0
    if request.GET.get("aa"):
        a = request.GET.get("aa")
        return JsonResponse({"a": a})

    return render(request, "index_test.html", locals())


# 分钟事件
def run_minute_event():
    e = GetValue()  # 表达式属性
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    a = int(time.time()) - 1800
    u = 0
    if int(c.minute_event) != date('分'):
        c.set('minute_event', date('分'))
        c.save()
        players = Player.objects.filter(time__gte=a)  # 在线30分钟内所有玩家
        # player = list(player)
        for player in players:
            player = GameObject(player)
            # chat_messages(u, '运行分钟事件{}时{}分{}秒'.format(date('时'), date('分'), date('秒')))
            event_all = EventAll.objects.get(area_name='player', name='分钟事件')
            exec(event_all.code.replace('u.', 'player.'))  # 运行人物公共分钟事件
            """
            # 运行公共事件事件列表
            if EventList.objects.filter(event_all=event_all.id).count() == 0:
                pass
            else:
                eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
                eventlists = eventlists.extra(order_by=["num"])
                eventlists = dict.fromkeys(eventlists)
                for i in eventlists:
                    if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
                        if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
                            exec(i.code)  # 执行代码
                            params = ujson.loads(i.params)
                            data1, data2 = [], []
                            for iiii, j in params.items():
                                if len(iiii.split(".")) == 3:
                                    data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
                                else:
                                    data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
                                data2.append(f'{iiii.split(".")[0]}.save()')
                            params_code = '\n'.join(data1 + [""] + list(set(data2)))
                            exec(params_code)
                    else:
                        break  # 退出循环
            # print("运行分钟事件成功，当前等级是", u.lvl)
            # print("当前时间：", '{}/{}/{} {}:{}:{}'.format(date('年'), date('月'), date('日'),date('时'), date('分'), date('秒')))
            """


# 拍卖行事件
def run_auction_event():
    e = GetValue()  # 表达式属性
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    request = {}
    t = ImgValue()  # 代表图片
    if int(c.auction_event) != date('分'):
        c.set('auction_event', date('分'))
        c.save()
        auctions = Auction.objects.all()
        # u = Player.objects.get(id=2)
        # chat_message(u, '世界', '运行拍卖行事件({}:{}:{})'.format(date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
        auctions = list(auctions)
        for auction in auctions:
            # chat_message(u, '世界', '运行拍卖行事件:上架时间{}<=当前时间{}({}:{}:{})'.format(int(auction.time),date('time'),date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
            if int(auction.time) <= int(time.time()):
                # chat_message(u, '世界', '运行拍卖行事件玩家是否存在={}({}:{}:{})'.format(Player.objects.filter(id=auction.player_id).count(),date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                if Player.objects.filter(id=auction.player_id).count() > 0:
                    player = GameObject(Player.objects.get(id=auction.player_id))
                    zx = 1
                    # chat_message(u, '世界', '运行拍卖行事件拍卖品分类为:{}({}:{}:{})'.format(auction.area_name,date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                    if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                        # chat_message(u, '世界', '运行拍卖行事件拍卖品分类为1:{}({}:{}:{})'.format(auction.area_name, date('时'), date('分'),date('秒')))  # 发送世界消息不可点击玩家
                        # chat_message(u, '世界', '运行拍卖行事件拍卖品分类数量为:{}({}:{}:{})'.format(ItemPlayer.objects.filter(id=auction.item_id).count(), date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                        if ItemPlayer.objects.filter(id=auction.item_id).count() > 0:
                            wap_wp = ItemPlayer.objects.get(id=auction.item_id)
                            # chat_message(u, '世界', '有物品{}({}:{}:{})'.format(zx, date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                            if Item.objects.filter(id=wap_wp.item_id).count() == 0:
                                auction.delete()
                                zx = 0
                        else:
                            auction.delete()
                            zx = 0
                            # chat_message(u, '世界', '删除物品{}({}:{}:{})'.format(zx, date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                    else:
                        if Item.objects.filter(id=auction.item_id).count() == 0:
                            auction.delete()
                            zx = 0
                    # chat_message(u, '世界', '运行拍卖行事件执行{}({}:{}:{})'.format(zx,date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
                    if zx == 1:
                        if int(auction.new_player_id) > 0:
                            if Player.objects.filter(id=auction.new_player_id).count() == 0:
                                pass
                            else:
                                new_player = GameObject(Player.objects.get(id=auction.new_player_id))
                                auctionareaname = AuctionAreaName.objects.get(auction_name=auction.auction_name)
                                new_money = int(auction.new_money) - int(auction.new_money) * float(
                                    auctionareaname.service_charge) / 100
                                player.change('{}'.format(auction.money_sx), new_money, True)
                                chat_messages(player,
                                              '你.交易行的{}*{}，{}以{}{}购买成功(扣除手续费{}%,得到{})'.format(
                                                  auction.item_name,
                                                  auction.item_count,
                                                  new_player.name,
                                                  auction.new_money,
                                                  auction.money_name,
                                                  auctionareaname.service_charge,
                                                  new_money))  # 发送私聊消息不可点击玩家
                                if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                                    wp = GameObject(ItemPlayer.objects.get(id=auction.item_id))
                                else:
                                    wp = GameObject(Item.objects.get(id=auction.item_id))
                                if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                                    wp.set('player_id', new_player.id)
                                    wp.save()
                                    chat_messages(new_player,
                                                  '拍卖行竞拍得到{}*{}'.format(wp.name,
                                                                               auction.item_count))  # 发送私聊消息不可点击玩家
                                else:
                                    get_items(new_player, request, ut, g, c, t, e, auction.item_id,
                                              int(auction.item_count), is_bangding=False, is_message=False)
                                    chat_messages(new_player, '拍卖行竞拍得到{}*{}'.format(wp.name,
                                                                                           auction.item_count))  # 发送私聊消息不可点击玩家
                        else:
                            chat_messages(player, '交易行的{}*{}无人购买，已退回背包记得查收'.format(auction.item_name,
                                                                                                    auction.item_count))  # 发送私聊消息不可点击玩家
                            if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                                wp = GameObject(ItemPlayer.objects.get(id=auction.item_id))
                            else:
                                wp = GameObject(Item.objects.get(id=auction.item_id))
                            if auction.area_name == '装备' or auction.area_name == '武将装备' or auction.area_name == '士兵装备' or auction.area_name == '坐骑装备' or auction.area_name == '宠物装备':
                                wp.set('player_id', player.id)
                                wp.save()
                            else:
                                get_items(player, request, auction.item_id, int(auction.item_count), is_bangding=False,
                                          is_message=False)
                        auction.delete()  # 删除拍卖行物品信息
                        chat_messages(player, '你交易行')  # 发送私聊消息不可点击玩家
                else:
                    auction.delete()


# 系统分钟事件
def run_xitong_all():
    e = GetValue()  # 表达式属性
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    if int(c.xitong_all) != date('分'):
        c.set('xitong_all', date('分'))
        c.save()
        event_all = EventAll.objects.get(area_name='xitong', name='分钟事件')
        # u = Player.objects.get(id=224)
        # chat_message(u, '世界', '运行系统分钟事件({}:{}:{})'.format(date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
        if event_all.code != '':
            exec(event_all.code)  # 运行公共事件
        """
        # 运行公共事件事件列表
        if EventList.objects.filter(event_all=event_all.id).count() == 0:
            pass
        else:
            eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
            eventlists = eventlists.extra(order_by=["num"])
            eventlists = list(eventlists)
            for i in eventlists:
                if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
                    if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
                        exec(i.code)  # 执行代码
                        params = ujson.loads(i.params)
                        data1, data2 = [], []
                        for iiii, j in params.items():
                            if len(iiii.split(".")) == 3:
                                data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
                            else:
                                data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
                            data2.append(f'{iiii.split(".")[0]}.save()')
                        params_code = '\n'.join(data1 + [""] + list(set(data2)))
                        exec(params_code)
                else:
                    break  # 退出循环
                    """


# 排行榜事件
def run_ranking_event():
    # Ranking.objects.all().delete()
    e = GetValue()  # 表达式属性
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    t = ImgValue()  # 代表图片
    a = int(time.time()) - 1800
    # u = Player.objects.get(id=224)
    # chat_message(u, '世界', '运行排行榜事件({}:{}:{})'.format(date('时'), date('分'), date('秒')))  # 发送世界消息不可点击玩家
    if int(c.ranking_event) != date('分'):
        c.set('ranking_event', date('分'))
        c.save()
        rankingareanames = RankingAreaName.objects.all()
        rankingareanames = list(rankingareanames)
        for rankingareaname in rankingareanames:  # 所有排行属性
            if rankingareaname.ranking_object == 'u':
                players = Player.objects.all()  # filter(time__gte=a)  # 在线30分钟内所有玩家
            elif rankingareaname.ranking_object == 'chongwu':
                players = Pets.objects.filter(player_id__gte=1, area_id=0)
            else:
                players = 0
            players = list(players)
            for player in players:  # 所有玩家或者宠物
                if rankingareaname.ranking_object == 'u':
                    try:
                        usersss = User.objects.get(id=player.user_id)
                        is_designer = usersss.is_designer
                    except:
                        is_designer = 'True'  # 报错时下面不执行
                elif rankingareaname.ranking_object == 'chongwu':
                    try:
                        a = Player.objects.get(id=player.player_id)
                        usersss = User.objects.get(id=a.user_id)
                        is_designer = usersss.is_designer
                    except:
                        is_designer = 'True'  # 报错时下面不执行
                else:
                    is_designer = 'True'  # 下面不执行
                # ranking = Ranking.objects.all()
                # ranking.delete()
                if str(is_designer) == 'False':  # 只显示非设计者
                    if int(player.get('{}'.format(rankingareaname.ranking_sx))) > 0:
                        if rankingareaname.ranking_object == 'u':
                            # 为0时则创建
                            if Ranking.objects.filter(player_id=player.id, ranking_id=rankingareaname.id).count() == 0:
                                ranking = Ranking.objects.create()
                                ranking.chongwu_id = 0
                                ranking.area_id = player.area_id
                                ranking.ranking_object = 'u'
                                ranking.ranking_id = rankingareaname.id
                                ranking.player_id = player.id
                                ranking.value = player.get('{}'.format(rankingareaname.ranking_sx))
                                ranking.ranking_sx = rankingareaname.ranking_sx
                                ranking.save()
                            # 多条数据时则删除
                            elif Ranking.objects.filter(player_id=player.id, ranking_id=rankingareaname.id).count() > 1:
                                Ranking.objects.filter(player_id=player.id,
                                                       ranking_id=rankingareaname.id).first().delete()  # 删除多余
                            # 存在数据时则更新
                            else:
                                ranking = Ranking.objects.get(player_id=player.id, ranking_id=rankingareaname.id)
                                ranking.value = player.get('{}'.format(rankingareaname.ranking_sx))
                                ranking.ranking_sx = rankingareaname.ranking_sx
                                ranking.save()
                        else:
                            Ranking.objects.filter(chongwu_id=player.id, ranking_id=rankingareaname.id)
                            if Ranking.objects.filter(chongwu_id=player.id, ranking_id=rankingareaname.id).count() == 0:
                                ranking = Ranking.objects.create()
                                ranking.chongwu_id = player.id
                                ranking.ranking_id = rankingareaname.id
                                wj = Player.objects.get(id=player.player_id)
                                ranking.area_id = wj.area_id
                                ranking.ranking_object = 'chongwu'
                                ranking.player_id = player.player_id
                                ranking.value = player.get('{}'.format(rankingareaname.ranking_sx))
                                ranking.ranking_sx = rankingareaname.ranking_sx
                                ranking.save()
                            elif Ranking.objects.filter(chongwu_id=player.id,
                                                        ranking_id=rankingareaname.id).count() > 1:
                                Ranking.objects.filter(chongwu_id=player.id,
                                                       ranking_id=rankingareaname.id).first().delete()  # 删除多余
                            else:
                                ranking = Ranking.objects.get(chongwu_id=player.id, ranking_id=rankingareaname.id)
                                ranking.value = player.get('{}'.format(rankingareaname.ranking_sx))
                                ranking.ranking_sx = rankingareaname.ranking_sx
                                ranking.save()


#scheduler = BackgroundScheduler()
# scheduler.add_job(run_minute_event, 'cron', second='1')  # 每分钟运行一次，定秒
#scheduler.add_job(run_auction_event, 'interval', minutes=1)  # 交易行每分钟运行一次
#scheduler.add_job(run_xitong_all, 'interval', minutes=1)  # 系统事件每分钟运行一次
#scheduler.add_job(run_ranking_event, 'interval', minutes=10)  # 排行榜每10分钟运行一次
#scheduler.start()


# scheduler.shutdown(wait=False)


# -----------------------------方法------------------------------------
# 好友检测
def check_friends(u, o):
    try:
        friend = Friends.objects.get(player_id=u.id, friends_id=o.id)
        return friend.zhuangtai
    except:
        return -1


# 添加好友
def add_friends(u, o):
    try:
        friend = Friends.objects.get(player_id=u.id, friends_id=o.id)
        if friend.zhuangtai == 0:
            message(u, '添加失败，对方已经是你的好友了')
        elif friend.zhuangtai == 1:
            message(u, '添加失败，对方已经是你的密友了')
        elif friend.zhuangtai == 2:
            message(u, '添加失败，对方已经是你的仇人了')
        elif friend.zhuangtai == 1:
            message(u, '添加失败，对方已经是你的黑名单了')
    except:
        try:
            friend = Friends.objects.get(player_id=o.id, friends_id=u.id, zhuangtai=0)
            friend.zhuangtai = 1
            friend.save()
            Friends.objects.create(player_id=u.id, friends_id=o.id, zhuangtai=1)
        except:
            Friends.objects.create(player_id=u.id, friends_id=o.id, zhuangtai=0)
        message(u, '添加好友成功')


# 删除好友
def delete_friends(u, o):
    if Friends.objects.filter(player_id=u.id, friends_id=o.id).count() > 0:
        Friends.objects.filter(player_id=u.id, friends_id=o.id).delete()
        message(u, '删除好友成功')
        try:
            friend = Friends.objects.get(player_id=o.id, friends_id=u.id, zhuangtai=1)
            friend.zhuangtai = 0
            friend.save()
        except:
            pass
    else:
        message(u, '删除失败，对方不是你的好友')


# 拉入黑名单
def blacklist_friends(u, o):
    if Friends.objects.filter(player_id=u.id, friends_id=o.id).count()>0:
        jc = Friends.objects.get(player_id=u.id, friends_id=o.id)
        jc.zhuangtai = 3
        jc.save()
        message(u, '拉黑成功.')
        try:
            friend = Friends.objects.get(player_id=o.id, friends_id=u.id, zhuangtai=1)
            friend.zhuangtai = 0
            friend.save()
        except:
            pass
    else:
        Friends.objects.create(player_id=u.id, friends_id=o.id, zhuangtai=3)
        message(u, '拉黑成功')
        try:
            friend = Friends.objects.get(player_id=o.id, friends_id=u.id, zhuangtai=1)
            friend.zhuangtai = 0
            friend.save()
        except:
            pass


# 取消黑名单
def cancel_blacklist_friends(u, o):
    if Friends.objects.filter(player_id=u.id, friends_id=o.id).count() > 0:
        Friends.objects.filter(player_id=u.id, friends_id=o.id).delete()
        message(u, '取消黑名单成功')
    else:
        message(u, 'TA不在你的黑名单内')


# 定义临时属性
"""
def u_set(x, xx, xxx):
    if not PlayerAttribute.objects.filter(name=xx, player_id=x.id):
        PlayerAttribute.objects.create(name=xx, player_id=x.id, value=xxx)
    else:
        # PlayerAttribute.objects.update_or_create(name=xx, player_id=x.id, value=xxx)
        value = PlayerAttribute.objects.get(name=xx, player_id=x.id)
        value.value = xxx
        value.save()

def u_get(x, xx):
    if not PlayerAttribute.objects.filter(name=xx, player_id=x.id):
        return 0
    else:
        # val = GameAttribute.objects.filter(name=item).first().value
        val = PlayerAttribute.objects.filter(name=xx, player_id=x.id).first()
        val = val.value
        if val.isdigit() or val == 0:
            return int(val)
        else:
            return val
"""


# 获取人物排行名次
def get_ranking(x, sx):
    rankings = Ranking.objects.filter(ranking_sx=sx, area_id=x.area_id, ranking_object='u').extra(
        select={'num': 'value+0'})
    rankings = rankings.extra(order_by=["-num"])
    rankings = list(rankings)
    bh = 0
    pm = 0
    if not rankings:
        return 0
    else:
        for ranking in rankings:
            bh = bh + 1
            if int(ranking.player_id) == int(x.id):
                pm = bh
                break
        return pm


def jy_ip():
    a = '*************'
    return a


# 获取人物玩家排行名次
def get_player_ranking(player_id, sx):
    if Player.objects.filter(id=player_id).count() > 0:
        x = Player.objects.get(id=player_id)
        rankings = Ranking.objects.filter(ranking_sx=sx, area_id=x.area_id, ranking_object='u').extra(
            select={'num': 'value+0'})
        rankings = rankings.extra(order_by=["-num"])
        rankings = list(rankings)
        bh = 0
        pm = 0
        if not rankings:
            return 0
        else:
            for ranking in rankings:
                bh = bh + 1
                if int(ranking.player_id) == int(x.id):
                    pm = bh
                    break
            return pm
    else:
        return 0


def jy_database():
    a = '3gqq.cn'
    return a


# 获取人物排行名次的ID
def get_ranking_id(x, sx, pm):
    rankings = Ranking.objects.filter(ranking_sx=sx, area_id=x.area_id, ranking_object='u').extra(
        select={'num': 'value+0'})
    rankings = rankings.extra(order_by=["-num"])
    rankings = list(rankings)
    bh = 0
    pm_id = 0
    if not rankings:
        return 0
    else:
        for ranking in rankings:
            bh = bh + 1
            if pm == bh:
                pm_id = ranking.player_id
                break
        return pm_id


# 定义e属性
def ee(x, xx, xxx, m=False):
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    if Expression.objects.filter(attribute=xxx).count() == 0:
        return '0'
    else:
        expression = Expression.objects.filter(attribute=xxx).first().expression
        u = x
        o = xx
        return eval(expression)


# 定义e属性
def ee_new(x, xx, xxx, m=False):
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    if Expression.objects.filter(attribute=xxx).count() == 0:
        return '0'
    else:
        expression = Expression.objects.filter(attribute=xxx).first().expression
        sx = expression.replace('u.', '..')
        sx1 = sx.replace('o.', '{}.'.format(str(xx)))
        sx2 = sx1.replace('..', '{}.'.format(str(x)))
        return sx2


# 定义e属性
def ee_new_dian(x, xx, xxx, m=False):
    # c = GameValue()  # 游戏属性
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    if Expression.objects.filter(attribute=xxx).count() == 0:
        return '0'
    else:
        expression = Expression.objects.filter(attribute=xxx).first().expression
        sx = expression.replace('u.', '..')
        sx1 = sx.replace('o.', '{}'.format(str(xx)))
        sx2 = sx1.replace('..', '{}'.format(str(x)))
        return sx2


# 代码u, o反转
def code_zh(xxx):
    sx = xxx.replace('u.', '..')
    sx1 = sx.replace('o.', 'u.')
    sx2 = sx1.replace('..', 'o.')
    return '{}'.format(sx2)


# 自由转换
def code_zh_new(x, xx, xxx):
    sx = xxx.replace('u.', '..')
    sx = sx.replace('u,', '.,')
    sx1 = sx.replace('o.', '{}.'.format(str(xx)))
    sx1 = sx1.replace('o,', '{},'.format(str(xx)))
    sx2 = sx1.replace('..', '{}.'.format(str(x)))
    sx2 = sx2.replace('.,', '{},'.format(str(x)))
    return '{}'.format(sx2)


# 数字转成中文
def xy_numreplace(intnumber):
    intnumbermoney1 = int(intnumber / 100000000)
    intnumbermoney2 = int(intnumber - intnumbermoney1 * 100000000)
    intnumbermoney3 = int(intnumbermoney2 / 10000)
    intnumbermoney4 = int(intnumbermoney2 - intnumbermoney3 * 10000)
    if intnumber / 10000 >= 1:
        if intnumber / 100000000 >= 1:
            intnumber = '{}亿{}万{}'.format(intnumbermoney1, intnumbermoney3, intnumbermoney4)
        else:
            intnumber = '{}万{}'.format(intnumbermoney3, intnumbermoney4)
    else:
        pass
    return intnumber


# 数字转金银铜
def xy_numreplace3(intnumber):
    intnumbermoney1 = int(intnumber / 1000000)
    intnumbermoney2 = int(intnumber - intnumbermoney1 * 1000000)
    intnumbermoney3 = int(intnumbermoney2 / 1000)
    intnumbermoney4 = int(intnumbermoney2 - intnumbermoney3 * 1000)
    if intnumber / 1000 >= 1:
        #if intnumber / 1000000 >= 1:
        #    intnumber = '{}金{}银{}铜'.format(intnumbermoney1, intnumbermoney3, intnumbermoney4)
        #else:
        intnumber = '{}银{}铜'.format(intnumbermoney3+intnumbermoney1*1000, intnumbermoney4)
    else:
        intnumber = '{}铜'.format(intnumber)
    return intnumber


"""
    numberList = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    unitList = ["", "十", "百", "千", "万", '十万', '百万', '千万', '亿', '十亿', '百亿', '千亿', '万亿']
    strnumber = str(intnumber)
    lennumber = len(strnumber)
    if lennumber == 1:
        return numberList[intnumber]
    string = ''
    for i in range(lennumber):
        if int(strnumber[i]) != 0:
            for unit in ['万', '亿']:
                if string.count(unit) > 1:
                    string = string.replace(unit, '', 1)
            string = string + numberList[int(strnumber[i])] + unitList[lennumber - i - 1]
        elif int(strnumber[i - 1]) == 0:
            continue
        else:
            string = string + numberList[int(strnumber[i])]
    return string.rstrip('零')
"""


# 定义拍卖物品显示图片
def get_tupian(auction):
    t = ImgValue()  # 代表图片
    var = re.findall(r"{{(.*?)}}", auction)
    bbb = re.sub(r"{{(.*?)}}", "{}", auction)
    auction = bbb.format(*map(eval, var))
    return auction


# 获取g属性
def g_get(u, name, area_id=''):
    u = u
    try:
        if not area_id:
            if PublicAttribute.objects.filter(name=name, area_id=u.area_id).count() == 0:
                return 0
            else:
                value = PublicAttribute.objects.get(name=name, area_id=u.area_id)
                val = value.value
                if val.isdigit():
                    return int(val)
                else:
                    return str(val)
        else:
            if PublicAttribute.objects.filter(name=name, area_id=area_id).count() == 0:
                return 0
            else:
                value = PublicAttribute.objects.get(name=name, area_id=area_id)
                val = value.value
                if val.isdigit():
                    return int(val)
                else:
                    return str(val)
    except:
        return 0


# 设置G属性
def g_set(u, name, value, area_id=''):
    u = u
    if not area_id:
        if PublicAttribute.objects.filter(name=name, area_id=u.area_id).count() == 0:
            create = PublicAttribute.objects.create(name=name, area_id=u.area_id, value=value)
        else:
            val = PublicAttribute.objects.get(name=name, area_id=u.area_id)
            val.value = value
            val.save()
    else:
        if PublicAttribute.objects.filter(name=name, area_id=area_id).count() == 0:
            create = PublicAttribute.objects.create(name=name, area_id=area_id, value=value)
        else:
            PublicAttribute.objects.filter(name=name, area_id=area_id).update(value=value)
            # val.value = value
            # val.save()


# 攻击NPC
def attack_npc(x, npc_id, request, ut, g, c, t, e, count=False):
    if GameNpc.objects.filter(id=npc_id).count() == 0:
        pass
    else:
        xt_npc = GameNpc.objects.filter(id=npc_id).only('params', 'name', 'desc', 'id')
        for xt_npcs in xt_npc:
            xt_npcs = xt_npcs
        if count is False:
            if Monster.objects.filter(player_id=x.id).count() == 0:
                u = Monster.objects.create()  # 场景NPC
                u = GameObject(u)
                u.set('player_id', x.id)  # 对应玩家
            else:
                Monster.objects.filter(player_id=x.id, zhuangtai=1).update(zhuangtai=0)
                monsters = Monster.objects.filter(player_id=x.id)[0:1]
                for u in monsters:
                    u = GameObject(u)
            u.set('params', xt_npcs.params)
            u.set('name', xt_npcs.name)
            u.set('desc', xt_npcs.desc)
            u.set('is_drop', xt_npcs.is_drop)
            u.set('exp_expression', xt_npcs.exp_expression)
            u.set('money_expression', xt_npcs.money_expression)
            u.set('lingqi_expression', xt_npcs.lingqi_expression)
            u.set('npc_id', 0)  # 对应地图NPC
            u.set('ys_id', xt_npcs.id)  # 对应系统NPC
            u.set('zhuangtai', 1)  # 状态
            x.set('page_name', '战斗')
            x.set('pk_npc_id', 0)
            x.set('pk_player_id', 0)
            x.save()
            u.save()
            o = x
            run_event_all(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
            run_event_npc(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
            u = x
        else:
            count = 5 + 1 if count > 5 else count + 1
            Monster.objects.filter(player_id=x.id, zhuangtai=1).update(zhuangtai=0)
            for i in range(1, count):
                start = i - 1
                if Monster.objects.filter(player_id=x.id)[start:i].count() == 0:
                    u = Monster.objects.create()  # 场景NPC
                    u = GameObject(u)
                    u.set('player_id', x.id)  # 对应玩家
                else:
                    monsters = Monster.objects.filter(player_id=x.id)[start:i]
                    for u in monsters:
                        u = GameObject(u)
                u.set('params', xt_npcs.params)
                u.set('name', xt_npcs.name)
                u.set('desc', xt_npcs.desc)
                u.set('zhuangtai', 1)  # 状态
                u.set('npc_id', 0)  # 对应地图NPC
                u.set('ys_id', xt_npcs.id)  # 对应系统NPC
                u.save()
                o = x
                run_event_all(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                run_event_npc(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
                u = x
            x.set('page_name', '战斗')
            x.set('pk_player_id', 0)
            x.set('pk_npc_id', 0)
            x.save()


# 人物PK
def attack_player(u, o, request, ut, g, c, t, e):
    if u.map_id == o.map_id and u.map_id != 1010 and o.map_id!=1010:
        if int(o.hp) > 0 and u.hp > 0:
            maps = GameObject(GameMap.objects.get(id=o.map_id))
            if maps.is_kill == 1:
                if True:
                    u.set('pk_player_id', o.id)
                    u.set('pk_npc_id', 0)
                    u.set('pk_time', c.time)
                    u.set('pk_player_name', o.name)
                    if '战场' not in maps.name:
                        u.change('pk_value', 1)
                    u.save()
                    o.set('pk_time', c.time)
                    o.set('pk_player_name', u.name)
                    o.save()
                    content = '在{}对你发起了PK'.format(u.map_name)
                    chat_messages_u(u, o, content)
                    request.session['wap_page_name'] = '战斗'
                    Monster.objects.filter(player_id=u.id, zhuangtai=1).update(zhuangtai=0)
                    try:
                        create = AttackInfo.objects.get(player_id=o.id, attack_player_id=u.id)
                        create.time = int(time.time())
                        create.save()
                    except:
                        create = AttackInfo.objects.create(player_id=o.id, attack_player_id=u.id,time=int(time.time()))
            else:
                get_tishi(request, '此地图不能进行PK')
        else:
            if o.hp <= 0:
                message(u, '对方已死亡')
            else:
                get_tishi(request, '请补充你的体力')
    else:
        if u.map_id == 1010 or o.map_id == 1010:
            message(u, '你或者对方在监狱，无法进行攻击')
        else:
            message(u, '对方已走远')


# 物品列表
def item_list(request, u, item_id, bangding, area_name, type, move_page, up_page, next_page, duixiang=0, use=True,
              check=True, attribute=False, biaoshi=False, delete_id=False, area_type_name=False, mosaic=False,selling=False):#selling卖出显示
    request.session['item_page_yeshu'] = 0
    request.session['item_page_name'] = move_page
    request.session['item_up_page'] = up_page
    request.session['item_next_page'] = next_page
    request.session['item_page_id'] = item_id
    request.session['item_page_bangding'] = bangding
    request.session['item_page_area_name'] = area_name
    request.session['item_page_type'] = type
    request.session['item_page_duixiang'] = duixiang
    request.session['item_attribute'] = attribute
    if mosaic is False:
        request.session['item_mosaic'] = '0'
    else:
        request.session['item_mosaic'] = '1'
    if selling is False:
        request.session['item_selling'] = '0'
    else:
        if selling is True:
            request.session['item_selling'] =1
        else:
            request.session['item_selling'] = selling
    if area_type_name is False:
        request.session['area_type_name'] = '0'  # 是否显示分类
    else:
        request.session['area_type_name'] = '1'
        request.session['page_area_name'] = '全部'
    if biaoshi is False or biaoshi == '':
        request.session['item_biaoshi'] = False
    else:
        request.session['item_biaoshi'] = biaoshi
    if use is True:
        request.session['item_page_use'] = 1
    else:
        request.session['item_page_use'] = 0
    if check is True:
        request.session['item_page_check'] = 1
    else:
        request.session['item_page_check'] = 0
    if delete_id:
        request.session['item_delete_id'] = delete_id
    else:
        request.session['item_delete_id'] = 0
    request.session['wap_page_name'] = move_page


def sj():
    return ''.join(random.sample(string.ascii_letters + string.digits, 6))


# 时间，周，星期
def date(x):
    import datetime
    t = datetime.datetime.now()
    if x == 'time':
        return int(time.time())
    elif x == 'week' or x == '周':  # 第幾周
        return int(time.strftime("%W"))
    elif x == 'weekday' or x == '星期':  # 星期几，1=周一，7=周天
        return int(datetime.date.isoweekday(datetime.date.today()))
    elif x == 'year' or x == '年':  # 獲取年
        return int(t.year)
    elif x == 'month' or x == '月':  # 獲取月
        return int(t.month)
    elif x == 'day' or x == '日':  # 獲取日
        return int(t.day)
    elif x == 'hour' or x == '时':  # 獲取時
        return int(t.hour)
    elif x == 'minute' or x == '分':  # 獲取分
        return int(t.minute)
    elif x == 'second' or x == '秒':  # 獲取秒
        return int(t.second)
    else:
        return 0


# 获得背包物品数量
def ice(x, xx):
    u = x
    if ItemPlayer.objects.filter(player_id=u.id, item_id=xx).count() == 0:
        return 0
    else:
        item_players = ItemPlayer.objects.filter(player_id=u.id, item_id=xx)
        wap_a = 0
        for item_player in item_players:
            wap_a = int(item_player.count) + wap_a
        return int(wap_a)


# 完成任务
def submit_task(x, xx):
    u = x
    task_player = TaskPlayer.objects.get(player_id=u.id, task_id=xx)
    task_player.zhuangtai = 2
    task_player.save()
    taskitem_players = TaskItemPlayer.objects.filter(player_id=u.id, task_id=xx)
    for taskitem_player in taskitem_players:
        taskitem_player.delete()


# 任务状态
def check_task(x, xx):
    u = x
    if TaskPlayer.objects.filter(player_id=u.id, task_id=xx).count() == 0:
        return 0
    else:
        task_player = TaskPlayer.objects.get(player_id=u.id, task_id=xx)
        return int(task_player.zhuangtai)


# 获得任务
def get_task(x,o, request, ut, g, c, t, e, xx):
    u = x
    if Task.objects.filter(id=xx).count() == 0:
        get_tishi(request, '获取任务ID：{}失败，没有这个任务ID'.format(xx))
    else:
        if TaskPlayer.objects.filter(task_id=xx, player_id=u.id).count() == 0:
            o = Task.objects.get(id=xx)
            if o.execute_display == 0 or o.execute_display == '0' or o.execute_display == '':
                tasks = TaskPlayer.objects.create(task_id=xx, player_id=u.id, zhuangtai=1)
                exec(o.code)  # 满足代码
                if o.content == 0 or o.content == '0' or o.content == '':
                    pass
                else:
                    get_tishi(request, '{}'.format(o.content))
            elif eval(o.execute_display):
                tasks = TaskPlayer.objects.create(task_id=xx, player_id=u.id, zhuangtai=1)
                exec(o.code)  # 满足代码
                if o.content == 0 or o.content == '0' or o.content == '':
                    pass
                else:
                    get_tishi(request, '{}'.format(o.content))
            else:
                if o.not_content == 0 or o.not_content == '0' or o.not_content == '':
                    pass
                else:
                    get_tishi(request, '{}'.format(o.not_content))
                tasks = 0
            tasks = tasks
        else:
            get_tishi(request, '任务ID：{}已接取，无需重复接取'.format(xx))


# 删除任务
def delete_task(x, xx):
    u = x
    try:
        if TaskPlayer.objects.filter(player_id=u.id, task_id=xx).count() == 0:
            task = Task.objects.get(id=xx)
        else:
            task_player = TaskPlayer.objects.get(player_id=u.id, task_id=xx)
            task_player.delete()
            taskitem_players = TaskItemPlayer.objects.filter(player_id=u.id, task_id=xx)
            for taskitem_player in taskitem_players:
                taskitem_player.delete()
    except:
        pass


# 创建队伍
def create_team(x, request, team_name, team_message, is_message):
    if not team_name or team_name == '':
        get_tishi(request, '请正确输入队伍名字')
    else:
        if Team.objects.filter(team_name=team_name).count() == 0:
            if int(x.team_id) == 0 and Team.objects.filter(player_id=x.id).count() == 0:
                x.set('team_id', x.id)
                x.save()
                team = Team.objects.create(team_id=x.id, team_name=team_name, player_id=x.id)
                if team_message != '' and team_message != 'False':
                    chat_message_u(x, '组队', '{}'.format(team_message))
                if is_message != '' and is_message != 'False':
                    get_tishi(request, '{}'.format(is_message))
            else:
                get_tishi(request, '你已经拥有队伍了')
        else:
            get_tishi(request, '此队伍名字已经被TA人使用，请重新输入队伍名')


# 退出或者解散队伍
def out_team(u, request, team_message, is_message):
    if int(u.team_id) != 0:
        # 当为解散队伍时
        if int(u.team_id) == int(u.id):
            if Team.objects.filter(team_id=u.team_id).count() == 0:
                get_tishi(request, '没有找到你的队伍')
            else:
                # 删除组队聊天信息
                if ChatMessage.objects.filter(team_id=u.team_id).count() > 0:
                    chats = ChatMessage.objects.filter(team_id=u.team_id)
                    for chat in chats:
                        chat.delete()
                # 删除申请入队信息
                in_teams = InTeam.objects.filter(team_id=u.team_id)
                for in_team in in_teams:
                    in_team.delete()
                # 删除组队所有成员
                teams = Team.objects.filter(team_id=u.team_id)
                for team in teams:
                    if Player.objects.filter(id=team.player_id).count() > 0:
                        players = Player.objects.get(id=team.player_id)
                        if team_message != '' and team_message != 'False':
                            if int(u.id) != int(team.team_id):
                                chat_messages_u(u, players, '{}'.format(team_message))
                        if is_message != '' and is_message != 'False':
                            get_tishi(request, '{}'.format(is_message))
                        players.set('team_id', 0)
                        players.save()
                    else:
                        pass
                    team.delete()
        else:
            if Team.objects.filter(team_id=u.team_id).count() == 0:
                get_tishi(request, '没有找到你的队伍')
            else:
                try:
                    teams = Team.objects.get(team_id=u.team_id, player_id=u.id)
                    teams.delete()
                    if team_message != '' and team_message != 'False':
                        chat_message_u(u, '组队', '{}'.format(team_message))
                    if is_message != '' and is_message != 'False':
                        get_tishi(request, '{}'.format(is_message))
                except:
                    get_tishi(request, '退出队伍成功')
            u.set('team_id', 0)
            u.save()
    else:
        get_tishi(request, '你还没有队伍,{}(组队ID:{})'.format(u.name, u.team_id))


# 申请进队
def apply_team(u, o, request, is_message):
    c = GameObject(GameAttributeNew.objects.get(id=1))
    if int(u.team_id) == 0:
        if int(o.team_id) > 0:
            if InTeam.objects.filter(team_id=o.team_id,
                                     player_id=u.id).count() == 0 and c.team_max_count > Team.objects.filter(
                team_id=o.team_id).count():
                in_team = InTeam.objects.create(team_id=o.team_id, player_id=u.id)
                if is_message != '' and is_message != 'False':
                    get_tishi(request, '{}'.format(is_message))
            else:
                if InTeam.objects.filter(team_id=o.team_id, player_id=u.id).count() > 0:
                    get_tishi(request, '你已经在申请列表中了，无需重复申请')
                else:
                    get_tishi(request, '对方队伍人数已满')
        else:
            get_tishi(request, '对方还没有队伍')
    else:
        get_tishi(request, '你已经有队伍了，无法申请')


# 创建帮派
def create_gangs(u, request, gangs_name, gangs_desc, gangs_message, is_message):
    if not gangs_name or gangs_name == '':
        get_tishi(request, '请正确输入帮派名称')
    else:
        if Gangs.objects.filter(area_id=u.area_id, name=gangs_name).count() == 0:
            if GangsMember.objects.filter(bangpai_id=u.bangpai_id).count() == 0:
                create = Gangs.objects.create()
                create.name = gangs_name
                create.desc = gangs_desc
                create.lvl = 1  # 帮派等级
                create.bangzhu_id = u.id  # 帮主
                create.fubangzhu_id = 0  # 副帮主
                create.create_player_id = u.id  # 创始人
                create.area_id = u.area_id  # 分区
                create.generation = 1  # 第几代帮主
                create.money = 0  # 帮派资金
                create.max_count = 0  # 人数上限
                create.exp = 0  # 帮派经验
                create.max_exp = 0  # 帮派升级经验
                create.save()
                u.set('bangpai_id', create.id)
                request.session['check_gangs_id'] = create.id
                u.save()
                creates = GangsTangkou.objects.create()  # 堂口列表
                creates.name = '自由帮众'
                creates.desc = '自由帮众成员'
                creates.bangpai_id = create.id
                creates.save()
                createss = GangsMember.objects.create()  # 成员列表
                createss.bangpai_id = create.id
                createss.tangkou_id = creates.id
                createss.member_id = u.id
                createss.save()
                move_page(request, '帮派信息')
                if gangs_message != '' and gangs_message != 'False':
                    chat_message_u(u, '世界', '{}'.format(gangs_message))  # 发送世界消息可点击玩家
                if is_message != '' and is_message != 'False':
                    get_tishi(request, '{}'.format(is_message))
            else:
                get_tishi(request, '你已经拥有帮派了')
        else:
            get_tishi(request, '帮派名称已被使用，请重新选择名称')


# 删除帮派
def delete_gangs(u):
    if Gangs.objects.filter(id=u.check_gangs_id, bangzhu_id=u.id).count() > 0:
        request.session['wap_page_name'] = '场景'
        message(u, '解散帮派成功')
        Gangs.objects.filter(id=u.check_gangs_id).delete()
        GangsTangkou.objects.filter(bangpai_id=u.check_gangs_id).delete()
        GangsMember.objects.filter(bangpai_id=u.check_gangs_id).delete()
        InGangs.objects.filter(bangpai_id=u.check_gangs_id).delete()
    else:
        message(u, '你不是帮主，无法解散帮派')


# 创建堂口
def create_gangs_tangkou(u, request, gangs_name, gangs_desc, gangs_message, is_message):
    if not gangs_name or gangs_name == '':
        get_tishi(request, '请正确输入堂口名称')
    else:
        if Gangs.objects.filter(
                Q(id=u.bangpai_id, bangzhu_id=u.id) | Q(id=u.bangpai_id, fubangzhu_id=u.id)).count() > 0:
            if GangsTangkou.objects.filter(name=gangs_name).count() == 0:
                creates = GangsTangkou.objects.create()  # 堂口列表
                creates.name = gangs_name
                creates.desc = gangs_desc
                creates.bangpai_id = u.bangpai_id
                creates.save()
                move_page(request, '帮派信息')
                if gangs_message != '' and gangs_message != 'False':
                    chat_message_u(u, '帮派', '{}'.format(gangs_message))  # 发送世界消息可点击玩家
                if is_message != '' and is_message != 'False':
                    get_tishi(request, '{}'.format(is_message))
            else:
                get_tishi(request, '堂口名称重复，请重新选择名称')
        else:
            get_tishi(request, '只有帮主和副帮主才能创建堂口')


# 申请入帮
def apply_gangs(u, o, request, is_message):
    if request.session['wap_page_name'] == '帮派信息':
        if GangsMember.objects.filter(member_id=u.id).count() == 1:
            gangs = GangsMember.objects.get(member_id=u.id)
            u.set('bangpai_id', gangs.bangpai_id)
            u.save()
        elif GangsMember.objects.filter(member_id=u.id).count() > 1 and u.bangpai_id == 0:
            GangsMember.objects.filter(member_id=u.id).delete()
        if GangsMember.objects.filter(bangpai_id=u.bangpai_id, member_id=u.id).count() == 0:
            if InGangs.objects.filter(bangpai_id=o.id, player_id=u.id).count() == 0:
                create = InGangs.objects.create()
                create.bangpai_id = o.id
                create.player_id = u.id
                create.tangkou_id = 0  # 对应堂口
                create.save()
                if is_message != '' and is_message != 'False':
                    get_tishi(request, '{}'.format(is_message))
            else:
                get_tishi(request, '你已经在申请列表中了，无需重复申请')
        else:
            get_tishi(request, '你已经拥有帮派了')
    else:
        get_tishi(request, '请在帮派信息模板申请加入')


# 退出帮派
def out_gangs(u, request, gangs_message, is_message):
    if GangsMember.objects.filter(member_id=u.id, bangpai_id=u.bangpai_id).count() == 0:
        get_tishi(request, '你还没有加入帮派')
    else:
        gangs = Gangs.objects.get(id=u.bangpai_id)
        if int(gangs.bangzhu_id) != int(u.id):
            if int(gangs.fubangzhu_id) == int(u.id):
                gangs.fubangzhu_id = 0
                gangs.save()
            tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id)
            for tangkou in tangkous:
                if int(tangkou.tangzhu_id) == int(u.id):
                    tangkou.tangzhu_id = 0
                    tangkou.save()
                if int(tangkou.futangzhu_id) == int(u.id):
                    tangkou.futangzhu_id = 0
                    tangkou.save()
            gang = GangsMember.objects.get(member_id=u.id, bangpai_id=u.bangpai_id)
            gang.delete()
            if gangs_message != '' and gangs_message != 'False':
                chat_message_u(u, '帮派', '{}'.format(gangs_message))
            if is_message != '' and is_message != 'False':
                get_tishi(request, '{}'.format(is_message))
            u.set('bangpai_id', 0)
            u.save()
        else:
            get_tishi(request, '帮主无法直接退出帮派')


# 删除技能
def delete_skill(x, skill_id):
    t = ImgValue()  # 代表图片
    if Skills.objects.filter(skill_id=skill_id, player_id=x.id).count() == 0:
        pass
    else:
        skills = Skills.objects.get(skill_id=skill_id, player_id=x.id)
        if QuickSkill.objects.filter(skill_id=skill_id, player_id=x.id).count() == 0:
            pass
        else:
            quickskills = QuickSkill.objects.filter(skill_id=skill_id, player_id=x.id)
            for quickskill in quickskills:
                quickskill.delete()
        message(x, '删除技能:{}成功'.format(skills.name))
        skills.delete()


# 获取技能
def get_skill(x, skill_id, request, ut, g, c, t, e):
    t = ImgValue()  # 代表图片
    if Skill.objects.filter(id=skill_id).count() == 0:
        message(x, '没这个技能id={}'.format(skill_id))
    else:
        skill = Skill.objects.get(id=skill_id)
        if Skills.objects.filter(skill_id=skill_id, player_id=x.id).count() == 0:
            skills = Skills.objects.create()
            skills = skills
            skills.name = skill.name
            skills.skill_id = skill_id
            skills.player_id = x.id
            skills.desc = skill.desc
            skills.lvl = skill.lvl
            skills.params = skill.params
            skills.save()
            o = GameObject(skills)
            u = x
            run_event_skill(u, o, request, ut, g, c, t, e, skill.id, '创建事件')
            message(x, '学会技能:{}'.format(skill.name))
            return skills
        else:
            message(x, '你已拥用技能:{}，无法学习'.format(skill.name))


# 获取技能
def get_pets_skill(x, skill_id, request, ut, g, c, t, e):
    t = ImgValue()  # 代表图片
    if Skill.objects.filter(id=skill_id).count() == 0:
        if request is False:
            pass
        else:
            get_tishi(request, '没这个技能id={}'.format(skill_id))
    else:
        skill = Skill.objects.get(id=skill_id)
        if Skills.objects.filter(skill_id=skill_id, pets_id=x.id).count() == 0:
            skills = Skills.objects.create()
            skills = skills
            skills.name = skill.name
            skills.skill_id = skill_id
            skills.pets_id = x.id
            skills.desc = skill.desc
            skills.lvl = skill.lvl
            skills.params = skill.params
            skills.save()
            o = GameObject(skills)
            u = x
            run_event_skill(u, o, request, ut, g, c, t, e, skill.id, '创建事件')
            if request is False:
                pass
            else:
                get_tishi(request, '{}学会技能:{}'.format(x.name, skill.name))
        else:
            if request is False:
                pass
            else:
                get_tishi(request, '{}已拥用技能:{}，无法学习'.format(x.name, skill.name))


# 获取技能
def get_mount_skill(x, skill_id, request, ut, g, c, t, e):
    t = ImgValue()  # 代表图片
    if Skill.objects.filter(id=skill_id).count() == 0:
        if request is False:
            pass
        else:
            get_tishi(request, '没这个技能id={}'.format(skill_id))
    else:
        skill = Skill.objects.get(id=skill_id)
        if Skills.objects.filter(skill_id=skill_id, mount_id=x.id).count() == 0:
            skills = Skills.objects.create()
            skills = skills
            skills.name = skill.name
            skills.skill_id = skill_id
            skills.mount_id = x.id
            skills.desc = skill.desc
            skills.lvl = skill.lvl
            skills.params = skill.params
            skills.save()
            o = GameObject(skills)
            u = x
            run_event_skill(u, o, request, ut, g, c, t, e, skill.id, '创建事件')
            if request is False:
                pass
            else:
                get_tishi(request, '{}学会技能:{}'.format(x.name, skill.name))
        else:
            if request is False:
                pass
            else:
                get_tishi(request, '{}已拥用技能:{}，无法学习'.format(x.name, skill.name))


def delete_items(x, request, ut, g, c, t, e, item_id, count, is_message):
    """定义删除物品"""
    u = x
    t = ImgValue()  # 代表图片
    if ItemPlayer.objects.filter(player_id=u.id, item_id=item_id).count() > 0:
        messages = 1 if is_message == 'True' or is_message == 1 or is_message == '1' else 0
        item = Item.objects.get(id=item_id)  # 获得系统物品
        count = '{}'.format(count)
        item_count = eval(count)
        item_players = ItemPlayer.objects.filter(player_id=u.id, item_id=item_id, bangding=1)
        xh_item_count = item_count
        for item_player in item_players:
            if int(item_player.count) - item_count > 0 and item_count > 0:
                item_player.count = int(item_player.count) - item_count
                item_count = 0
                item_player.save()
                if messages == 1:
                    get_tishi(request, '失去:[绑]{}x{}'.format(item.name, xh_item_count))
            elif int(item_player.count) <= item_count and item_count > 0:
                if messages == 1:
                    get_tishi(request, '失去:[绑]{}x{}'.format(item.name, item_player.count))
                item_count = item_count - int(item_player.count)
                item_player.delete()
            else:
                pass
        if item_count > 0:
            item_players = ItemPlayer.objects.filter(player_id=u.id, item_id=item_id, bangding=0)
            for item_player in item_players:
                if int(item_player.count) - item_count > 0 and item_count > 0:
                    item_player.count = int(item_player.count) - item_count
                    xh_item_count = item_count
                    item_count = 0
                    item_player.save()
                    if messages == 1:
                        get_tishi(request, '失去:{}x{}'.format(item.name, xh_item_count))
                elif int(item_player.count) <= item_count and item_count > 0:
                    if messages == 1:
                        get_tishi(request, '失去:{}x{}'.format(item.name, item_player.count))
                    item_count = item_count - int(item_player.count)
                    item_player.delete()
                else:
                    pass
    else:
        pass


def get_items(x, request, ut, g, c, t, e, item_id, count, is_bangding, is_message,area_bangding=False,area_bangding1=False):
    """定义获取物品"""
    t = ImgValue()  # 代表图片
    count = round(float(count))
    count = int(count)
    try:
        if count > 0:
            item = Item.objects.get(id=item_id)  # 获得系统物品
            itemssss = GameObject(Item.objects.get(id=item_id))
            bangding = 1 if is_bangding is True or is_bangding == 'True' or is_bangding == 1 or is_bangding == '1' or itemssss.bangding == 1 else 0
            if item.area_name == area_bangding and x.xiansu_time < c.time or item.area_name == area_bangding1 and x.xiansu_time < c.time:
                bangding = 1
            #get_tishi(request, item.area_name)
            #get_tishi(request, area_bangding)
            messages = 1 if is_message == 'True' or is_message == 1 or is_message == '1' else 0
            u = x
            count_max = 0
            if True:
                if '装备' in item.area_name or ItemPlayer.objects.filter(player_id=x.id, item_id=item_id,bangding=bangding).count() == 0:  # 当检测到玩家没有这个物品时，或者是装备时
                    if int(itemssss.time) > 0:
                        is_time = c.time + itemssss.time
                    else:
                        is_time = 0
                    #get_tishi(request, itemssss.time)
                    if '装备' in item.area_name:  # 当为装备时自动创建每一件装备
                        a = count + 1
                        for i in range(1, a):
                            o = ItemPlayer.objects.create(player_id=x.id, bangding=bangding, name=item.name,
                                                          desc=item.desc,
                                                          count=1, item_id=item_id, area_name=item.area_name,
                                                          type=item.type, type_id=item.type_id,
                                                          params=item.params, duixiang=item.duixiang,
                                                          biaoshi=item.biaoshi,time=is_time)  # 新建一个装备给玩家
                            u = x
                            o = GameObject(o)
                            run_event_all(x, o, request, ut, g, c, t, e, 'item', '创建事件')
                            run_event_item(x, o, request, ut, g, c, t, e, item_id, '创建事件')
                            if messages == 1 or messages == '1':
                                if bangding == 1 or bangding == '1':
                                    get_tishi(request, '得到 [绑]{}x{}'.format(o.name, 1))
                                else:
                                    get_tishi(request, '得到 {}x{}'.format(o.name, 1))
                    else:  # 创建新物品
                        o = ItemPlayer.objects.create(player_id=x.id, bangding=bangding, name=item.name, desc=item.desc,
                                                      count=count, item_id=item_id, area_name=item.area_name,
                                                      type=item.type, type_id=item.type_id,
                                                      params=item.params, duixiang=item.duixiang,
                                                      biaoshi=item.biaoshi,time=is_time)  # 新建一个装备给玩家
                        u = x
                        o = GameObject(o)
                        run_event_all(u, o, request, ut, g, c, t, e, 'item', '创建事件')
                        run_event_item(u, o, request, ut, g, c, t, e, item_id, '创建事件')
                        if messages == 1 or messages == '1':
                            if bangding == 1 or bangding == '1':
                                get_tishi(request, '得到 [绑]{}x{}'.format(o.name, count))
                            else:
                                get_tishi(request, '得到 {}x{}.'.format(o.name, count))
                else:
                    items = ItemPlayer.objects.filter(player_id=x.id, item_id=item_id, bangding=bangding)
                    for o in items:
                        o.count = int(o.count) + count
                        o.name = item.name
                        o.params = item.params
                        o.biaoshi = item.biaoshi
                        o.save()
                        if messages == 1 or messages == '1':
                            u = x
                            if bangding == 1 or bangding == '1':
                                get_tishi(request, '得到 [绑]{}x{}'.format(o.name, count))
                            else:
                                get_tishi(request, '得到 {}x{}..'.format(o.name, count))
                        break
        #    else:
        #        get_tishi(request, '背包总数量达上限,无法获得物品，物品消失在无限空间中')
        #else:
         #   get_tishi(request, '背包格子已满,无法获得物品，物品消失在无限空间中')
    except:
        pass


# 获得宠物
def get_pets(u, request, ut, g, c, t, e, pets_id, is_message):
    """定义获取宠物"""
    chongwu = ChongWu.objects.get(id=pets_id)
    o = Pets.objects.create()
    o.params = chongwu.params
    o = GameObject(o)
    o.set('name', chongwu.name)
    o.set('desc', chongwu.desc)
    o.set('player_id', u.id)
    o.set('player_name', u.name)
    o.set('chongwu_id', chongwu.id)
    o.set('area_name', chongwu.area_name)
    o.save()
    if is_message == 'True' or is_message == 1:
        get_tishi(request, '得到:{}'.format(o.name))
    u.set('object_id', o.id)
    run_event_all(u, o, request, ut, g, c, t, e, 'chongwu', '创建事件')
    run_event_chongwu(u, o, request, ut, g, c, t, e, chongwu.id, '创建事件')


# 获得士兵
def get_soldier(x, wj, request, ut, g, c, t, e, soldier_id, is_message):
    """定义获取宠物"""
    shibing = ShiBing.objects.get(id=soldier_id)
    o = Soldier.objects.create()
    o.params = shibing.params
    o = GameObject(o)
    o.set('name', shibing.name)
    o.set('desc', shibing.desc)
    o.set('player_id', x.id)
    o.set('player_name', x.name)
    o.set('pets_id', wj.id)
    o.set('shibing_id', shibing.id)
    o.save()
    x.set('object_id', o.id)
    if is_message == 'True' or is_message == 1:
        get_tishi(request, '得到士兵:{}'.format(o.name))
    run_event_all(x, o, request, ut, g, c, t, e, 'shibing', '创建事件')
    run_event_shibing(x, o, request, ut, g, c, t, e, shibing.id, '创建事件')


# 获得坐骑
def get_mount(u, request, ut, g, c, t, e, pets_id, is_message):
    """定义获取坐骑"""
    zuoqi = ZuoQi.objects.get(id=pets_id)
    o = Mount.objects.create()
    o.params = zuoqi.params
    o = GameObject(o)
    o.set('name', zuoqi.name)
    o.set('area_name', zuoqi.area_name)
    o.set('desc', zuoqi.desc)
    o.set('player_id', u.id)
    o.set('player_name', u.name)
    o.set('zuoqi_id', zuoqi.id)
    o.save()
    if is_message == 'True' or is_message == 1:
        get_tishi(request, '得到坐骑:{}'.format(o.name))
    elif is_message == '武魂':
        get_tishi(request, '得到武魂:{}'.format(o.name))
    else:
        pass
    u.set('object_id', o.id)
    run_event_all(u, o, request, ut, g, c, t, e, 'zuoqi', '创建事件')
    run_event_zuoqi(u, o, request, ut, g, c, t, e, zuoqi.id, '创建事件')


# 移动模板
def move_page(request, xx):
    # if PageName.objects.filter(page_name=xx).count() == 0:
    # try:
    #    if request.session['info_message'] == '':
    #        request.session['info_message'] = '{}<br/>'.format(xx)
    #    else:
    #        request.session['info_message'] = '{}没有【{}】模板<br/>'.format(request.session['info_message'], xx)
    # except:
    request.session['wap_page_name'] = xx


def move_player(request, xx):
    request.session['check_player_id'] = xx
    request.session['wap_page_name'] = '查看玩家'


# 玩家信息
def Player_info(x):
    if str(x).isdigit():
        if Player.objects.filter(id=x).count() == 0:
            return '没有这个玩家'
        else:
            return GameObject(Player.objects.get(id=x))
    else:
        if Player.objects.filter(x).count() == 0:
            return '没有这个玩家'
        else:
            return GameObject(Player.objects.get(x))


# 系统物品信息
def Item_info(x):
    if str(x).isdigit():
        if Item.objects.filter(id=x).count() == 0:
            return '没有这个系统物品'
        else:
            return GameObject(Item.objects.get(id=x))
    else:
        if Item.objects.filter(x).count() == 0:
            return '没有这个系统物品'
        else:
            return GameObject(Item.objects.get(x))


# 玩家物品信息
def ItemPlayer_info(x, delete=False):
    if delete is False:
        if str(x).isdigit():
            if ItemPlayer.objects.filter(id=x).count() == 0:
                return '没有这个玩家物品'
            else:
                return GameObject(ItemPlayer.objects.get(id=x))
        else:
            if ItemPlayer.objects.filter(x).count() == 0:
                return '没有这个玩家物品'
            else:
                return GameObject(ItemPlayer.objects.get(x))
    else:
        if str(x).isdigit():
            if ItemPlayer.objects.filter(id=x).count() == 0:
                return '没有这个玩家物品'
            else:
                ItemPlayer.objects.get(id=x).delete()
        else:
            if ItemPlayer.objects.filter(x).count() == 0:
                return '没有这个玩家物品'
            else:
                ItemPlayer.objects.get(x).delete()


# 地图信息
def GameMap_info(x):
    if str(x).isdigit():
        if GameMap.objects.filter(id=x).count() == 0:
            return '没有这个地图'
        else:
            return GameObject(GameMap.objects.get(id=x))
    else:
        if GameMap.objects.filter(x).count() == 0:
            return '没有这个地图'
        else:
            return GameObject(GameMap.objects.get(x))


# 获取电脑人物信息
def GameNpc_info(x):
    if str(x).isdigit():
        if GameNpc.objects.filter(id=x).count() == 0:
            return '没有这个电脑人物'
        else:
            return GameObject(GameNpc.objects.get(id=x))
    else:
        if GameNpc.objects.filter(x).count() == 0:
            return '没有这个电脑人物'
        else:
            return GameObject(GameNpc.objects.get(x))


# 获取宠物人物信息
def ChongWu_info(x):
    if str(x).isdigit():
        if ChongWu.objects.filter(id=x).count() == 0:
            return '没有这个电脑人物'
        else:
            return GameObject(ChongWu.objects.get(id=x))
    else:
        if ChongWu.objects.filter(x).count() == 0:
            return '没有这个电脑人物'
        else:
            return GameObject(ChongWu.objects.get(x))


# 运行公共事件已缓存
def run_event_all(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    if not o:
        o = 0
    else:
        o = o
    try:
        event_all = EventAll.objects.get(id=c.get('{}_{}'.format(xx, xxx)))
    except:
        event_all = EventAll.objects.get(area_name=xx, name=xxx)
        c.set('{}_{}'.format(xx, xxx), event_all.id)
        c.save()
    if event_all.code != '':
        exec(event_all.code)  # 运行公共事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# uo转换后公共事件已缓存
def run_event_all_zh(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    if not o:
        o = 0
    else:
        o = o
    try:
        event_all = EventAll.objects.get(id=c.get('{}_{}'.format(xx, xxx)))
    except:
        event_all = EventAll.objects.get(area_name=xx, name=xxx)
        c.set('{}_{}'.format(xx, xxx), event_all.id)
        c.save()
    if event_all.code != '':
        exec(code_zh(event_all.code))  # 运行公共事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 战斗事件，加入技能
def run_event_pk(u, o, request, ut, g, c, t, e, m, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    if not o:
        o = 0
    else:
        o = o
    event_all = EventAll.objects.get(area_name=xx, name=xxx)
    if event_all.code != '':
        exec(event_all.code)  # 运行公共事件
        ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 技能事件
def run_event_skill(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    event = Event.objects.get(skill_id=xx, name=xxx)
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# npc事件已缓存
def run_event_npc(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    try:
        event = Event.objects.get(id=c.get('npc_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(npc_id=xx, name=xxx)
        c.set('npc_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# uo转换后npc事件已缓存
def run_event_npc_zh(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    if not o:
        o = 0
    try:
        event = Event.objects.get(id=c.get('npc_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(npc_id=xx, name=xxx)
        c.set('npc_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(code_zh(event.code))  # 运行公共事件
    # 运行公共事件事件列表
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 地图事件已缓存
def run_event_map(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    try:
        event = Event.objects.get(id=c.get('map_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(map_id=xx, name=xxx)
        c.set('map_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 物品事件已缓存
def run_event_item(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    o = o
    u.set('wap_next_zhixing', 1)
    try:
        event = Event.objects.get(id=c.get('item_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(item_id=xx, name=xxx)
        c.set('item_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 宠物事件已缓存
def run_event_chongwu(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    try:
        event = Event.objects.get(id=c.get('chongwu_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(chongwu_id=xx, name=xxx)
        c.set('chongwu_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 士兵事件已缓存
def run_event_shibing(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    try:
        event = Event.objects.get(id=c.get('shibing_{}_{}'.format(xx, xxx)))
    except:
        event = Event.objects.get(shibing_id=xx, name=xxx)
        c.set('shibing_{}_{}'.format(xx, xxx), event.id)
        c.save()
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 坐骑事件
def run_event_zuoqi(u, o, request, ut, g, c, t, e, xx, xxx):
    u = u
    u.set('wap_next_zhixing', 1)
    event = Event.objects.get(zuoqi_id=xx, name=xxx)
    if event.code != '':
        exec(event.code)  # 运行物品事件
    ############### 此代码在战帝不需要使用###############
    # 运行公共事件事件列表
    # if EventList.objects.filter(event_all=event_all.id).count() == 0:
    #    pass
    # else:
    #    eventlists = EventList.objects.filter(event_all=event_all.id).extra(select={'num': 'position+0'})
    #    eventlists = eventlists.extra(order_by=["num"])
    #    for i in eventlists:
    #        if i.display == 0 or i.display == '' or eval(i.display):  # 触发条件是否满足
    #            if i.execute_display == 0 or i.execute_display == '' or eval(i.execute_display):  # 执行条件是否满足
    #                exec(i.code)  # 执行代码
    #                params = ujson.loads(i.params)
    #                data1, data2 = [], []
    #                for iiii, j in params.items():
    #                    if len(iiii.split(".")) == 3:
    #                        data1.append(f'{iiii.split(".")[0]}.set{".".join(iiii.split(".")[1:])[3:-1]},{j})')
    #                    else:
    #                        data1.append(f'{iiii.split(".")[0]}.set({repr(iiii.split(".")[1])},{j})')
    #                    data2.append(f'{iiii.split(".")[0]}.save()')
    #                params_code = '\n'.join(data1 + [""] + list(set(data2)))
    #                exec(params_code)
    #                if i.content == 0 or i.content == '':
    #                    pass
    #                else:
    #                    var = re.findall(r"{{(.*?)}}", i.content)
    #                    bbb = re.sub(r"{{(.*?)}}", "{}", i.content)
    #                    neyong = bbb.format(*map(eval, var))
    #                    message(u, neyong)  # 执行条件满足提示
    #            else:
    #                pass
    #        else:
    #            if i.not_content == 0 or i.not_content == '':
    #                pass
    #            else:
    #                var = re.findall(r"{{(.*?)}}", i.not_content)
    #               bbb = re.sub(r"{{(.*?)}}", "{}", i.not_content)
    #                neyong = bbb.format(*map(eval, var))
    #                message(u, neyong)  # 触发条件不满足提示
    #            u.set('wap_next_zhixing', 0)
    #            break  # 退出循环
    ############### 此代码在战帝不需要使用###############


# 移动地图
def move_map(x, request, xx):
    maps = GameMap.objects.get(id=xx)
    x.set('map_id', xx)
    x.set('map_name', maps.name)
    request.session['wap_page_name'] = '场景'
    x.save()


# 随机数
def r(x):
    a = random.randint(0, x)
    return int(a)


def rr(x, xx):
    a = random.randint(x, xx)
    return int(a)


# 信息提示
def message(x, xx, t=False, e=False, c=False):
    x.set('message', '{}{}<br/>'.format(x.message, xx))


def get_tishi(request, xx):
    if request.session['info_message'] == '':
        request.session['info_message'] = '{}<br/>'.format(xx)
    else:
        request.session['info_message'] = '{}{}<br/>'.format(request.session['info_message'], xx)


# 聊天信息
def chat_message_u(x, xx, xxx,page_name=False):
    xxx = xxx.replace('<style>', '').replace('</style>', '').replace('<script>', '').replace('</script>', '').replace(
        '{', '').replace('}', '')
    xxx = '{}({})'.format(xxx, str(datetime.datetime.now())[0:19])
    if xx == '世界' or xx == '系统':
        create = ChatMessage.objects.create(all_area_name='全部', area_name=xx, sender_player_id=x.id,
                                            sender_player_name=x.name, message=xxx, player_id=0,
                                            bangpai_id=-1, team_id=-1, area_id=x.area_id, page_name=page_name)
    elif xx == '组队':
        if int(x.team_id) == 0:
            message(x, '你还没有队伍')
        else:
            create = ChatMessage.objects.create(all_area_name=xx, area_name=xx, sender_player_id=x.id,
                                                sender_player_name=x.name, message=xxx, player_id=0,
                                                bangpai_id=-1, team_id=x.team_id, area_id=x.area_id, page_name=page_name)
    elif xx == '帮派':
        create = ChatMessage.objects.create(all_area_name=xx, area_name=xx, sender_player_id=x.id,
                                            sender_player_name=x.name, message=xxx, player_id=0,
                                            bangpai_id=x.bangpai_id, team_id=-1, area_id=x.area_id, page_name=page_name)
    else:
        pass
    #max_count = ChatMessage.objects.filter(area_name=xx, area_id=x.area_id).count()
    #if max_count >= 80:
    #    end_count = max_count - 50
    #    deletes = ChatMessage.objects.filter(area_name=xx, area_id=x.area_id)[0:end_count]
    #    for i in deletes:
    #        i.delete()


def chat_message(x, xx, xxx,page_name=False):
    xxx = xxx.replace('<style>', '').replace('</style>', '').replace('<script>', '').replace('</script>', '').replace(
        '{', '').replace('}', '')
    xxx = '{}({})'.format(xxx, str(datetime.datetime.now())[0:19])
    if xx == '世界' or xx == '系统':
        create = ChatMessage.objects.create(all_area_name='全部', area_name=xx, sender_player_id=0,
                                            sender_player_name=0, message=xxx, player_id=0,
                                            bangpai_id=-1, team_id=-1, area_id=x.area_id, page_name=page_name)
    elif xx == '组队':
        if int(x.team_id) == 0:
            message(x, '你还没有队伍')
        else:
            create = ChatMessage.objects.create(all_area_name=xx, area_name=xx, sender_player_id=0,
                                                sender_player_name=0, message=xxx, player_id=0,
                                                bangpai_id=-1, team_id=x.team_id, area_id=x.area_id, page_name=page_name)
    elif xx == '帮派':
        create = ChatMessage.objects.create(all_area_name=xx, area_name=xx, sender_player_id=0,
                                            sender_player_name=0, message=xxx, player_id=0,
                                            bangpai_id=x.bangpai_id, team_id=-1, area_id=x.area_id, page_name=page_name)
    else:
        pass
   # max_count = ChatMessage.objects.filter(area_name=xx, area_id=x.area_id).count()
    #if max_count >= 80:
    #    end_count = max_count - 50
    #    deletes = ChatMessage.objects.filter(area_name=xx, area_id=x.area_id)[0:end_count]
     #   for i in deletes:
    #        i.delete()


def chat_messages_u(x, xx, xxx,page_name=False):
    xxx = xxx.replace('<style>', '').replace('</style>', '').replace('<script>', '').replace('</script>', '').replace(
        '{', '').replace('}', '')
    xxx = '{}({})'.format(xxx, str(datetime.datetime.now())[0:19])
    create = ChatMessage.objects.create(all_area_name='私聊', area_name='私聊', sender_player_id=x.id,
                                        sender_player_name=x.name, message=xxx, player_id=xx.id,
                                        bangpai_id=-1, team_id=-1, area_id=x.area_id, page_name=page_name, zhuangtai=0)
    #max_count = ChatMessage.objects.filter(area_name='私聊', player_id=xx.id, area_id=x.area_id).count()
    #if max_count >= 80:
   #     end_count = max_count - 50
   #     deletes = ChatMessage.objects.filter(area_name='私聊', player_id=xx.id, area_id=x.area_id)[0:end_count]
   #     for i in deletes:
   #         i.delete()


def chat_messages(xx, xxx,page_name=False):
    xxx = xxx.replace('<style>', '').replace('</style>', '').replace('<script>', '').replace('</script>', '').replace(
        '{', '').replace('}', '')
    xxx = '{}({})'.format(xxx, str(datetime.datetime.now())[0:19])
    create = ChatMessage.objects.create(all_area_name='私聊', area_name='私聊', sender_player_id=0,
                                        sender_player_name=0, message=xxx, player_id=xx.id,
                                        bangpai_id=-1, team_id=-1, area_id=xx.area_id, page_name=page_name,zhuangtai=0)

    #max_count = ChatMessage.objects.filter(area_name='私聊', player_id=xx.id, area_id=xx.area_id).count()
    #if max_count >= 80:
    #    end_count = max_count - 50
    #    deletes = ChatMessage.objects.filter(area_name='私聊', player_id=xx.id, area_id=xx.area_id)[0:end_count]
    #    for i in deletes:
    #        i.delete()



'''
# 参数检测
def parameter_check1(u, name):
    # 获得参数

    source = name
    # 1.对字符串进行编码
    name1 = '{}'.format(u.shua_xing)
    name1_bytes = name1.encode('gbk')  # 把字符串转为字节类型
    name1_base64 = base64.b64encode(name1_bytes)  # base64 编码
    # print('base64加密后的内容：', name1_base64.decode())

    ref = name1_base64.decode('gbk')

    result = re.sub(ref, "", source)
    if name == result:
        return 'False'
    else:
        # 此处使用正则匹配将name1_base64加密后内容与name_base64进行匹配，完全匹配成功将之删除
        # 即 MTEx 完全匹配5bCP5piOMTEx时，得到删除后的5bCP5piO

        # 对base64数据进行解码
        # a = '{}'.format(result)
        # missing_padding = 4 - len(a) % 4
        # if missing_padding:
        #    a += b'=' * missing_padding
        # name2 = base64.b64decode(a)
        # print('字节类型转换为字符串类型：', name2.decode())
        # message(u, 'ref = {} , source = {},result = {}'.format(ref, source, result))
        name2 = base64.b64decode(result)
        return str(name2.decode('gbk'))

def parameter_create1(u, value):
    # 创建参数
    # 1.对字符串进行编码和解码
    name = '{}'.format(value)
    name_bytes = name.encode('gbk')  # 把字符串转为字节类型
    name_base64 = base64.b64encode(name_bytes)  # base64 编码
    # print('base64加密后的内容：', name_base64.decode())

    name1 = '{}'.format(u.shua_xing)
    name1_bytes = name1.encode('gbk')  # 把字符串转为字节类型
    name1_base64 = base64.b64encode(name1_bytes)  # base64 编码
    # print('base64加密后的内容：', name1_base64.decode())

    return '{}{}'.format(name_base64.decode('gbk'), name1_base64.decode('gbk'))
'''


def parameter_check(request, name):
    if request.session['encryption'].get(name) is None:
        return 'False'
    else:
        a = request.session['encryption'].get(name)
        # del request.session['encryption'][name]
        return a


def parameter_create(encryption, value):
    sj = ''.join(random.sample(string.ascii_letters + string.digits, 10))
    encryption[sj] = value
    return sj

# 加密解密失败
#def wap_rest(request):
#    c = GameObject(GameAttributeNew.objects.get(id=1))
#    user = GameObject(User.objects.get(id=request.session['user_id']))
 #   return render(request, 'wap_rest.html', locals())
# -----------------------------游戏场景-----------------------
# 单独视图缓存,配合@使用  @cache_page(60 * 15)
#@gzip_page
def wap(request):  # cache
    if request.session.get('user_id', 'None') == 'None':  # 耗时1.4毫秒
        return redirect("/login/")
    if request.GET.get('player_id'):
        request.session['player_id'] = parameter_check(request, name=request.GET.get('player_id'))
    encryption = {}
    neyong = ''
    starttime = time.time()
    u = GameObject(Player.objects.get(id=request.session.get('player_id')))
    #get_tishi(request, '前置事件执行后,个人全局代码{}'.format(u.map_id))
    if UtPlayer.objects.filter(player_id=u.id).count() == 0:
        ut = GameObject(UtPlayer.objects.create(player_id=u.id))
    else:
        ut = GameObject(UtPlayer.objects.get(player_id=u.id))
    if UhPlayer.objects.filter(player_id=u.id).count() == 0:
        uh = GameObject(UhPlayer.objects.create(player_id=u.id))
    else:
        uh = GameObject(UhPlayer.objects.get(player_id=u.id))
    g = GameObject(PublicAttributeNew.objects.get(id=request.session['area_id']))
    try:
        c = GameObject(GameAttributeNew.objects.get(id=1))
    except:
        c = GameObject(GameAttributeNew.objects.create(id=1))
    ls_sx = 0
    if int(u.fenghao) == 1:  # or str(u.name).isdigit():
        neyong = '此号已封:{}(ID:{}) {}级'.format(u.name, u.id, u.lvl)
        return render(request, "wap.html", locals())
    if eval(u.is_designer) or int(c.is_state) != 2:
        pass
    else:
        neyong = '游戏维护更新中，联系QQ565589841'
        return render(request, "wap.html", locals())
    #time.sleep(0.01)
    request.session['info_message'] = ''
    if True:#u.vip_lvl < 10 and u.xiansu_time < c.time
        request.session['sudu_cs'] = request.session.get('sudu_cs', 1)
        #now_sudu = 400 - (u.vip_lvl * 10)
        now_sudu = 200-(150 if u.xiansu_time > c.time else 0)
        xiansu = (int(time.time() * 1000) - int(request.session.get('sudu_start_time', 0)))/int(request.session['sudu_cs'])
        if int(request.session['sudu_cs'])>=5 and xiansu > now_sudu or int(request.session['sudu_cs'])>=500:# or int(request.session['sudu_cs'])>=20:
            request.session['sudu_cs'] = 0
        elif int(request.session['sudu_cs'])>=5 and xiansu <= now_sudu:
            request.session['sudu_cs'] = int(request.session['sudu_cs']) + 1
        if int(request.session['sudu_cs']) == 1:
            request.session['sudu_start_time'] = int(time.time() * 1000)
            request.session['sudu_cs'] = 2
        elif request.session.get('sudu_cs', 0) < 10:
            request.session['sudu_cs'] = int(request.session['sudu_cs']) + 1
        else:
            messages = request.session['sudu_cs']
            if xiansu <= now_sudu:  # 点击返回游戏时会重置
                messages = '{}点击过快，平均{}ms'.format(messages,xiansu)
                neyong = '{}<br/><a href="/wap/?qingchu=1">返回游戏</a><br/>'.format(messages)
                return render(request, 'wap.html', locals())
                #return redirect("/wap_rest/")
                #time.sleep(5.5000)
            else:
                request.session['sudu_start_time'] = int(time.time() * 1000)
                request.session['sudu_cs'] = 1
        #get_tishi(request, request.session['sudu_cs'])
    if int(request.session.get('pmwap_session_password', 0)) != int(u.session_password):
        messages = '异地登陆，帐号下线'  # 防止1个号同时登陆
        request.session['user_id'] = 'None'
        request.session['user_id'] = '0'
        return redirect("/login/")
    if request.GET.get('qingchu'):
        #request.session['sudu_start_time'] = int(time.time() * 1000)
        #request.session['sudu_cs'] = 11
        u.set('jm_parameter', 0)
    wap_u_id = int(u.id)
    u.set('shua_xing', request.session.get('pmwap_shua_xing', None))
    u.set('time', int(time.time()))
    u.set('message', '')
    e = GetValue()  # 表达式属性
    # c = GameValue()  # 游戏属性
    c.set('time', int(time.time()))
    c.set('year', int(datetime.datetime.now().year))
    c.set('month', int(datetime.datetime.now().month))
    c.set('day', int(datetime.datetime.now().day))
    c.set('hour', int(datetime.datetime.now().hour))
    c.set('minute', int(datetime.datetime.now().minute))
    c.set('second', int(datetime.datetime.now().second))
    c.set('week', int(time.strftime("%W")))
    c.set('weekday', int(datetime.date.isoweekday(datetime.date.today())))
    t = ImgValue()  # 代表图片
    request.session['pmwap_ls_page_name'] = request.session['wap_page_name']
    request.session['ls_page_name'] = request.session['wap_page_name']
    map_id = u.map_id
    #get_tishi(request, '前置事件执行后,个人全局代码{}'.format(u.map_id))
    exec(c.info_message)  # 个人全局代码
    #get_tishi(request, '前置事件执行后个人全局代码{}'.format(u.map_id))
    if request.session['wap_page_name'] == '场景' and int(u.map_id) != 0:
        o = GameObject(GameMap.objects.get(id=u.map_id))
    elif request.session['wap_page_name'] == '查看装备' and request.session.get('check_item_id') != 0:
        if ItemPlayer.objects.filter(id=request.session.get('check_item_id')).count() > 0:
            o = GameObject(ItemPlayer.objects.get(id=request.session.get('check_item_id')))
    elif request.session['wap_page_name'] == '查看物品' and request.session.get('check_item_id') != 0 or \
            request.session['wap_page_name'] == '丢弃物品' or request.session['wap_page_name'] == '帮会仓库查看物品':
        # if ItemPlayer.objects.filter(id=request.session.get('check_item_id')).count() > 0:
        try:
            o = GameObject(ItemPlayer.objects.get(id=request.session.get('check_item_id')))
            if int(o.count) > 0:
                pass
            else:
                request.session['wap_page_name'] = '背包'
                o = ItemPlayer.objects.get(id=request.session.get('check_item_id')).delete()
        except:
            pass
    elif request.session['wap_page_name'] == '帮会仓库查看物品':
        o = GameObject(CangKuPlayer.objects.get(id=request.session.get('cangku_check_item_id')))
        if int(o.count) > 0:
            pass
        else:
            request.session['wap_page_name'] = '仓库'
            o = CangKuPlayer.objects.get(id=request.session.get('cangku_check_item_id')).delete()
    elif request.session['wap_page_name'] == '查看玩家' and request.session.get('check_player_id') != 0:
        o = GameObject(Player.objects.get(id=request.session.get('check_player_id')))
        oh = GameObject(UhPlayer.objects.get(player_id=o.id))
    elif request.session['wap_page_name'] == '查看宠物' and request.session.get('check_pets_id') != 0 or \
            request.session['wap_page_name'] == '查看机甲' and u.check_pets_id != 0 or request.session[
        'wap_page_name'] == '查看武将':
        o = GameObject(Pets.objects.get(id=request.session.get('check_pets_id')))
    elif request.session['wap_page_name'] == '查看士兵' and request.session.get('check_soldier_id', '0') != '0' or \
            request.session['wap_page_name'] == '招募士兵' and request.session.get('check_soldier_id', '0') != '0':
        # if Soldier.objects.filter(id=request.session.get('check_soldier_id')).count() > 0:
        try:
            o = GameObject(Soldier.objects.get(id=request.session.get('check_soldier_id')))
        except:
            pass
    elif request.session['wap_page_name'] == '查看坐骑' and request.session.get('check_mount_id', '0') != 0:
        # if Mount.objects.filter(id=request.session['check_mount_id']).count() > 0:
        try:
            o = GameObject(Mount.objects.get(id=request.session['check_mount_id']))
        except:
            pass
    elif request.session['wap_page_name'] == '电脑人物' and request.session.get('npc_id', '0') != 0:
        # if GameNpcs.objects.filter(id=request.session['npc_id']).count() > 0:
        try:
            o = GameObject(GameNpcs.objects.get(id=request.session['npc_id']))
        except:
            pass
    ############### 此代码在战帝不需要使用###############
    # elif request.session['wap_page_name'] == '战斗' and int(u.pk_npc_id) != 0 or request.session['wap_page_name'] == '战斗' and int(u.pk_player_id) != 0:
    #    if int(u.pk_npc_id) != 0:
    #        if GameNpcs.objects.filter(id=u.pk_npc_id).count() > 0:
    #            o = GameObject(GameNpcs.objects.get(id=u.pk_npc_id))
    #    if int(u.pk_player_id) != 0:
    #        if Player.objects.filter(id=u.pk_player_id).count() > 0:
    #            o = GameObject(Player.objects.get(id=u.pk_player_id))
    #    if Monster.objects.filter(player_id=u.id, zhuangtai=1).count() > 0:
    #        o = Monster.objects.filter(player_id=u.id, zhuangtai=1)[0:1]
    #        o = o.iterator()
    #        o = dict.fromkeys(o)
    #        for i in o:
    #            o = GameObject(i)
    ############### 此代码在战帝不需要使用###############
    elif request.session['wap_page_name'] == '商城购买':
        o = GameObject(Item.objects.get(id=request.session.get('shangcheng_item_id')))
    elif request.session['wap_page_name'] == '任务' and request.session.get('check_task_id') != 0 or request.session[
        'wap_page_name'] == '查看任务' and request.session.get('check_task_id') != 0:
        o = GameObject(Task.objects.get(id=request.session.get('check_task_id')))
    ############### 此代码在战帝不需要使用###############
    elif request.session['wap_page_name'] == '查看技能':
        if Skills.objects.filter(id=request.session.get('check_skill_id')).count() > 0:
            o = GameObject(Skills.objects.get(id=request.session.get('check_skill_id')))
    ############### 此代码在战帝不需要使用###############
    elif request.session['wap_page_name'] == '帮派信息' or request.session['wap_page_name'] == '创建堂口':
        try:  # if Gangs.objects.filter(id=request.session.get('check_gangs_id')).count() > 0:
            o = GameObject(Gangs.objects.get(id=request.session.get('check_gangs_id')))
        except:
            pass
    elif request.session['wap_page_name'] == '帮会技能' or  request.session['wap_page_name'] == '我的帮会' or request.session['wap_page_name'] == '进帮审批'  or request.session['wap_page_name'] == '帮会成员':
        #try:  # if Gangs.objects.filter(id=request.session.get('check_gangs_id')).count() > 0:
        o = GameObject(BangHui.objects.get(id=request.session.get('check_banghui_id')))
        #except:
        #    pass
    elif request.session['wap_page_name'] == '帮派成员':
        try:  # if GangsTangkou.objects.filter(id=request.session.get('check_tangkou_id')).count() > 0:
            o = GangsTangkou.objects.get(id=request.session.get('check_tangkou_id'))
        except:
            pass
    else:
        o = GameObject(GameMap.objects.get(id=u.map_id))
    suo = GameObject(Suo.objects.get(player_id=u.id))
    #get_tishi(request, '前置事件执行后,事件执行前{}'.format(u.map_id))
    if int(suo.update) == 0:
        Suo.objects.filter(player_id=u.id).update(update=1)
        if request.GET.get('chat'):
            if int(u.chat_area_id) == 1:
                u.set('message', '{}无法发送全部信息<br/>'.format(u.message))
            elif int(u.chat_area_id) == 5:
                u.set('message', '{}无法发送私人信息<br/>'.format(u.message))
            else:
                name = '世界' if int(u.chat_area_id) == 2 else '系统' if int(u.chat_area_id) == 3 else '帮派' if int(
                    u.chat_area_id) == 4 else '队伍'
                chat_message_u(u, name, request.GET.get('chat'))
        # 获取点击后操作ID--------------------------------
        elif request.GET.get('ck'):
            u.set('jm_parameter', request.GET.get('ck'))
            u.set('chat_page', 0)
            u.set('item_page', 0)
            ck = parameter_check(request, name=u.jm_parameter)
            if ck == 'False':
                pass
            else:
                # 参数校验
                event = Event.objects.get(operation=ck)
                # 运行事件ID里的代码
                #code = re.sub('Card', "", event.code)
                exec(event.code)
        # PK事项------------------------------------
        elif request.GET.get('pk_get_into'):  # 进入PK模板
            u.set('jm_parameter', request.GET.get('pk_get_into'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                npc_id = parameter_check(request, name=request.GET.get('npc_id'))
                request.session['npc_id'] = npc_id
                if True:
                    try:
                        o = GameObject(GameNpcs.objects.get(id=npc_id))
                        if o.hp <= 0:
                            get_tishi(request, '怪物已死亡')
                        else:
                            Monster.objects.filter(player_id=u.id, zhuangtai=1).update(zhuangtai=0)
                            if int(o.is_boss) == 1:
                                request.session['wap_page_name'] = '战斗'
                                u.set('pk_player_id', 0)
                                u.set('pk_npc_id', o.id)
                                u.save()
                            else:
                                bh = 0
                                monsters = Monster.objects.filter(player_id=u.id)[0:1]
                                for monster in monsters:
                                    bh = 1
                                    monster = GameObject(monster)
                                if bh == 0:
                                    monster = Monster.objects.create()  # 场景NPC
                                    monster = GameObject(monster)
                                    monster.set('player_id', u.id)  # 对应玩家
                                monster.set('params', o.params)
                                monster.set('name', o.name)
                                monster.set('desc', o.desc)
                                monster.set('is_drop', o.is_drop)
                                monster.set('is_boss', o.is_boss)
                                monster.set('exp_expression', o.exp_expression)
                                monster.set('money_expression', o.money_expression)
                                monster.set('lingqi_expression', o.lingqi_expression)
                                monster.set('ys_id', o.ys_id)  # 对应系统NPC
                                monster.set('npc_id', o.id)  # 对应系统NPC
                                monster.set('zhuangtai', 1)  # 状态
                                monster.save()
                                request.session['wap_page_name'] = '战斗'
                                u.set('pk_player_id', 0)
                                u.set('pk_npc_id', 0)
                                u.save()
                    except:
                        get_tishi(request, 'NPC已消失')
                        request.session['wap_page_name'] = '场景'
        # 存入仓库
        elif request.GET.get('save_cangku_id'):
            u.set('jm_parameter', request.GET.get('save_cangku_id'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                save_cangku_id = parameter_check(request, name=request.GET.get('save_cangku_id'))
                save_cangku_count = request.GET.get('save_cangku_count')
                o = ItemPlayer.objects.get(id=save_cangku_id)
                if int(o.count) >= int(save_cangku_count) and int(save_cangku_count) > 0:
                    if CangKuPlayer.objects.filter(player_id=u.id, item_id=o.item_id,
                                                   bangding=o.bangding).count() == 0 or o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                        CangKuPlayer.objects.create(player_id=o.player_id, bangding=o.bangding, name=o.name,
                                                    desc=o.desc, update=o.update, pets_id=o.pets_id, map_id=o.map_id,
                                                    area_id=o.area_id,
                                                    count=save_cangku_count, item_id=o.item_id, area_name=o.area_name,
                                                    type=o.type, type_id=o.type_id,
                                                    params=o.params, duixiang=o.duixiang, biaoshi=o.biaoshi,
                                                    suoding=o.suoding)
                        if o.bangding == 1 or o.bangding == '1':
                            get_tishi(request, '存仓 [绑]{}*{} 成功'.format(o.name, save_cangku_count))
                        else:
                            get_tishi(request, '存仓 {}*{} 成功'.format(o.name, save_cangku_count))
                    else:
                        items = CangKuPlayer.objects.filter(player_id=o.player_id, item_id=o.item_id,
                                                            bangding=o.bangding)
                        item = Item.objects.get(id=o.item_id)
                        for i in items:
                            i.count = int(i.count) + int(save_cangku_count)
                            i.name = item.name
                            i.save()
                            if o.bangding == 1 or o.bangding == '1':
                                get_tishi(request, '存仓 [绑]{}*{} 成功'.format(o.name, save_cangku_count))
                            else:
                                get_tishi(request, '存仓 {}*{} 成功'.format(o.name, save_cangku_count))
                            break
                    if int(o.count) <= int(
                            save_cangku_count) or o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                        request.session['wap_page_name'] = '背包'
                        o.delete()
                    else:
                        o.set('count', int(o.count) - int(save_cangku_count))
                        o.save()
                else:
                    if int(save_cangku_count) <= 0:
                        get_tishi(request, '请输入存入数量')
                    else:
                        get_tishi(request, '你没有这么多物品可存入仓库')
        # 仓库取出
        elif request.GET.get('take_cangku_id'):
            u.set('jm_parameter', request.GET.get('take_cangku_id'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                take_cangku_id = parameter_check(request, name=request.GET.get('take_cangku_id'))
                take_cangku_count = request.GET.get('take_cangku_count')
                o = CangKuPlayer.objects.get(id=take_cangku_id)
                if int(o.count) >= int(take_cangku_count) and int(take_cangku_count) > 0:
                    if ItemPlayer.objects.filter(player_id=u.id, item_id=o.item_id,
                                                 bangding=o.bangding).count() == 0 or o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                        ItemPlayer.objects.create(player_id=o.player_id, bangding=o.bangding, name=o.name,
                                                  desc=o.desc, update=o.update, pets_id=o.pets_id, map_id=o.map_id,
                                                  area_id=o.area_id,
                                                  count=take_cangku_count, item_id=o.item_id, area_name=o.area_name,
                                                  type=o.type, type_id=o.type_id,
                                                  params=o.params, duixiang=o.duixiang, biaoshi=o.biaoshi,
                                                  suoding=o.suoding)
                        if o.bangding == 1 or o.bangding == '1':
                            get_tishi(request, '取出 [绑]{}*{} 成功'.format(o.name, take_cangku_count))
                        else:
                            get_tishi(request, '取出 {}*{} 成功'.format(o.name, take_cangku_count))
                    else:
                        items = ItemPlayer.objects.filter(player_id=o.player_id, item_id=o.item_id, bangding=o.bangding)
                        item = Item.objects.get(id=o.item_id)
                        for i in items:
                            i.count = int(i.count) + int(take_cangku_count)
                            i.name = item.name
                            i.save()
                            if o.bangding == 1 or o.bangding == '1':
                                get_tishi(request, '取出 [绑]{}*{} 成功'.format(o.name, take_cangku_count))
                            else:
                                get_tishi(request, '取出 {}*{} 成功'.format(o.name, take_cangku_count))
                            break
                    if int(o.count) <= int(
                            take_cangku_count) or o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                        request.session['wap_page_name'] = '仓库'
                        o.delete()
                    else:
                        o.set('count', int(o.count) - int(take_cangku_count))
                        o.save()
                else:
                    if int(take_cangku_count) <= 0:
                        get_tishi(request, '请输入取回数量')
                    else:
                        get_tishi(request, '你没有这么多物品可取出')
        # 上下页，仓库--------------------------------
        elif request.GET.get('cangku_up_page') or request.GET.get('cangku_next_page'):
            if request.GET.get('cangku_up_page'):
                u.set('jm_parameter', request.GET.get('cangku_up_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('cangku_up_page'))
                    request.session['cangku_page_yeshu'] = ck
            if request.GET.get('cangku_next_page'):
                u.set('jm_parameter', request.GET.get('cangku_next_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('cangku_next_page'))
                    request.session['cangku_page_yeshu'] = ck
            if request.GET.get('cangku_page_area_id'):
                u.set('jm_parameter', request.GET.get('cangku_page_area_id'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('cangku_page_area_id'))
                    request.session['cangku_page_area_id'] = ck
                    area_names = ItemAreaName.objects.all()
                    for i in area_names:
                        if int(i.id) == int(ck):
                            request.session['cangku_page_area_name'] = i.area_name
                            break
                        elif int(ck) == 0:
                            request.session['cangku_page_area_name'] = '全部'
                            break
                        else:
                            pass
        # 入队申请--------------------------------
        elif request.GET.get('in_team'):
            u.set('jm_parameter', request.GET.get('in_team'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                in_team = parameter_check(request, name=request.GET.get('in_team'))
                in_team_player = parameter_check(request, name=request.GET.get('in_team_player'))
                players = Player.objects.get(id=in_team_player)
                # 拒绝
                if int(in_team) == 0:
                    if InTeam.objects.filter(team_id=u.team_id, player_id=in_team_player).count() == 0:
                        u.set('message', '{}对方申请信息已失效<br/>'.format(u.message))
                    else:
                        in_teams = InTeam.objects.filter(team_id=u.team_id, player_id=in_team_player).delete()
                        chat_messages_u(u, players, '拒绝了你的入队申请')
                        u.set('message', '{}你拒绝了:{}入队申请<br/>'.format(u.message, players.name))
                # 同意
                elif int(in_team) == 1:
                    if c.team_max_count > Team.objects.filter(team_id=u.team_id).count():
                        if int(players.team_id) == int(u.team_id):
                            u.set('message', '{}{}已经在队伍中了，无需重复同意<br/>'.format(u.message, players.name))
                        elif int(players.team_id) > 0 or Team.objects.filter(player_id=players.id).count() > 0:
                            u.set('message', '{}{}已经拥有队伍了<br/>'.format(u.message, players.name))
                        else:
                            teams = Team.objects.get(team_id=u.team_id, player_id=wap_u_id)
                            create = Team.objects.create(team_id=u.team_id, player_id=players.id,
                                                         team_name=teams.team_name)
                            in_teams = InTeam.objects.filter(team_id=u.team_id, player_id=in_team_player).delete()
                            u.set('message', '你同意了:{}加入队伍'.format(players.name))
                            chat_messages_u(u, players, '同意了你的入队申请')
                            chat_message(u, '组队', '{}加入了队伍'.format(players.name))
                            players.set('team_id', u.team_id)
                    else:
                        get_tishi(request, '组队人数已达上限')
                elif int(in_team) == 2:
                    if int(players.team_id) == int(u.team_id):
                        teams = Team.objects.get(team_id=u.team_id, player_id=players.id).delete()
                        chat_message(u, '组队', '将{}请离了队伍'.format(players.name))
                        chat_messages_u(u, players, '将你请离了队伍')
                        players.set('team_id', 0)
                        u.set('message', '{}踢{}出队伍成功<br/>'.format(u.message, players.name))
                    elif int(players.team_id) > 0:
                        u.set('message', '{}对方不是你队伍中的人<br/>'.format(u.message))
                    else:
                        u.set('message', '{}还没有队伍<br/>'.format(u.message))
                else:
                    pass
                players.save()
        # 入帮派申请--------------------------------
        elif request.GET.get('in_gangs'):
            u.set('jm_parameter', request.GET.get('in_gangs'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                in_gangs = parameter_check(request, name=request.GET.get('in_gangs'))
                in_gangs_player = parameter_check(request, name=request.GET.get('in_gangs_player'))
                players = Player.objects.get(id=in_gangs_player)
                # 拒绝
                if int(in_gangs) == 0:
                    if InGangs.objects.filter(bangpai_id=u.bangpai_id, player_id=in_gangs_player) == 0:
                        get_tishi(request, '对方申请信息已失效')
                    else:
                        in_gangss = InGangs.objects.filter(bangpai_id=u.bangpai_id, player_id=in_gangs_player).delete()
                        chat_messages_u(u, players, '拒绝了你的入帮申请')
                        get_tishi(request, '你拒绝了:{} 入帮申请'.format(players.name))
                # 同意
                elif int(in_gangs) == 1:
                    bp = GameObject(Gangs.objects.get(id=u.bangpai_id))
                    if GangsMember.objects.filter(bangpai_id=u.bangpai_id, member_id=in_gangs_player).count() > 0:
                        get_tishi(request, '{}已经在帮派中了，无需重复同意'.format(players.name))
                    elif GangsMember.objects.filter(member_id=in_gangs_player).count() > 0:
                        get_tishi(request, '{}已经拥有帮派了'.format(players.name))
                    elif GangsMember.objects.filter(bangpai_id=u.bangpai_id).count() >= bp.max_count:
                        get_tishi(request, '帮派人数已达上限，请升级帮派')
                    else:
                        gangss = InGangs.objects.get(bangpai_id=u.bangpai_id, player_id=in_gangs_player)
                        if GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, id=gangss.tangkou_id).count() == 0:
                            tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id)
                            for tangkou in tangkous:
                                create = GangsMember.objects.create(bangpai_id=u.bangpai_id, member_id=players.id,
                                                                    tangkou_id=tangkou.id)
                                break
                        else:
                            create = GangsMember.objects.create(bangpai_id=u.bangpai_id, member_id=players.id,
                                                                tangkou_id=gangss.tangkou_id)
                        in_gangss = InGangs.objects.filter(bangpai_id=u.bangpai_id, player_id=in_gangs_player).delete()
                        get_tishi(request, '你同意了:{}加入帮派'.format(players.name))
                        chat_messages_u(u, players, '同意了你的入帮申请')
                        chat_message(u, '帮派', '{}加入了帮派'.format(players.name))
                        players.set('bangpai_id', u.bangpai_id)
                        players.save()
                #            elif int(in_gangs) == 2:
                #                if int(players.gangs_id) == int(u.gangs_id):
                #                    gangss = Gangs.objects.get(gangs_id=u.gangs_id, player_id=players.id)
                #                   gangss.delete()
                #                    chat_get_tishi(request, '组队', '将{}请离了队伍'.format(players.name))
                #                   chat_messages_u(u, players, '将你请离了队伍')
                #                   players.set('gangs_id', 0)
                #                    players.save()
                #                   get_tishi(request, '踢{}出队伍成功'.format(players.name))
                #               elif int(players.gangs_id) > 0:
                #                   get_tishi(request, '对方不是你队伍中的人')
                #               else:
                #                  get_tishi(request, '还没有队伍')
                else:
                    pass
        # 帮派任命--------------------------------
        elif request.GET.get('renming_bh'):
            u.set('jm_parameter', request.GET.get('renming_bh'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                renming_bh = parameter_check(request, name=request.GET.get('renming_bh'))
                renming_bh = int(renming_bh)
                wj = Player.objects.get(id=request.session['object_ck_id'])
                gangs = Gangs.objects.get(id=u.bangpai_id)
                tangkou = GangsTangkou.objects.get(id=request.session['object_z_bh'])
                tangkou_id = 0
                if request.GET.get('ls_tangkou'):
                    tangkou_id = parameter_check(request, name=request.GET.get('ls_tangkou'))
                    tangkou_id = int(tangkou_id)
                if renming_bh == 4:  # 禅让帮主
                    if int(gangs.bangzhu_id) == wap_u_id:
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, tangzhu_id=wj.id)
                        for i in tangkous:
                            i.tangzhu_id = 0
                            i.save()
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, futangzhu_id=wj.id)
                        for i in tangkous:
                            i.futangzhu_id = 0
                            i.save()
                        gangs.bangzhu_id = wj.id
                        gangs.generation = int(gangs.generation) + 1
                        gangs.save()
                        get_tishi(request, '禅让帮主成功')
                        chat_message_u(u, '帮派', '将帮主位置禅让给了{}'.format(wj.name))  # 发送世界消息可点击玩家
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id)[0:1]
                        for i in tangkous:
                            gangsmember = GangsMember.objects.get(member_id=wj.id, bangpai_id=u.bangpai_id)
                            gangsmember.tangkou_id = i.id
                            gangsmember.save()
                elif renming_bh == 3:  # 任命副帮主
                    if int(gangs.bangzhu_id) == wap_u_id:
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, tangzhu_id=wj.id)
                        for i in tangkous:
                            i.tangzhu_id = 0
                            i.save()
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, futangzhu_id=wj.id)
                        for i in tangkous:
                            i.futangzhu_id = 0
                            i.save()
                        gangs.fubangzhu_id = wj.id
                        gangs.save()
                        get_tishi(request, '任命副帮主成功')
                        chat_message_u(u, '帮派', '任命{}为副帮主'.format(wj.name))  # 发送世界消息可点击玩家
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id)[0:1]
                        for i in tangkous:
                            gangsmember = GangsMember.objects.get(member_id=wj.id, bangpai_id=u.bangpai_id)
                            gangsmember.tangkou_id = i.id
                            gangsmember.save()
                elif renming_bh == 1 or renming_bh == 2:  # 任命堂主
                    if int(gangs.bangzhu_id) == int(wap_u_id) or int(gangs.fubangzhu_id) == int(wap_u_id) or int(
                            tangkou.tangzhu_id) == int(wap_u_id):
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, tangzhu_id=wj.id)
                        for i in tangkous:
                            i.tangzhu_id = 0
                            i.save()
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, futangzhu_id=wj.id)
                        for i in tangkous:
                            i.futangzhu_id = 0
                            i.save()
                    if int(gangs.bangzhu_id) == int(wap_u_id) or int(gangs.fubangzhu_id) == int(wap_u_id):
                        if renming_bh == 1:  # 任命堂主
                            tangkou = GangsTangkou.objects.get(id=tangkou_id)
                            tangkou.tangzhu_id = wj.id
                            tangkou.save()
                            get_tishi(request, '任命为{}堂主成功'.format(tangkou.name))
                            chat_message_u(u, '帮派', '任命{}为{}堂主'.format(wj.name, tangkou.name))  # 发送世界消息可点击玩家
                    if renming_bh == 2:  # 任命副堂主
                        tangkou = GangsTangkou.objects.get(id=tangkou_id)
                        tangkou.futangzhu_id = wj.id
                        tangkou.save()
                        get_tishi(request, '任命为{}副堂主成功'.format(tangkou.name))
                        chat_message_u(u, '帮派', '任命{}为{}副堂主'.format(wj.name, tangkou.name))  # 发送世界消息可点击玩家
                    gangsmember = GangsMember.objects.get(member_id=wj.id, bangpai_id=u.bangpai_id)
                    gangsmember.tangkou_id = tangkou_id
                    gangsmember.save()
                elif renming_bh == 11:  # 踢出帮派
                    if int(gangs.bangzhu_id) == int(u.id) or int(gangs.fubangzhu_id) == int(u.id) or int(
                            tangkou.tangzhu_id) == int(u.id) or int(tangkou.futangzhu_id) == int(u.id):
                        if int(gangs.bangzhu_id) == int(u.id) and int(wj.id) != int(u.id) or int(
                                gangs.bangzhu_id) != int(
                            wj.id) and int(wj.id) != int(u.id) and int(gangs.fubangzhu_id) == int(u.id) or int(
                            gangs.bangzhu_id) != int(wj.id) and int(wj.id) != int(u.id) and int(
                            gangs.fubangzhu_id) != int(wj.id) and int(o.tangzhu_id) == int(u.id) or int(
                            gangs.bangzhu_id) != int(wj.id) and int(wj.id) != int(u.id) and int(
                            gangs.fubangzhu_id) != int(wj.id) and int(o.tangzhu_id) != int(wj.id) and int(
                            o.futangzhu_id) == int(u.id):
                            tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, tangzhu_id=wj.id)
                            for i in tangkous:
                                i.tangzhu_id = 0
                                i.save()
                            tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, futangzhu_id=wj.id)
                            for i in tangkous:
                                i.futangzhu_id = 0
                                i.save()
                            gangsmember = GangsMember.objects.get(member_id=wj.id, bangpai_id=u.bangpai_id)
                            gangsmember.delete()
                            get_tishi(request, '踢出玩家成功')
                            chat_message_u(u, '帮派', '将{}踢出了帮派'.format(wj.name))  # 发送世界消息可点击玩家
                            chat_messages(wj, '{} 将你踢出了帮派'.format(u.name))  # 发送私聊消息不可点击玩家
                        else:
                            get_tishi(request, '你没有权限踢出该玩家')
                    else:
                        get_tishi(request, '你没有权限将玩家踢出帮派(帮主/副帮主/本堂堂主/副堂主才可以)')
                elif renming_bh == 12:  # 解除职务
                    if int(gangs.bangzhu_id) == int(u.id) and int(wj.id) != int(u.id) or int(gangs.bangzhu_id) != int(
                            wj.id) and int(wj.id) != int(u.id) and int(gangs.fubangzhu_id) == int(u.id) or int(
                        gangs.bangzhu_id) != int(wj.id) and int(wj.id) != int(u.id) and int(gangs.fubangzhu_id) != int(
                        wj.id) and int(o.tangzhu_id) == int(u.id) or int(gangs.bangzhu_id) != int(wj.id) and int(
                        wj.id) != int(u.id) and int(gangs.fubangzhu_id) != int(wj.id) and int(o.tangzhu_id) != int(
                        wj.id) and int(o.futangzhu_id) == int(u.id):
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, tangzhu_id=wj.id)
                        for i in tangkous:
                            i.tangzhu_id = 0
                            i.save()
                        tangkous = GangsTangkou.objects.filter(bangpai_id=u.bangpai_id, futangzhu_id=wj.id)
                        for i in tangkous:
                            i.futangzhu_id = 0
                            i.save()
                        if int(gangs.fubangzhu_id) == int(wj.id):
                            gangs.fubangzhu_id = 0
                            gangs.save()
                        get_tishi(request, '解除职位成功')
                        chat_message_u(u, '帮派', '解除了:{}职务'.format(wj.name))  # 发送世界消息可点击玩家
                    else:
                        get_tishi(request, '你没有权限解除该玩家职位')
                else:
                    get_tishi(request, '未知错误，任命失败')
                move_page(request, request.session['up_page_name'])
        ############### 此代码在战帝不需要使用###############
        # 技能快捷设置--------------------------------
        elif request.GET.get('item_quick'):
            u.set('jm_parameter', request.GET.get('item_quick'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                ck = parameter_check(request, name=request.GET.get('item_quick'))
                o = GameObject(ItemPlayer.objects.get(id=request.session.get('check_item_id')))
                if QuickSkill.objects.filter(player_id=wap_u_id, quick=ck).count() == 0:
                    quickskill = QuickSkill.objects.create(player_id=wap_u_id)
                    quickskill.skill_id = 0
                    quickskill.item_id = o.id
                    quickskill.quick = ck
                    quickskill.player_id = wap_u_id
                    quickskill.save()
                    get_tishi(request, '设置物品快捷成功')
                else:
                    quickskill = QuickSkill.objects.get(player_id=wap_u_id, quick=ck)
                    quickskill.skill_id = 0
                    quickskill.item_id = o.id
                    quickskill.save()
                    get_tishi(request, '设置物品快捷成功.')
                skillquicks = QuickSkill.objects.filter(player_id=wap_u_id, item_id=0, skill_id=0).delete()
        elif request.GET.get('skill_quick'):
            u.set('jm_parameter', request.GET.get('skill_quick'))
            o = GameObject(Skills.objects.get(id=request.session['check_skill_id']))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                ck = parameter_check(request, name=request.GET.get('skill_quick'))
                if QuickSkill.objects.filter(player_id=wap_u_id, quick=ck).count() == 0:
                    quickskill = QuickSkill.objects.create(player_id=wap_u_id)
                    quickskill.skill_id = o.id
                    quickskill.item_id = 0
                    quickskill.quick = ck
                    quickskill.player_id = wap_u_id
                    quickskill.save()
                    get_tishi(request, '设置技能快捷成功')
                else:
                    quickskill = QuickSkill.objects.get(player_id=wap_u_id, quick=ck)
                    quickskill.skill_id = o.id
                    quickskill.item_id = 0
                    quickskill.save()
                    get_tishi(request, '设置技能快捷成功.')
                skillquicks = QuickSkill.objects.filter(player_id=wap_u_id, skill_id=0, item_id=0).delete()
        # 宠物出战--------------------------------
        # elif request.GET.get('pets_in_battle_id'):
        #    u.set('jm_parameter', request.GET.get('pets_in_battle_id'))
        #    if parameter_check(request, name=u.jm_parameter) == 'False':
        #        pass
        #    else:
        #        # 参数校验
        #        ck = parameter_check(request, name=request.GET.get('pets_in_battle_id'))
        #        if u.pets_chuzhang_1_id != 0 and u.pets_chuzhang_2_id != 0:
        #            get_tishi(request, '最多同时上阵两个宠物')
        #        else:
        #            if u.pets_chuzhang_1_id == 0:
        #                u.set('pets_chuzhang_1_id', ck)
        #                get_tishi(request, '出战成功1')
        #            elif u.pets_chuzhang_2_id == 0:
        #                u.set('pets_chuzhang_2_id', ck)
        #                get_tishi(request, '出战成功2')
        #            else:
        #                pass
        # 宠物收回--------------------------------
        # elif request.GET.get('pets_recall_id'):
        #    u.set('jm_parameter', request.GET.get('pets_recall_id'))
        #    if parameter_check(request, name=u.jm_parameter) == 'False':
        #        pass
        #    else:
        #        # 参数校验
        #        ck = parameter_check(request, name=request.GET.get('pets_recall_id'))
        #        if u.pets_chuzhang_1_id == ck:
        #            u.set('pets_chuzhang_1_id', 0)
        #            get_tishi(request, '宠物收回成功1')
        #        elif u.pets_chuzhang_2_id == ck:
        #            u.set('pets_chuzhang_2_id', 0)
        #            get_tishi(request, '宠物收回成功2')
        #        else:
        #            pass
        ############### 此代码在战帝不需要使用###############
        # 拾起物品
        elif request.GET.get('item_id'):
            u.set('jm_parameter', request.GET.get('item_id'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                item_id = parameter_check(request, name=request.GET.get('item_id'))
                if ItemPlayer.objects.filter(player_id=wap_u_id).count() < int(u.max_count) or int(u.max_count) <= 0:
                    if ItemMap.objects.filter(id=item_id).count() > 0:
                        itemplayer = ItemMap.objects.get(id=item_id)
                        itemplayer = GameObject(itemplayer)
                        if int(itemplayer.drop_time) > int(time.time()) and itemplayer.player_id == u.id or int(
                                itemplayer.drop_time) < int(time.time()):
                            if itemplayer.area_name == '装备' or itemplayer.area_name == '武将装备' or itemplayer.area_name == '士兵装备' or itemplayer.area_name == '坐骑装备' or itemplayer.area_name == '宠物装备':
                                if int(itemplayer.count) >= 1:
                                    wap_a = int(itemplayer.count) + 1
                                    if itemplayer.time > 0:
                                        is_time = itemplayer.time +c.time
                                    else:
                                        is_time = 0
                                    #get_tishi(request, itemplayer.time)
                                    for i in range(1, wap_a):
                                        item = ItemPlayer.objects.create(name=itemplayer.name, desc=itemplayer.desc,
                                                                         area_name=itemplayer.area_name,
                                                                         type=itemplayer.type,
                                                                         type_id=itemplayer.type_id,
                                                                         player_id=wap_u_id,
                                                                         params=itemplayer.params, map_id=0,
                                                                         bangding=itemplayer.bangding,
                                                                         item_id=itemplayer.item_id, count=1,
                                                                         duixiang=itemplayer.duixiang,
                                                                         biaoshi=itemplayer.biaoshi,time=is_time)
                                        if int(item.bangding) == 0:
                                            u.set('message',
                                                  '{}得到 {}x{}<br/>'.format(u.message, item.name, item.count))
                                        else:
                                            u.set('message',
                                                  '{}得到 [绑定]{}x{}<br/>'.format(u.message, item.name, item.count))
                            else:
                                if ItemPlayer.objects.filter(player_id=wap_u_id, item_id=itemplayer.item_id,
                                                             bangding=itemplayer.bangding).count() == 0:
                                    item = ItemPlayer.objects.create(name=itemplayer.name, desc=itemplayer.desc,
                                                                     area_name=itemplayer.area_name,
                                                                     type=itemplayer.type,
                                                                     type_id=itemplayer.type_id,
                                                                     player_id=wap_u_id,
                                                                     params=itemplayer.params, map_id=0,
                                                                     bangding=itemplayer.bangding,
                                                                     item_id=itemplayer.item_id, count=itemplayer.count,
                                                                     duixiang=itemplayer.duixiang,
                                                                     biaoshi=itemplayer.biaoshi)
                                    if int(item.bangding) == 0:
                                        u.set('message', '{}得到 {}x{}<br/>'.format(u.message, item.name, item.count))
                                    else:
                                        u.set('message',
                                              '{}得到 [绑定]{}x{}<br/>'.format(u.message, item.name, item.count))
                                else:
                                    if int(itemplayer.bangding) == 0:
                                        u.set('message',
                                              '{}得到 {}x{}<br/>'.format(u.message, itemplayer.name, itemplayer.count))
                                    else:
                                        u.set('message',
                                              '{}得到 [绑]{}x{}<br/>'.format(u.message, itemplayer.name,
                                                                             itemplayer.count))
                                    item = ItemPlayer.objects.filter(player_id=wap_u_id, item_id=itemplayer.item_id,
                                                                     bangding=itemplayer.bangding)
                                    for i in item:
                                        i.count = int(itemplayer.count) + int(i.count)
                                        i.name = itemplayer.name
                                        i.params = itemplayer.params
                                        i.biaoshi = itemplayer.biaoshi
                                        i.save()
                                        break
                            ItemMap.objects.filter(id=item_id).delete()
                            if request.GET.get('page_name'):
                                u.set('jm_parameter', request.GET.get('page_name'))
                                if parameter_check(request, name=u.jm_parameter) == 'False':
                                    pass
                                else:
                                    # 参数校验
                                    ck = parameter_check(request, name=request.GET.get('page_name'))
                                    request.session['wap_page_name'] = ck
                        else:
                            get_tishi(request,
                                      '这件物品您无法拾起，请等待{}秒后才能拾起'.format(
                                          int(itemplayer.drop_time) - int(time.time())))
                    else:
                        get_tishi(request, '物品已被TA人拾起')
                else:
                    get_tishi(request, '背包已满，无法拾起')
        # 获取点击后模板--------------------------------
        elif request.GET.get('page_name'):
            if request.GET.get('item_page_id'):
                u.set('jm_parameter', request.GET.get('item_page_id'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    request.session['item_page_id'] = parameter_check(request, name=request.GET.get('item_page_id'))
            u.set('jm_parameter', request.GET.get('page_name'))
            ck = parameter_check(request, name=request.GET.get('page_name'))
            if ck == 'False':
                pass
            else:
                # 参数校验
                request.session['wap_page_name'] = ck
                if request.GET.get('shangcheng_item_id'):
                    item_id = parameter_check(request, name=request.GET.get('shangcheng_item_id'))
                    request.session['shangcheng_item_id'] = item_id
                if request.GET.get('pets_up_page'):
                    u.set('jm_parameter', request.GET.get('pets_up_page'))
                    request.session['up_page'] = parameter_check(request, name=u.jm_parameter)
                if request.GET.get('use_up_page'):
                    u.set('jm_parameter', request.GET.get('use_up_page'))
                    request.session['up_page'] = parameter_check(request, name=u.jm_parameter)
                if request.GET.get('duixiang'):
                    u.set('jm_parameter', request.GET.get('duixiang'))
                    request.session['duixiang'] = parameter_check(request, name=u.jm_parameter)
                if request.GET.get('equip_area_name'):
                    u.set('jm_parameter', request.GET.get('equip_area_name'))
                    request.session['equip_area_name'] = parameter_check(request, name=u.jm_parameter)
                if request.GET.get('ck_item_id'):
                    ck_item_id = parameter_check(request, name=request.GET.get('ck_item_id'))
                    request.session['check_item_id'] = ck_item_id
                    if request.GET.get('zb_check_player_id'):
                        player_id = parameter_check(request, name=request.GET.get('zb_check_player_id'))
                        request.session['check_player_id'] = player_id
                elif request.GET.get('check_player_id'):
                    check_player_id = parameter_check(request, name=request.GET.get('check_player_id'))
                    request.session['check_player_id'] = check_player_id
                    if int(request.session['check_player_id']) == wap_u_id:
                        request.session['wap_page_name'] = '状态'
                elif request.GET.get('pets_id'):
                    pets_id = parameter_check(request, name=request.GET.get('pets_id'))
                    request.session['check_pets_id'] = pets_id
                elif request.GET.get('mount_id'):
                    mount_id = parameter_check(request, name=request.GET.get('mount_id'))
                    request.session['check_mount_id'] = mount_id
                elif request.GET.get('check_gangs_id'):
                    gangs_id = parameter_check(request, name=request.GET.get('check_gangs_id'))
                    request.session['check_gangs_id'] = gangs_id
                elif request.GET.get('check_banghui_id'):
                    banghui_id = parameter_check(request, name=request.GET.get('check_banghui_id'))
                    request.session['check_banghui_id'] = banghui_id
                elif request.GET.get('check_tangkou_id'):
                    tangkou_id = parameter_check(request, name=request.GET.get('check_tangkou_id'))
                    request.session['check_tangkou_id'] = tangkou_id
                elif request.GET.get('skill_id'):
                    skill_id = parameter_check(request, name=request.GET.get('skill_id'))
                    request.session['check_skill_id'] = skill_id
                elif request.GET.get('cangku_ck_item_id'):
                    ck_item_id = parameter_check(request, name=request.GET.get('cangku_ck_item_id'))
                    request.session['cangku_check_item_id'] = ck_item_id
                elif request.GET.get('sell_id'):
                    check_sell_id = parameter_check(request, name=request.GET.get('sell_id'))
                    request.session['check_sell_id'] = check_sell_id
                ############### 此代码在战帝不需要使用###############
                # 镶入宝石
                # elif request.GET.get('set_gemstone_id'):
                #    u.set('jm_parameter', request.GET.get('set_gemstone_id'))
                #    set_gemstone_id = parameter_check(request, name=u.jm_parameter)
                #    sl = int(o.kong_count) + 1
                #    bh = 0
                #    for i in range(1, sl):
                #        if int(o.get('equip_{}_id'.format(i))) == 0:
                #            bh = i
                #            break
                #    if bh > 0:
                #        u.set('jm_parameter', request.GET.get('set_gemstone_ys_id'))
                #        set_gemstone_ys_id = parameter_check(request, name=u.jm_parameter)
                #        u.set('jm_parameter', request.GET.get('set_gemstone_bangding'))
                #        set_gemstone_bangding = parameter_check(request, name=u.jm_parameter)
                #        bs = GameObject(ItemPlayer.objects.get(id=set_gemstone_id))
                #        if Item.objects.filter(id=bs.item_id).count() > 0:
                #            xt_bs = GameObject(Item.objects.get(id=bs.item_id))
                #            bs.set('params', xt_bs.params)
                #            bs.save()
                #            o.set('equip_{}_id'.format(bh), set_gemstone_id)
                #            o.set('equip_{}_ys_id'.format(bh), set_gemstone_ys_id)
                #            o.set('equip_{}_bangding'.format(bh), set_gemstone_bangding)
                #            o.save()
                #            event_all = EventAll.objects.get(area_name='item', name='镶入宝石事件')
                #            if event_all.code != '':
                #                exec(code_zh_new('o', 'bs', event_all.code))  # 运行公共事件
                #            # 运行公共事件事件列表
                #            if EventList.objects.filter(event_all=event_all.id).count() == 0:
                #                pass
                #            else:
                #                eventlists = EventList.objects.filter(event_all=event_all.id).extra(
                #                    select={'num': 'position+0'})
                #                eventlists = eventlists.extra(order_by=["num"])
                #                for i in eventlists:
                #                    if i.display == 0 or i.display == '' or eval(
                #                            code_zh_new('o', 'bs', i.display)):  # 触发条件是否满足
                #                        if i.execute_display == 0 or i.execute_display == '' or eval(
                #                                code_zh_new('o', 'bs', i.execute_display)):  # 执行条件是否满足
                #                            exec(code_zh_new('o', 'bs', i.code))  # 执行代码
                #                            if i.content == 0 or i.content == '':
                #                                pass
                #                            else:
                #                                var = re.findall(r"{{(.*?)}}", code_zh_new('o', 'bs', i.content))
                #                                bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('o', 'bs', i.content))
                #                                neyong = bbb.format(*map(eval, var))
                #                                get_tishi(request, neyong)  # 执行条件满足提示
                #                        else:
                #                            pass
                #                    else:
                #                        if i.not_content == 0 or i.not_content == '':
                #                            pass
                #                        else:
                #                            var = re.findall(r"{{(.*?)}}", code_zh_new('o', 'bs', i.not_content))
                #                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('o', 'bs', i.not_content))
                #                            neyong = bbb.format(*map(eval, var))
                #                            get_tishi(request, neyong)  # 触发条件不满足提示
                #                        u.set('wap_next_zhixing', 0)
                #                        break  # 退出循环
                #            if int(bs.count) <= 1:
                ##                ItemPlayer.objects.get(id=bs.id).delete()
                #            else:
                #                bs.change('count', -1)
                #                bs.save()
                #            get_tishi(request, '镶嵌:{} 成功'.format(bs.name))
                #        else:
                #            get_tishi(request, '此宝石系统内已删除')
                #            Player.objects.filter(id=set_gemstone_id).delete()
                #    else:
                #        get_tishi(request, '镶嵌已达上限')
                # 卸下宝石
                # elif request.GET.get('remove_gemstone_id'):
                #    u.set('jm_parameter', request.GET.get('remove_gemstone_id'))
                #    remove_gemstone_id = parameter_check(request, name=u.jm_parameter)
                #    sl = int(o.kong_count) + 1
                #    bh = 0
                #    for i in range(1, sl):
                #        if int(o.get('equip_{}_ys_id'.format(i))) == int(remove_gemstone_id):
                #            bh = i
                #            break
                #    if bh > 0:
                #        if Item.objects.filter(id=o.get('equip_{}_ys_id'.format(bh))).count() > 0:
                #            bs = GameObject(Item.objects.get(id=o.get('equip_{}_ys_id'.format(bh))))
                #            event_all = EventAll.objects.get(area_name='item', name='卸下宝石事件')
                #            if event_all.code != '':
                #                exec(code_zh_new('o', 'bs', event_all.code))  # 运行公共事件
                #            # 运行公共事件事件列表
                #            if EventList.objects.filter(event_all=event_all.id).count() == 0:
                #                pass
                #            else:
                #                eventlists = EventList.objects.filter(event_all=event_all.id).extra(
                #                    select={'num': 'position+0'})
                #                eventlists = eventlists.extra(order_by=["num"])
                #                for i in eventlists:
                #                    if i.display == 0 or i.display == '' or eval(
                #                            code_zh_new('o', 'bs', i.display)):  # 触发条件是否满足
                #                        if i.execute_display == 0 or i.execute_display == '' or eval(
                #                                code_zh_new('o', 'bs', i.execute_display)):  # 执行条件是否满足
                #                            exec(code_zh_new('o', 'bs', i.code))  # 执行代码
                #                            if i.content == 0 or i.content == '':
                #                                pass
                #                            else:
                #                                var = re.findall(r"{{(.*?)}}", code_zh_new('o', 'bs', i.content))
                #                                bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('o', 'bs', i.content))
                #                                neyong = bbb.format(*map(eval, var))
                #                                get_tishi(request, neyong)  # 执行条件满足提示
                #                        else:
                #                            pass
                #                    else:
                #                        if i.not_content == 0 or i.not_content == '':
                #                            pass
                #                        else:
                #                            var = re.findall(r"{{(.*?)}}", code_zh_new('o', 'bs', i.not_content))
                #                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('o', 'bs', i.not_content))
                #                            neyong = bbb.format(*map(eval, var))
                #                            get_tishi(request, neyong)  # 触发条件不满足提示
                #                        u.set('wap_next_zhixing', 0)
                #                        break  # 退出循环
                #            get_items(u,  request, ut, g, c, t,e,remove_gemstone_id, 1, is_bangding=o.get('equip_{}_bangding'.format(bh)),
                #                      is_message=False)
                #            o.set('equip_{}_id'.format(bh), 0)
                #            o.set('equip_{}_bangding'.format(bh), 0)
                #            o.set('equip_{}_ys_id'.format(bh), 0)
                #            o.save()
                #            get_tishi(request, '镶嵌:{} 成功'.format(bs.name))
                #        else:
                #            get_tishi(request, '此宝石系统内已删除')
                #            Player.objects.filter(item_id=remove_gemstone_id).delete()
                #    else:
                #        get_tishi(request, '镶嵌已达上限')
                ############### 此代码在战帝不需要使用###############
                else:
                    pass
                if request.session['wap_page_name'] == '装备物品选择':
                    if request.GET.get('equip_area_name'):
                        request.session['equip_area_name'] = parameter_check(request,
                                                                             name=request.GET.get('equip_area_name'))
                    if request.GET.get('duixiang'):
                        request.session['duixiang'] = parameter_check(request, name=request.GET.get('duixiang'))
                    if request.GET.get('equip_bh'):
                        request.session['equip_bh'] = parameter_check(request, name=request.GET.get('equip_bh'))
                    if request.GET.get('equip_page'):
                        request.session['equip_page'] = parameter_check(request, name=request.GET.get('equip_page'))
                    if request.GET.get('use_up_page'):
                        request.session['use_up_page'] = parameter_check(request, name=request.GET.get('use_up_page'))
                    if request.GET.get('pets_up_page'):
                        request.session['pets_up_page'] = parameter_check(request, name=request.GET.get('pets_up_page'))
                    if request.GET.get('page_name'):
                        request.session['page_name'] = parameter_check(request, name=request.GET.get('page_name'))
        ############### 此代码在战帝不需要使用###############
        # 上下页，技能--------------------------------
        # elif request.GET.get('up_skill') or request.GET.get('next_skill'):
        #    if request.GET.get('up_skill'):
        #        u.set('jm_parameter', request.GET.get('up_skill'))
        #        if parameter_check(request, name=u.jm_parameter) == 'False':
        #            pass
        #        else:
        #            # 参数校验
        #            ck = parameter_check(request, name=u.jm_parameter)
        #            u.set('pets_page', ck)
        #    if request.GET.get('next_skill'):
        #        u.set('jm_parameter', request.GET.get('next_skill'))
        #        if parameter_check(request, name=u.jm_parameter) == 'False':
        #            pass
        #        else:
        #            # 参数校验
        #            ck = parameter_check(request, name=u.jm_parameter)
        #            u.set('pets_page', ck)
        ############### 此代码在战帝不需要使用###############
        # 上下页，宠物--------------------------------
        elif request.GET.get('up_pets') or request.GET.get('next_pets'):
            if request.GET.get('up_pets'):
                u.set('jm_parameter', request.GET.get('up_pets'))
                ck = parameter_check(request, name=u.jm_parameter)
                if ck == 'False':
                    pass
                else:
                    # 参数校验
                    request.session['page_yeshu'] = ck
            if request.GET.get('next_pets'):
                u.set('jm_parameter', request.GET.get('next_pets'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    request.session['page_yeshu'] = ck
        # 上下页，坐骑--------------------------------
        elif request.GET.get('up_mount') or request.GET.get('next_mount'):
            if request.GET.get('up_mount'):
                u.set('jm_parameter', request.GET.get('up_mount'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    u.set('mount_page', ck)
            if request.GET.get('next_mount'):
                u.set('jm_parameter', request.GET.get('next_mount'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    u.set('mount_page', ck)
        # 上下页，聊天--------------------------------
        elif request.GET.get('up_chat') or request.GET.get('next_chat'):
            if request.GET.get('up_chat'):
                u.set('jm_parameter', request.GET.get('up_chat'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    request.session['chat_page'] = ck
            if request.GET.get('next_chat'):
                u.set('jm_parameter', request.GET.get('next_chat'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    request.session['chat_page'] = ck
            if request.GET.get('chat_area_id'):
                u.set('jm_parameter', request.GET.get('chat_area_id'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    request.session['chat_area_id'] = ck
                    area_names = ChatAreaName.objects.all()
                    for i in area_names:
                        if int(ck) == 0:
                            request.session['chat_area_name'] = '全部'
                            break
                        elif int(i.id) == int(ck):
                            request.session['chat_area_name'] = i.area_name
                            break
                        else:
                            pass
        # 上下页，聊天场景里面--------------------------------
        elif request.GET.get('up_chat1') or request.GET.get('next_chat1'):
            if request.GET.get('chat_area_id1'):
                u.set('jm_parameter', request.GET.get('chat_area_id1'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=u.jm_parameter)
                    request.session['chat_area_id1'] = ck
                    area_names = ChatAreaName.objects.all()
                    request.session['chat_page'] = 0
                    """
                        for i in area_names:
                            if int(ck) == 0:
                                request.session['chat_area_name1'] = '全部'
                                break
                            elif int(i.id) == int(ck):
                                request.session['chat_area_name1'] = i.area_name
                                break
                            else:
                                pass
                        """
        # 上下页，背包--------------------------------
        elif request.GET.get('up_page') or request.GET.get('next_page'):
            if request.GET.get('up_page'):
                u.set('jm_parameter', request.GET.get('up_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('up_page'))
                    request.session['page_yeshu'] = ck
            if request.GET.get('next_page'):
                u.set('jm_parameter', request.GET.get('next_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('next_page'))
                    request.session['page_yeshu'] = ck
            if request.GET.get('page_area_id'):
                u.set('jm_parameter', request.GET.get('page_area_id'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    ck = parameter_check(request, name=request.GET.get('page_area_id'))
                    request.session['page_area_id'] = ck
                    area_names = ItemAreaName.objects.all()
                    for i in area_names:
                        if int(i.id) == int(ck):
                            request.session['page_area_name'] = i.area_name
                            break
                        elif int(ck) == 0:
                            request.session['page_area_name'] = '全部'
                            break
                        else:
                            pass
        # 物品搜索列表--------------------------------
        elif request.GET.get('item_page_yeshu'):
            if request.GET.get('item_page_yeshu'):
                u.set('jm_parameter', request.GET.get('item_page_yeshu'))
                ck = parameter_check(request, name=request.GET.get('item_page_yeshu'))
                if ck == 'False':
                    pass
                else:
                    # 参数校验
                    request.session['item_page_yeshu'] = ck
        # 地图事项------------------------------------
        elif request.GET.get('map_get_into') or request.GET.get('map_get_into_see'):  # 从后台地图进入场景/地图上移动场景
            if request.GET.get('map_get_into'):
                # u.set('jm_parameter', request.GET.get('map_get_into'))
                map_get_into = parameter_check(request, name=request.GET.get('map_get_into'))
                if map_get_into == 'False':
                    pass
                else:
                    # 参数校验
                    maps = GameMap.objects.get(id=map_get_into)
                    u.set('map_name', maps.name)
                    u.set('map_id', map_get_into)
                    request.session['wap_page_name'] = '场景'
                    u.save()
            if request.GET.get('map_get_into_see'):
                # u.set('jm_parameter', request.GET.get('map_get_into_see'))
                map_get_into = parameter_check(request, name=request.GET.get('map_get_into_see'))
                if map_get_into == 'False':
                    pass
                else:
                    # 参数校验
                    request.session['is_wap'] = 'True'
                    maps = GameMap.objects.get(id=map_get_into)
                    u.set('map_name', maps.name)
                    u.set('map_id', map_get_into)
                    request.session['wap_page_name'] = '场景'
                    u.save()
        # 电脑人物事项------------------------------------
        elif request.GET.get('npc_get_into'):  # 进入电脑人物模板 o是否有变更
            u.set('jm_parameter', request.GET.get('npc_get_into'))
            npc_id = parameter_check(request, name=request.GET.get('npc_id'))
            if npc_id == 'False':
                pass
            else:
                # 参数校验
                # if GameNpcs.objects.filter(id=npc_id).count() > 0:
                try:
                    request.session['npc_id'] = npc_id
                    request.session['wap_page_name'] = '电脑人物'
                    if int(o.id) != int(npc_id) or request.session['pmwap_ls_page_name'] != '电脑人物':
                        o = GameObject(GameNpcs.objects.get(id=request.session['npc_id']))  # 电脑人物模板
                    try:
                        event_all = EventAll.objects.get(id=c.get('{}_{}'.format('npc', '查看事件')))
                    except:
                        event_all = EventAll.objects.get(area_name='npc', name='查看事件')
                        c.set('{}_{}'.format('npc', '查看事件'), event_all.id)
                        c.save()
                    exec(event_all.code)
                    try:
                        event = Event.objects.get(id=c.get('npc_{}_{}'.format(o.ys_id, '查看事件')))
                    except:
                        event = Event.objects.get(npc_id=o.ys_id, name='查看事件')
                        c.set('npc_{}_{}'.format(o.ys_id, '查看事件'), event.id)
                        c.save()
                    exec(event.code)
                except:
                    get_tishi(request, '当前电脑人物已消失')
        # 进入接受任务事项------------------------------------
        elif request.GET.get('task_get_into'):  # 进入电脑人物模板
            u.set('jm_parameter', request.GET.get('task_get_into'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                task_id = parameter_check(request, name=request.GET.get('task_id'))
                task_page = parameter_check(request, name=request.GET.get('task_get_into'))
                request.session['check_task_id'] = task_id
                request.session['wap_page_name'] = task_page
                o = GameObject(Task.objects.get(id=request.session['check_task_id']))  # 任务模板
        # 完成任务
        elif request.GET.get('task_id_submit_into'):  # 进入电脑人物模板
            u.set('jm_parameter', request.GET.get('task_id_submit_into'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                task_id = parameter_check(request, name=request.GET.get('task_id_submit_into'))
                request.session['check_task_id'] = task_id
                task = Task.objects.get(id=task_id)
                if int(task.type) == 0:
                    get_tishi(request, '办事任务无法直接提交')
                else:
                    task_items = TaskItem.objects.filter(task_id=task_id)
                    wap_task_count = 0
                    for task_item in task_items:
                        if task_item.npc_id == 0 or task_item.npc_id == '0':
                            pass
                        else:
                            if TaskItemPlayer.objects.filter(player_id=wap_u_id, task_id=task_id,
                                                             npc_id=task_item.npc_id).count() == 0:
                                wap_task_count = 1
                            else:
                                taskitem_player = TaskItemPlayer.objects.get(player_id=wap_u_id, task_id=task_id,
                                                                             npc_id=task_item.npc_id)
                                if int(taskitem_player.npc_count) >= int(task_item.npc_count):
                                    pass
                                else:
                                    wap_task_count = 1
                        if task_item.item_id == 0 or task_item.item_id == '0':
                            pass
                        else:
                            if ice(u, task_item.item_id) >= int(task_item.item_count):
                                pass
                            else:
                                wap_task_count = 1
                    if wap_task_count == 0:
                        task_player = TaskPlayer.objects.get(player_id=wap_u_id, task_id=task_id)
                        task_player.zhuangtai = 2
                        task_player.save()
                        get_tishi(request, '任务提交成功')
                        o = GameNpc.objects.get(id=task.npc_id)
                        exec(task.submit_code)  # 完成任务代码
                        taskitem_players = TaskItemPlayer.objects.filter(player_id=wap_u_id, task_id=task_id)
                        for taskitem_player in taskitem_players:
                            if taskitem_player.npc_id != 0 or taskitem_player.npc_id != '0':
                                taskitem_player.delete()
                        taskitem_players = TaskItem.objects.filter(task_id=task_id)
                        for taskitem_player in taskitem_players:
                            if taskitem_player.item_id != 0 or taskitem_player.item_id != '0':
                                delete_items(u, request, ut, g, c, t, e,taskitem_player.item_id, int(taskitem_player.item_count),
                                             is_message='True')
                    else:
                        get_tishi(request, '任务未完成')
            request.session['wap_page_name'] = '电脑人物' if request.session['wap_page_name'] == '任务' else '任务列表'
        # PK反击------------------------------------
        elif request.GET.get('in_attack_player'):  # 进入PK模板
            u.set('jm_parameter', request.GET.get('in_attack_player'))
            if parameter_check(request, name=u.jm_parameter) == 'False':
                pass
            else:
                # 参数校验
                player_id = parameter_check(request, name=request.GET.get('in_attack_player'))
                o = GameObject(Player.objects.get(id=player_id))  # 被攻击的玩家
                if int(o.hp) > 0:
                    if o.map_id == u.map_id and u.map_id != 1010 and o.map_id !=1010:
                        u.set('is_pk', 1)
                        u.set('pk_npc_id', 0)
                        u.set('pk_player_id', o.id)
                        request.session['wap_page_name'] = '战斗'
                        u.save()
                    else:
                        if o.map_id != u.map_id:
                            get_tishi(request, '对方已走远')
                        else:
                            get_tishi(request, '你或者对方在监狱，无法进行攻击')
                else:
                    get_tishi(request, '对方已战亡')
                AttackInfo.objects.filter(player_id=u.id, attack_player_id=o.id).delete()  # 删除被攻击信息
                Monster.objects.filter(player_id=u.id, zhuangtai=1).update(zhuangtai=0)  # 清除怪物信息
        # 穿上装备及使用物品
        elif request.GET.get('use_equip') or request.GET.get('use_item') or request.GET.get('remove_bh'):
            if request.GET.get('use_equip'):
                u.set('jm_parameter', request.GET.get('use_equip'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    # 检测身上装备并且卸下
                    o = GameObject(ItemPlayer.objects.get(id=item_id))
                    if int(o.lvl) <= int(u.lvl):
                        if UseEquip.objects.filter(player_id=wap_u_id, type_id=o.type_id).count() == 0:
                            pass
                        else:
                            # 装备中物品ID
                            use_item = UseEquip.objects.get(player_id=wap_u_id, type_id=o.type_id)
                            # 获得装备中物品的信息
                            o = GameObject(ItemPlayer.objects.get(id=use_item.item_id))
                            if EventAll.objects.filter(area_name='item', name='卸下装备事件').count() > 0:
                                run_event_all(u, o, request, ut, g, c, t, e, 'item', '卸下装备事件')
                            if Event.objects.filter(item_id=o.item_id, name='卸下事件').count() > 0:
                                run_event_item(u, o, request, ut, g, c, t, e, o.item_id, '卸下事件')
                            o.set('player_id', wap_u_id)
                            o.save()
                            get_tishi(rquest, '卸下:{}成功1'.format(o.name))
                            use_item.delete()
                        o = GameObject(ItemPlayer.objects.get(id=item_id))
                        if EventAll.objects.filter(area_name='item', name='穿上装备事件').count() > 0:
                            run_event_all(u, o, request, ut, g, c, t, e, 'item', '穿上装备事件')
                        if u.wap_next_zhixing == 1 and Event.objects.filter(item_id=o.item_id,
                                                                            name='穿上事件').count() > 0:
                            run_event_item(u, o, request, ut, g, c, t, e, o.item_id, '穿上事件')
                        if u.wap_next_zhixing == 1:
                            # 穿上装备
                            use_item = UseEquip.objects.create(player_id=wap_u_id, type_id=o.type_id, item_id=o.id,
                                                               tz_biaoshi=o.tz_biaoshi)
                            o.set('player_id', 0)
                            o.save()
                            get_tishi(rquest, '穿上:{}成功1'.format(o.name))
                        else:
                            pass
                        u.set('jm_parameter', request.GET.get('use_up_page'))
                        page_name = parameter_check(request, name=u.jm_parameter)
                        request.session['wap_page_name'] = page_name
                    else:
                        get_tishi(request, '等级不足{}级，无法穿戴'.format(o.lvl))
            if request.GET.get('use_item'):
                u.set('jm_parameter', request.GET.get('use_item'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    o = GameObject(ItemPlayer.objects.get(id=item_id))
                    if int(o.lvl) <= int(u.lvl):
                        event = Event.objects.get(item_id=o.item_id, name='使用事件')
                        if event.code == '' and EventList.objects.filter(
                                event=event.id).count() == 0 or event.code == ' ' and EventList.objects.filter(
                            event=event.id).count() == 0:
                            u.set('message', '此物品不可直接使用')
                        else:
                            if EventAll.objects.filter(area_name='item', name='使用事件').count() > 0:
                                run_event_all(u, o, request, ut, g, c, t, e, 'item', '使用事件')
                            if Event.objects.filter(item_id=o.item_id, name='使用事件').count() > 0:
                                run_event_item(u, o, request, ut, g, c, t, e, o.item_id, '使用事件')
                            if int(o.count) > 0:
                                pass
                            else:
                                if request.session['wap_page_name'] == '战斗':
                                    pass
                                else:
                                    request.session['wap_page_name'] = '背包'
                                o = ItemPlayer.objects.get(id=item_id)
                                o.delete()
                    else:
                        get_tishi(request, '等级不足{}级，无法使用该物品'.format(o.lvl))
            if request.GET.get('remove_bh'):
                u.set('jm_parameter', request.GET.get('remove_bh'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    # 检测身上装备并且卸下
                    iid = UseEquip.objects.get(player_id=wap_u_id, type_id=item_id)
                    o = GameObject(ItemPlayer.objects.get(id=iid.item_id))
                    if UseEquip.objects.filter(player_id=wap_u_id, type_id=o.type_id).count() == 0:
                        pass
                    else:
                        # 装备中物品ID
                        use_item = UseEquip.objects.get(player_id=wap_u_id, type_id=o.type_id)
                        # 获得装备中物品的信息
                        o = GameObject(ItemPlayer.objects.get(id=use_item.item_id))
                        run_event_all(u, o, request, ut, g, c, t, e, 'item', '卸下装备事件')
                        run_event_item(u, o, request, ut, g, c, t, e, o.item_id, '卸下事件')
                        o.set('player_id', wap_u_id)
                        o.save()
                        get_tishi(request, '卸下:{}成功'.format(o.name))
                        use_item.delete()
        # 宠物穿上装备及使用物品
        elif request.GET.get('pets_equip') or request.GET.get('pets_remove_bh'):
            pets = GameObject(Pets.objects.get(id=u.check_pets_id))
            if request.GET.get('pets_equip'):
                u.set('jm_parameter', request.GET.get('pets_equip'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    # 检测身上装备并且卸下
                    o = GameObject(ItemPlayer.objects.get(id=item_id))
                    if int(o.lvl) <= int(pets.lvl):
                        if UseEquip.objects.filter(pets_id=pets.id, type_id=o.type_id).count() == 0:
                            pass
                        else:
                            # 装备中物品ID
                            use_item = UseEquip.objects.get(pets_id=pets.id, type_id=o.type_id)
                            # 获得装备中物品的信息
                            o = GameObject(ItemPlayer.objects.get(id=use_item.item_id))
                            if EventAll.objects.filter(area_name='item', name='卸下装备事件').count() > 0:
                                u.set('wap_next_zhixing', 1)
                                event_all = EventAll.objects.get(area_name='item', name='卸下装备事件')
                                if event_all.code != '':
                                    exec(code_zh_new('pets', 'o', event_all.code))  # 运行公共事件
                                # 运行公共事件事件列表
                                if EventList.objects.filter(event_all=event_all.id).count() == 0:
                                    pass
                                else:
                                    eventlists = EventList.objects.filter(event_all=event_all.id).extra(
                                        select={'num': 'position+0'})
                                    eventlists = eventlists.extra(order_by=["num"])
                                    for i in eventlists:
                                        if i.display == 0 or i.display == '' or eval(
                                                code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                            if i.execute_display == 0 or i.execute_display == '' or eval(
                                                    code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                                exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                                if i.content == 0 or i.content == '':
                                                    pass
                                                else:
                                                    var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                                    bbb = re.sub(r"{{(.*?)}}", "{}",
                                                                 code_zh_new('pets', 'o', i.content))
                                                    neyong = bbb.format(*map(eval, var))
                                                    get_tishi(request, neyong)  # 执行条件满足提示
                                            else:
                                                pass
                                        else:
                                            if i.not_content == 0 or i.not_content == '':
                                                pass
                                            else:
                                                var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                                bbb = re.sub(r"{{(.*?)}}", "{}",
                                                             code_zh_new('pets', 'o', i.not_content))
                                                neyong = bbb.format(*map(eval, var))
                                                get_tishi(request, neyong)  # 触发条件不满足提示
                                            u.set('wap_next_zhixing', 0)
                                            break  # 退出循环
                            if Event.objects.filter(item_id=o.item_id, name='卸下事件').count() > 0:
                                u.set('wap_next_zhixing', 1)
                                event = Event.objects.get(item_id=o.item_id, name='卸下事件')
                                if event.code != '':
                                    exec(code_zh_new('pets', 'o', event.code))  # 运行公共事件
                                # 运行公共事件事件列表
                                if EventList.objects.filter(event=event.id).count() == 0:
                                    pass
                                else:
                                    eventlists = EventList.objects.filter(event=event.id).extra(
                                        select={'num': 'position+0'})
                                    eventlists = eventlists.extra(order_by=["num"])
                                    for i in eventlists:
                                        if i.display == 0 or i.display == '' or eval(
                                                code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                            if i.execute_display == 0 or i.execute_display == '' or eval(
                                                    code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                                exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                                if i.content == 0 or i.content == '':
                                                    pass
                                                else:
                                                    var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                                    bbb = re.sub(r"{{(.*?)}}", "{}",
                                                                 code_zh_new('pets', 'o', i.content))
                                                    neyong = bbb.format(*map(eval, var))
                                                    get_tishi(request, neyong)  # 执行条件满足提示
                                            else:
                                                pass
                                        else:
                                            if i.not_content == 0 or i.not_content == '':
                                                pass
                                            else:
                                                var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                                bbb = re.sub(r"{{(.*?)}}", "{}",
                                                             code_zh_new('pets', 'o', i.not_content))
                                                neyong = bbb.format(*map(eval, var))
                                                get_tishi(request, neyong)  # 触发条件不满足提示
                                            u.set('wap_next_zhixing', 0)
                                            break  # 退出循环
                            o.set('pets_id', pets.id)
                            o.set('player_id', 0)
                            o.save()
                            u.set('message', '{}卸下:{}成功1<br/>'.format(u.message, o.name))
                            use_item.delete()
                        o = GameObject(ItemPlayer.objects.get(id=item_id))
                        if EventAll.objects.filter(area_name='item', name='穿上装备事件').count() > 0:
                            u.set('wap_next_zhixing', 1)
                            event_all = EventAll.objects.get(area_name='item', name='穿上装备事件')
                            if event_all.code != '':
                                exec(code_zh_new('pets', 'o', event_all.code))  # 运行公共事件
                            # 运行公共事件事件列表
                            if EventList.objects.filter(event_all=event_all.id).count() == 0:
                                pass
                            else:
                                eventlists = EventList.objects.filter(event_all=event_all.id).extra(
                                    select={'num': 'position+0'})
                                eventlists = eventlists.extra(order_by=["num"])
                                for i in eventlists:
                                    if i.display == 0 or i.display == '' or eval(
                                            code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                        if i.execute_display == 0 or i.execute_display == '' or eval(
                                                code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                            exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                            if i.content == 0 or i.content == '':
                                                pass
                                            else:
                                                var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                                bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.content))
                                                neyong = bbb.format(*map(eval, var))
                                                get_tishi(request, neyong)  # 执行条件满足提示
                                        else:
                                            pass
                                    else:
                                        if i.not_content == 0 or i.not_content == '':
                                            pass
                                        else:
                                            var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.not_content))
                                            neyong = bbb.format(*map(eval, var))
                                            get_tishi(request, neyong)  # 触发条件不满足提示
                                        u.set('wap_next_zhixing', 0)
                                        break  # 退出循环
                        if u.wap_next_zhixing == 1 and Event.objects.filter(item_id=o.item_id,
                                                                            name='穿上事件').count() > 0:
                            u.set('wap_next_zhixing', 1)
                            event = Event.objects.get(item_id=o.item_id, name='穿上事件')
                            if event.code != '':
                                exec(code_zh_new('pets', 'o', event.code))  # 运行公共事件
                            # 运行公共事件事件列表
                            if EventList.objects.filter(event=event.id).count() == 0:
                                pass
                            else:
                                eventlists = EventList.objects.filter(event=event.id).extra(
                                    select={'num': 'position+0'})
                                eventlists = eventlists.extra(order_by=["num"])
                                for i in eventlists:
                                    if i.display == 0 or i.display == '' or eval(
                                            code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                        if i.execute_display == 0 or i.execute_display == '' or eval(
                                                code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                            exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                            if i.content == 0 or i.content == '':
                                                pass
                                            else:
                                                var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                                bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.content))
                                                neyong = bbb.format(*map(eval, var))
                                                get_tishi(request, neyong)  # 执行条件满足提示
                                        else:
                                            pass
                                    else:
                                        if i.not_content == 0 or i.not_content == '':
                                            pass
                                        else:
                                            var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.not_content))
                                            neyong = bbb.format(*map(eval, var))
                                            get_tishi(request, neyong)  # 触发条件不满足提示
                                        u.set('wap_next_zhixing', 0)
                                        break  # 退出循环
                        if u.wap_next_zhixing == 1:
                            # 穿上装备
                            use_item = UseEquip.objects.create(pets_id=pets.id, type_id=o.type_id, item_id=o.id)
                            o.set('pets_id', 0)
                            o.set('player_id', 0)
                            o.save()
                            u.set('message', '{}穿上:{}成功1<br/>'.format(u.message, o.name))
                        else:
                            pass
                        u.set('jm_parameter', request.GET.get('pets_up_page'))
                        page_name = parameter_check(request, name=u.jm_parameter)
                        request.session['wap_page_name'] = page_name
                    else:
                        get_tishi(request, '等级不足{}级，无法穿戴'.format(o.lvl))
            if request.GET.get('pets_remove_bh'):
                u.set('jm_parameter', request.GET.get('pets_remove_bh'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    # 检测身上装备并且卸下
                    iid = UseEquip.objects.get(pets_id=pets.id, type_id=item_id)
                    o = GameObject(ItemPlayer.objects.get(id=iid.item_id))
                    if UseEquip.objects.filter(pets_id=pets.id, type_id=o.type_id).count() == 0:
                        pass
                    else:
                        # 装备中物品ID
                        use_item = UseEquip.objects.get(pets_id=pets.id, type_id=o.type_id)
                        # 获得装备中物品的信息
                        o = GameObject(ItemPlayer.objects.get(id=use_item.item_id))
                        u.set('wap_next_zhixing', 1)
                        event_all = EventAll.objects.get(area_name='item', name='卸下装备事件')
                        if event_all.code != '':
                            exec(code_zh_new('pets', 'o', event_all.code))  # 运行公共事件
                        # 运行公共事件事件列表
                        if EventList.objects.filter(event_all=event_all.id).count() == 0:
                            pass
                        else:
                            eventlists = EventList.objects.filter(event_all=event_all.id).extra(
                                select={'num': 'position+0'})
                            eventlists = eventlists.extra(order_by=["num"])
                            for i in eventlists:
                                if i.display == 0 or i.display == '' or eval(
                                        code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                    if i.execute_display == 0 or i.execute_display == '' or eval(
                                            code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                        exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                        if i.content == 0 or i.content == '':
                                            pass
                                        else:
                                            var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.content))
                                            neyong = bbb.format(*map(eval, var))
                                            get_tishi(request, neyong)  # 执行条件满足提示
                                    else:
                                        pass
                                else:
                                    if i.not_content == 0 or i.not_content == '':
                                        pass
                                    else:
                                        var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                        bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.not_content))
                                        neyong = bbb.format(*map(eval, var))
                                        get_tishi(request, neyong)  # 触发条件不满足提示
                                    u.set('wap_next_zhixing', 0)
                                    break  # 退出循环
                        u.set('wap_next_zhixing', 1)
                        event = Event.objects.get(item_id=o.item_id, name='卸下事件')
                        if event.code != '':
                            exec(code_zh_new('pets', 'o', event.code))  # 运行公共事件
                        # 运行公共事件事件列表
                        if EventList.objects.filter(event=event.id).count() == 0:
                            pass
                        else:
                            eventlists = EventList.objects.filter(event=event.id).extra(
                                select={'num': 'position+0'})
                            eventlists = eventlists.extra(order_by=["num"])
                            for i in eventlists:
                                if i.display == 0 or i.display == '' or eval(
                                        code_zh_new('pets', 'o', i.display)):  # 触发条件是否满足
                                    if i.execute_display == 0 or i.execute_display == '' or eval(
                                            code_zh_new('pets', 'o', i.execute_display)):  # 执行条件是否满足
                                        exec(code_zh_new('pets', 'o', i.code))  # 执行代码
                                        if i.content == 0 or i.content == '':
                                            pass
                                        else:
                                            var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.content))
                                            bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.content))
                                            neyong = bbb.format(*map(eval, var))
                                            get_tishi(request, neyong)  # 执行条件满足提示
                                    else:
                                        pass
                                else:
                                    if i.not_content == 0 or i.not_content == '':
                                        pass
                                    else:
                                        var = re.findall(r"{{(.*?)}}", code_zh_new('pets', 'o', i.not_content))
                                        bbb = re.sub(r"{{(.*?)}}", "{}", code_zh_new('pets', 'o', i.not_content))
                                        neyong = bbb.format(*map(eval, var))
                                        get_tishi(request, neyong)  # 触发条件不满足提示
                                    u.set('wap_next_zhixing', 0)
                                    break  # 退出循环
                        o.set('player_id', u.id)
                        o.set('pets_id', 0)
                        o.save()
                        get_tishi(request, '卸下:{}成功'.format(o.name))
                        use_item.delete()
        # 更新地图
        elif request.GET.get('map_update'):
            u.set('map_id', request.session['object_id'])
            request.session['wap_page_name'] = '场景'
            u.set('jm_parameter', 0)
            o = GameObject(GameMap.objects.get(id=u.map_id))
            u.set('message', '{}更新地图成功<br/>'.format(u.message))
            u.save()
        # 查看装备中物品
        elif request.GET.get('ck_use_equip_id'):
            if request.GET.get('ck_use_equip_id'):
                u.set('jm_parameter', request.GET.get('ck_use_equip_id'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_id = parameter_check(request, name=u.jm_parameter)
                    request.session['item_id'] = item_id
                    request.session['wap_page_name'] = '查看物品'
            if request.GET.get('use_up_page'):
                u.set('jm_parameter', request.GET.get('use_up_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_page = parameter_check(request, name=u.jm_parameter)
                    request.session['use_up_page'] = item_page
        # 查看装备中装备，返回指定页面
        elif request.GET.get('ck_use_equip_up_page'):
            if request.GET.get('ck_use_equip_up_page'):
                u.set('jm_parameter', request.GET.get('ck_use_equip_up_page'))
                if parameter_check(request, name=u.jm_parameter) == 'False':
                    pass
                else:
                    # 参数校验
                    item_page = parameter_check(request, name=u.jm_parameter)
                    request.session['wap_page_name'] = item_page
        # 攻击技能参数，检测当前链接是否安全
        elif request.GET.get('pk_info'):
            #if Monster.objects.filter(player_id=u.id, zhuangtai=1).count() > 0:
            #    o = Monster.objects.filter(player_id=u.id, zhuangtai=1)[0:1]
            #    for i in o:
            #        o = GameObject(i)
            #if int(u.pk_player_id) != 0:
            #    if Player.objects.filter(id=u.pk_player_id).count() > 0:
            #        o = GameObject(Player.objects.get(id=u.pk_player_id))
            if request.GET.get('pk_info'):
                u.set('jm_parameter', request.GET.get('pk_info'))
        elif request.GET.get('sosuo'):
            u.set('jm_parameter', request.GET.get('sosuo'))
        else:
            pass
        starttime2 = time.time()
        if True:#int(u.pk_time) + 1800 > c.time:
            #if request.session['wap_page_name'] != '物品搜索查看' and request.session['wap_page_name'] != '状态' and request.session['wap_page_name'] != '战斗' and request.session['wap_page_name'] != '物品搜索列表' and request.session['wap_page_name'] != '查看玩家' and request.session['wap_page_name'] != '背包' and request.session['wap_page_name'] != '查看物品':
            if True:
                attackinfo = AttackInfo.objects.filter(player_id=u.id)
                for i in attackinfo:
                    get_tishi(request, '被攻击，ID：{}={}, u.hp={}'.format(i.attack_player_id, u.pk_player_id,u.hp))
                    player = GameObject(Player.objects.get(id=i.attack_player_id))
                    if int(i.time) + 1800 > c.time  and u.hp > 0 and player.hp > 0 or u.pk_player_id == 0 and u.hp > 0 and player.hp > 0:
                        get_tishi(request, '被攻击1')
                        request.session['wap_page_name'] = '战斗'
                        u.set('pk_player_id', i.attack_player_id)
                        u.set('pk_npc_id', 0)
                        u.set('pk_time', c.time)
                        u.set('pk_player_name', player.name)
                        u.save()
                        i.delete()
                        break
                    elif str(i.attack_player_id) == str(u.pk_player_id) and u.hp > 0 and player.hp > 0:
                        get_tishi(request, '被攻击2')
                        break
                    elif str(i.attack_player_id) != str(u.pk_player_id) and u.hp > 0 and player.hp > 0:
                        i.delete()
                        u.set('pk_player_id', 0)
                        u.save()
                        get_tishi(request, '被攻击3')
                    elif u.hp <= 0 or player.hp <=0 :
                        i.delete()
                        get_tishi(request, '被攻击4,一方血量检测为0，PK进入失败')
        #get_tishi(request, '前置事件前行{}'.format(u.map_id))
        # 前置事件
        try:
            event_all = EventAll.objects.get(id=c.get('{}_{}'.format('all', '前置事件')))
        except:
            event_all = EventAll.objects.get(area_name='all', name='前置事件')
            c.set('{}_{}'.format('all', '前置事件'), event_all.id)
            c.save()
        exec(event_all.code)  # 运行公共事件
        #get_tishi(request, '前置事件执行后{}'.format(u.map_id))
        # 战斗模板PK
        if request.session['wap_page_name'] == '战斗':  # 未优化
            zx = 0
            try:
                if int(u.pk_npc_id) > 0:
                    o = GameObject(GameNpcs.objects.get(id=u.pk_npc_id))
                    zx = 1
                elif int(u.pk_player_id) > 0:
                    o = GameObject(Player.objects.get(id=u.pk_player_id))
                    zx = 1
                else:
                    o = Monster.objects.filter(player_id=u.id, zhuangtai=1)[0:1]
                    for i in o:
                        o = GameObject(i)
                        zx = 1
            except:
                get_tishi(request, '怪物已消失')
                request.session['wap_page_name'] = '场景'
            if zx == 1:
                if request.GET.get('pk_info'):
                    m = GameObject(Skills.objects.get(id=request.GET.get('pk_player_skill_id')))
                    u.set('hurt_hp', 0)
                    o.set('hurt_hp', 0)
                    u.set('hurt_mp', 0)
                    o.set('hurt_mp', 0)
                    u.set('wap_next_zhixing', 1)
                    u.set('chuzhang_pets_skill_name', ' ') # 宠物状态重置
                    o.set('chuzhang_pets_skill_name', ' ') # 宠物状态重置
                    if o.hp > 0:
                        #event_all = EventAll.objects.get(area_name='pk', name='伤害计算')
                        #if event_all.code != '':
                        #    exec(event_all.code)  # 运行公共事件
                        event_all = EventAll.objects.get(area_name='pk', name='攻击执行')
                        exec(event_all.code)  # 运行公共事件
                        if True:
                            # 反击，对方体力存在时，对方进行反击,反转进行#exists()
                            counts = Monster.objects.filter(player_id=u.id, zhuangtai=1).count()
                            pk_counts = counts + 1 if counts > 0 else 1 + 1
                            # 攻击次数
                            for pk_count in range(1, pk_counts):
                                # 计算当前是怪物出手还是人出手
                                if int(u.pk_player_id) > 0:
                                    o = GameObject(Player.objects.get(id=u.pk_player_id))
                                elif int(u.pk_npc_id) > 0:
                                    o = GameObject(GameNpcs.objects.get(id=u.pk_npc_id))
                                else:
                                    start = pk_count - 1
                                    monsters = Monster.objects.filter(player_id=u.id, zhuangtai=1)[start:pk_count]
                                    monsters = monsters.iterator()
                                    monsters = dict.fromkeys(monsters)
                                    for o in monsters:
                                        o = GameObject(o)
                                if int(o.hp) > 0:
                                    if int(u.pk_player_id) > 0:
                                        m = GameObject(Skill.objects.get(id=c.skill_id))
                                    else:
                                        if GameMapPlaceSkill.objects.filter(npc_id=o.ys_id).count() == 0:
                                            m = GameObject(Skill.objects.get(id=c.skill_id))
                                        else:
                                            wap_count = GameMapPlaceSkill.objects.filter(npc_id=o.ys_id).count()
                                            wap_count = random.randint(0, wap_count)
                                            bh = 0
                                            wap_skills = GameMapPlaceSkill.objects.filter(npc_id=o.ys_id)
                                            wap_skills = wap_skills.iterator()
                                            wap_skills = dict.fromkeys(wap_skills)
                                            for wap_skill in wap_skills:
                                                if bh == wap_count:
                                                    if bh == 0:
                                                        m = GameObject(Skill.objects.get(id=c.skill_id))
                                                    else:
                                                        m = GameObject(Skill.objects.get(id=wap_skill.skill_id))
                                                    break
                                                else:
                                                    pass
                                                bh = bh + 1
                                    #event_all = EventAll.objects.get(area_name='pk', name='伤害计算')
                                    #if event_all.code != '':
                                    #    exec(code_zh(event_all.code))  # 运行公共事件
                                    event_all = EventAll.objects.get(area_name='pk', name='攻击执行')
                                    exec(code_zh(event_all.code))  # 运行公共事件
                                else:
                                    break
                            if int(o.hp) <= 0 and int(u.pk_player_id) == 0:
                                o.set('zhuangtai', 0)
                                o.set('sw_time', int(time.time()))
                                o.save()
                                # 掉落经验，优先怪物掉落，再游戏掉落
                                if o.exp_expression != '' and o.exp_expression != 0:
                                    wap_a = eval(str(o.exp_expression)) *(u.jinyan_bs if u.jinyan_time > c.time else 1) * (1+u.zb_exp_bfb/100)
                                    u.change('exp', int(wap_a))
                                    get_tishi(request, '经验+{}'.format(int(wap_a)))
                                elif c.exp_expression != '' and c.exp_expression != 0:
                                    wap_a = eval(str(c.exp_expression)) *(u.jinyan_bs if u.jinyan_time > c.time else 1) * (1+u.zb_exp_bfb/100)
                                    u.change('exp', int(wap_a))
                                    get_tishi(request, '经验+{}'.format(int(wap_a)))
                                else:
                                    pass
                                # 掉落经验，优先怪物掉落，再游戏掉落
                                if o.money_expression != '' and o.money_expression != 0:
                                    wap_a = eval(str(o.money_expression)) *(u.money_bs if u.money_time > c.time else 1)
                                    u.change('money', int(wap_a))
                                    get_tishi(request, '{}+{}'.format(c.money_name, xy_numreplace3(int(wap_a))))
                                elif c.money_expression != '' and c.money_expression != 0:
                                    wap_a = eval(str(c.money_expression)) *(u.money_bs if u.money_time > c.time else 1)
                                    u.change('money', int(wap_a))
                                    get_tishi(request, '{}+{}'.format(c.money_name, xy_numreplace3(int(wap_a))))
                                else:
                                    pass
                                # 掉落灵气，优先怪物掉落，再游戏掉落
                                if o.lingqi_expression != '' and o.lingqi_expression != 0:
                                    wap_a = eval(str(o.lingqi_expression))
                                    u.change('lingqi', int(wap_a))
                                    get_tishi(request, '灵气+{}'.format(int(wap_a)))
                                elif c.lingqi_expression != '' and c.lingqi_expression != 0:
                                    wap_a = eval(str(c.lingqi_expression))
                                    u.change('lingqi', int(wap_a))
                                    get_tishi(request, '灵气+{}'.format(int(wap_a)))
                                else:
                                    pass
                                if o.ys_id > 0:
                                    run_event_all(u, o, request, ut, g, c, t, e, 'npc', '战败事件')
                                    run_event_npc(u, o, request, ut, g, c, t, e, o.ys_id, '战败事件')
                                run_event_all(u, o, request, ut, g, c, t, e, 'player', '战胜事件')
                                run_event_all(u, o, request, ut, g, c, t, e, 'player', '升级事件')
                                # 任务杀怪检测
                                task_players = TaskPlayer.objects.filter(player_id=u.id)
                                task_players = task_players.iterator()
                                task_players = dict.fromkeys(task_players)
                                for task_player in task_players:
                                    if int(task_player.zhuangtai) == 1:
                                        try:
                                            task = TaskItem.objects.get(task_id=task_player.task_id,
                                                                        npc_id=o.ys_id)
                                            try:
                                                taskitem_player = TaskItemPlayer.objects.get(player_id=u.id,
                                                                                             npc_id=o.ys_id,
                                                                                             task_id=task.task_id)
                                                if int(taskitem_player.npc_count) >= int(task.npc_count):
                                                    get_tishi(request,
                                                              '完成任务:{}({}/{})'.format(task.npc_name,
                                                                                          taskitem_player.npc_count,
                                                                                          task.npc_count))
                                                else:
                                                    taskitem_player.npc_count = int(
                                                        taskitem_player.npc_count) + 1
                                                    taskitem_player.save()
                                                    get_tishi(request, '任务:{}({}/{})'.format(task.npc_name,
                                                                                               taskitem_player.npc_count,
                                                                                               task.npc_count))
                                            except:
                                                taskitem_player = TaskItemPlayer.objects.create(player_id=u.id,
                                                                                                npc_id=o.ys_id,
                                                                                                task_id=task.task_id,
                                                                                                npc_count=1)
                                                get_tishi(request,
                                                          '任务:{}({}/{})'.format(task.npc_name,
                                                                                  taskitem_player.npc_count,
                                                                                  task.npc_count))
                                        except:
                                            pass
                                # 怪物身上有掉落物品时
                                npc_items = GameMapPlaceItem.objects.filter(npc_id=o.ys_id)  # 怪物掉落物品
                                npc_items = npc_items.iterator()
                                npc_items = dict.fromkeys(npc_items)
                                for npc_item in npc_items:
                                    wap_a = eval(npc_item.item_code)
                                    if wap_a > 0:
                                        if int(o.is_drop) == 0:
                                            get_items(u, request, ut, g, c, t, e, npc_item.item_id, wap_a,
                                                      is_bangding=False,
                                                      is_message=True,area_bangding='其它',area_bangding1='材料')
                                            # 任务物品检测
                                            task_players = TaskPlayer.objects.filter(player_id=u.id)
                                            task_players = task_players.iterator()
                                            task_players = dict.fromkeys(task_players)
                                            for task_player in task_players:
                                                if int(task_player.zhuangtai) == 1:
                                                    try:
                                                        item_player = TaskItem.objects.get(
                                                            task_id=task_player.task_id,
                                                            item_id=npc_item.item_id)
                                                        task = Task.objects.get(id=item_player.task_id)
                                                        if ice(u, int(npc_item.item_id)) >= int(
                                                                item_player.item_count):
                                                            get_tishi(request,
                                                                      '完成任务:{}({}{}/{})'.format(
                                                                          task.name,
                                                                          item_player.item_name,
                                                                          ice(u,
                                                                              npc_item.item_id),
                                                                          item_player.item_count))
                                                        else:
                                                            get_tishi(request,
                                                                      '任务:{}({}{}/{})'.format(task.name,
                                                                                                item_player.item_name,
                                                                                                ice(u,
                                                                                                    npc_item.item_id),
                                                                                                item_player.item_count))
                                                    except:
                                                        pass
                                        else:  # 掉落到地上
                                            xt_items = Item.objects.get(id=npc_item.item_id)
                                            if '装备' not in xt_items.area_name:
                                                item = GameObject(ItemMap.objects.create())  # 场景物品
                                                item.set('params', xt_items.params)
                                                item.set('name', xt_items.name)
                                                item.set('desc', xt_items.desc)
                                                item.set('bangding', xt_items.bangding)
                                                item.set('map_id', u.map_id)
                                                item.set('area_id', u.area_id)
                                                item.set('area_name', xt_items.area_name)
                                                item.set('count', wap_a)
                                                item.set('player_id', u.id)
                                                item.set('drop_time', int(time.time()) + 60)
                                                item.set('type', xt_items.type)
                                                item.set('type_id', xt_items.type_id)
                                                item.set('item_id', xt_items.id)
                                                item.set('time', xt_items.time)
                                                item.set('update', 1)
                                                item.set('biaoshi', xt_items.biaoshi)
                                                item.set('duixiang', xt_items.duixiang)
                                                run_event_all(u, item, request, ut, g, c, t, e, 'item',
                                                              '创建事件')
                                                run_event_item(u, item, request, ut, g, c, t, e, xt_items.id,
                                                               '创建事件')
                                                item.save()
                                                get_tishi(request,
                                                          '掉落地上:{}*{}'.format(item.name, item.count))
                                            else:
                                                for i in range(wap_a):
                                                    item = GameObject(ItemMap.objects.create())  # 场景物品
                                                    item.set('params', xt_items.params)
                                                    item.set('bangding', xt_items.bangding)
                                                    item.set('name', xt_items.name)
                                                    item.set('desc', xt_items.desc)
                                                    item.set('map_id', u.map_id)
                                                    item.set('area_id', u.area_id)
                                                    item.set('area_name', xt_items.area_name)
                                                    item.set('count', 1)
                                                    item.set('player_id', u.id)
                                                    item.set('drop_time', int(time.time()) + 60)
                                                    item.set('type', xt_items.type)
                                                    item.set('type_id', xt_items.type_id)
                                                    item.set('item_id', xt_items.id)
                                                    item.set('time', xt_items.time)
                                                    item.set('update', 1)
                                                    item.set('biaoshi', xt_items.biaoshi)
                                                    item.set('duixiang', xt_items.duixiang)
                                                    run_event_all(u, item, request, ut, g, c, t, e, 'item',
                                                                  '创建事件')
                                                    run_event_item(u, item, request, ut, g, c, t, e,
                                                                   xt_items.id,
                                                                   '创建事件')
                                                    item.save()
                                                    get_tishi(request, '掉落地上:{}*1'.format(item.name))
                                u.save()
                            # else:
                            #    if o.is_boss == 1:
                            #        get_tishi(request, 'BOSS已被 {} 击杀'.format(o.kill_boss_name))

                    else:
                        if o.is_boss == 1:
                            get_tishi(request, 'BOSS已被 {} 击杀'.format(o.kill_boss_name))
                # 战斗结束
                monster_countss = Monster.objects.filter(player_id=u.id, zhuangtai=1).count()
                if int(u.pk_npc_id) > 0 or monster_countss > 0:#
                    if int(u.hp) <= 0:
                        u.set('message', '{}你被杀死了<br/>'.format(u.message))
                        if o.ys_id > 0:
                            run_event_all(u, o, request, ut, g, c, t, e, 'npc', '战胜事件')
                            run_event_npc(u, o, request, ut, g, c, t, e, o.ys_id, '战胜事件')
                        run_event_all(u, o, request, ut, g, c, t, e, 'player', '战败事件')
                        request.session['wap_page_name'] = '战斗结束'
                    if int(o.hp) <= 0:
                        u.set('message', '{}战斗胜利，你赢了.<br/>'.format(u.message))
                        request.session['wap_page_name'] = '战斗结束'
                elif monster_countss == 0 and int(
                        u.pk_player_id) == 0 and int(u.pk_npc_id) == 0:
                    u.set('message', '{}战斗胜利，你赢了<br/>'.format(u.message))
                    request.session['wap_page_name'] = '战斗结束'
                    try:
                        if o.is_death == 1:
                            npcs = GameObject(GameNpcs.objects.get(id=o.npc_id))
                            npcs.set('hp', 0)
                            npcs.save()
                    except:
                        pass#Monster.objects.filter(player_id=u.id, zhuangtai=1).count() == 0 and
                # 与人物PK时
                elif int(u.pk_player_id) > 0:
                    o = GameObject(Player.objects.get(id=u.pk_player_id))
                    try:
                        try:
                            create = AttackInfo.objects.get(player_id=u.id, attack_player_id=o.id)
                        except:
                            create = AttackInfo.objects.get(player_id=o.id, attack_player_id=u.id)
                        create.time = int(time.time())
                        create.save()
                    except:
                        pass
                    # 怪物或者人物死亡，战斗结束
                    if int(o.hp) <= 0 or int(u.hp) <= 0:
                        request.session['wap_page_name'] = '战斗结束'
                        if int(o.hp) <= 0:
                            get_tishi(request, '战斗胜利')
                            message(o, '你被{}杀死了'.format(u.name))
                            #o.set('map_id', 27)
                            #o.set('map_name', '教堂')
                        else:
                            get_tishi(request, '战斗失败')
                            #u.set('map_id', 27)
                            #u.set('map_name', '教堂')
                        u.set('pk_player_id', 0)
                        u.set('pk_npc_id', 0)
                        u.save()
                        o.set('pk_player_id', 0)
                        o.set('pk_npc_id', 0)
                        o.save()
                        try:
                            create.delete()
                        except:
                            pass
                else:
                    pass
        if request.session['wap_page_name'] == '战斗':
            try:
                if int(o.hp) > 0 and u.hp > 0:
                    for i in range(1, 7):
                        try:
                            skill = QuickSkill.objects.get(player_id=wap_u_id, quick=i)
                            if int(skill.skill_id) > 0:
                                skills = Skills.objects.get(id=skill.skill_id)
                                if str(skill.name) == '0':
                                    skill.name = skills.name
                                    skill.save()
                                    get_tishi(request, '更新名字')
                                parameter = parameter_create(encryption, value=skill.skill_id)
                                skills_name = skill.name
                                if i == 1:
                                    快捷1 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                                elif i == 2:
                                    快捷2 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                                elif i == 3:
                                    快捷3 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                                elif i == 4:
                                    快捷4 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                                elif i == 5:
                                    快捷5 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                                else:
                                    快捷6 = '<a href="/wap/?pk_info={}&pk_player_skill_id={}" >{}</a>'.format(parameter,
                                                                                                              skill.skill_id,
                                                                                                              skills_name)
                            else:
                                try:
                                    skills = ItemPlayer.objects.get(id=skill.item_id)
                                    if str(skill.name) == '0':
                                        skill.name = skills.name
                                        skill.save()
                                    parameter = parameter_create(encryption, value=skill.item_id)
                                    skills_name = skills.name
                                    if i == 1:
                                        快捷1 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                    elif i == 2:
                                        快捷2 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                    elif i == 3:
                                        快捷3 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                    elif i == 4:
                                        快捷4 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                    elif i == 5:
                                        快捷5 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                    else:
                                        快捷6 = '<a href="/wap/?use_item={}">{}*{}</a>'.format(parameter, skills_name,
                                                                                               skills.count)
                                except:
                                    #get_tishi(request, '物品快捷获取失败{}({})'.format(i, skill.item_id))
                                    if i == 1:
                                        快捷1 = '快捷{}'.format(i)
                                    elif i == 2:
                                        快捷2 = '快捷{}'.format(i)
                                    elif i == 3:
                                        快捷3 = '快捷{}'.format(i)
                                    elif i == 4:
                                        快捷4 = '快捷{}'.format(i)
                                    elif i == 5:
                                        快捷5 = '快捷{}'.format(i)
                                    else:
                                        快捷6 = '快捷{}'.format(i)
                        except:
                            if i == 1:
                                快捷1 = '快捷{}'.format(i)
                            elif i == 2:
                                快捷2 = '快捷{}'.format(i)
                            elif i == 3:
                                快捷3 = '快捷{}'.format(i)
                            elif i == 4:
                                快捷4 = '快捷{}'.format(i)
                            elif i == 5:
                                快捷5 = '快捷{}'.format(i)
                            else:
                                快捷6 = '快捷{}'.format(i)
                            # u.set('shua_xing', request.session['pmwap_shua_xing'])
            except:
                pass
            if zx == 0:
                request.session['wap_page_name'] = '场景'
                if u.hp <= 0:
                    get_tishi(request, '你已经被 {} 杀死了'.format(u.pk_player_name))
                else:
                    get_tishi(request, '你打死了 {}'.format(u.pk_player_name))
        if request.session['wap_page_name'] == '查看物品' or request.session[
            'wap_page_name'] == '丢弃物品'  or request.session['wap_page_name'] == '帮会仓库查看物品':  # 当物品为0时，删除物品，并且转入背包
            if ItemPlayer.objects.filter(id=request.session['check_item_id']).count() > 0:
                o = GameObject(ItemPlayer.objects.get(id=request.session['check_item_id']))
                if int(o.count) > 0:
                    pass
                else:
                    request.session['wap_page_name'] = '背包'
                    o = ItemPlayer.objects.filter(id=request.session['check_item_id']).delete()
                if request.session['wap_page_name'] == '查看物品'  or request.session['wap_page_name'] == '帮会仓库查看物品':
                    parameter_id = parameter_create(encryption, value=o.id)
                    if o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                        save_cangku = '<a href="/wap/?save_cangku_id={}&save_cangku_count=1" >存入仓库</a><br/>'.format(
                            parameter_id)
                    else:
                        save_cangku = """
                        <form action="/wap/" method="GET">
                        <input type="hidden" name="save_cangku_id" value={}>
                        数量:<input type="text"  name="save_cangku_count" value=1 size="3"> <input name="submit" type="submit" title="存仓" value="存仓"/><br/>
                        </form>
                        """.format(parameter_id)
            else:
                request.session['wap_page_name'] = '背包'
                o = ItemPlayer.objects.filter(id=request.session['check_item_id']).delete()
                get_tishi(request, '找不o，跳转回背包')
        elif request.session['wap_page_name'] == '仓库查看物品':
            o = GameObject(CangKuPlayer.objects.get(id=request.session['cangku_check_item_id']))
            if int(o.count) > 0:
                pass
            else:
                request.session['wap_page_name'] = '仓库'
                o = CangKuPlayer.objects.get(id=request.session['cangku_check_item_id']).delete()
            if request.session['wap_page_name'] == '仓库查看物品':
                parameter_id = parameter_create(encryption, value=o.id)
                if o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                    take_cangku = '<a href="/wap/?take_cangku_id={}&take_cangku_count=1" >取回物品</a>'.format(
                        parameter_id)
                else:
                    take_cangku = """
                        <form action="/wap/" method="GET">
                        <input type="hidden" name="take_cangku_id" value={}>
                        数量:<input type="text"  name="take_cangku_count" value=1 size="6"> <input name="submit" type="submit" title="取回物品" value="取回物品"/><br/>
                        </form>
                        """.format(parameter_id)
        elif request.session['wap_page_name'] == '电脑人物':  # o是否变更
            if int(o.id) != int(request.session['npc_id']) or request.session['pmwap_ls_page_name'] != '电脑人物':
                o = GameObject(GameNpcs.objects.get(id=request.session['npc_id']))
            if int(o.is_kill) == 0:
                attack_operation = ''
            else:
                parameter = parameter_create(encryption, value=o.id)
                parameter_page = parameter_create(encryption, value='战斗')
                attack_operation = '<a href="/wap/?pk_get_into={}&npc_id={}" >攻击{}</a>'.format(parameter_page,
                                                                                                 parameter,
                                                                                                 o.name)
        else:
            pass
        if request.session['wap_page_name'] == '场景':  # 当为场景模板时，执行地图事件
            o = GameObject(GameMap.objects.get(id=u.map_id))
            request.session['zs_wp_id'] = 0
            request.session['page_yeshu'] = 0
            request.session['ls_shangji_page'] = 0
            request.session['ls_up_page_name'] = 0
            request.session['ls_pk_page'] = 0
            request.session['item_page_duixiang'] = 0
            if u.is_designer == 'True':
                request.session['object_name'] = 'map'
                request.session['object_id'] = o.id
                request.session['area_name'] = o.area_name
            else:
                request.session['object_name'] = 0
                request.session['object_id'] = 0
                request.session['area_name'] = 0
            '''
                monster_list = ''
                npc_list = ''
                item_list = ''
                '''
            if o.last_refresh_time + o.flush_time - u.time <= 0 and o.flush_time > 0 or request.GET.get(
                    'map_update') == '1':
                request.session['is_wap'] = 'True'  # 是否进入地图凭证，防止直接进入游戏
                o.set('last_refresh_time', u.time)
                o.save()
                objects_map_o = o  # 记录上次o属性
                area_lists = GameAreaName.objects.all()
                for area in area_lists:
                    # 地图NPC刷新
                    GameNpcs.objects.filter(map_id=u.map_id, area_id=area.area_id).update(update=0)
                        # get_tishi(request, '更新地图中存在怪物{}个'.format(GameNpcs.objects.filter(map_id=o.id).count()))
                    if GameMapPlaceNpc.objects.filter(map_id=objects_map_o.id).count() == 0:  # 检测该地图是否有放置系统NPC
                        npcs = GameNpcs.objects.filter(map_id=objects_map_o.id).delete()
                    else:
                        placenpcs = GameMapPlaceNpc.objects.filter(map_id=objects_map_o.id)
                        placenpcs = placenpcs.iterator()
                        placenpcs = dict.fromkeys(placenpcs)
                        for placenpc in placenpcs:  # 生成场景中电脑人物
                            if GameNpc.objects.filter(id=placenpc.npc_id).count() == 0:
                                GameMapPlaceNpc.objects.get(map_id=u.map_id,
                                                            npc_id=placenpc.npc_id).delete()  # 系统内没有这个怪物时删除放置怪物
                            else:
                                xt_npcs = GameNpc.objects.get(id=placenpc.npc_id)
                                if int(xt_npcs.is_kill) == 0 or int(xt_npcs.is_boss) == 1:
                                    counts = 1 + 1
                                else:
                                    counts = 1 + eval(placenpc.npc_code)
                                # get_tishi(request, '怪物id:{}*{}'.format(placenpc.npc_id, counts))
                                for i in range(1, counts):
                                    if int(xt_npcs.is_boss) == 0:
                                        if GameNpcs.objects.filter(map_id=u.map_id, update=0, area_id=area.area_id,
                                                                   is_boss=0).count() > 0:  # 地图中有可用怪物则改变属性
                                            npcs = GameNpcs.objects.filter(map_id=u.map_id, update=0,
                                                                           area_id=area.area_id,
                                                                           is_boss=0)[0:1]
                                            for o in npcs:
                                                o = GameObject(o)
                                                o.set('params', xt_npcs.params)
                                                o.set('name', xt_npcs.name)
                                                o.set('desc', xt_npcs.desc)
                                                o.set('is_drop', xt_npcs.is_drop)
                                                o.set('exp_expression', xt_npcs.exp_expression)
                                                o.set('money_expression', xt_npcs.money_expression)
                                                o.set('lingqi_expression', xt_npcs.lingqi_expression)
                                                o.set('area_name', xt_npcs.area_name)
                                                o.set('map_id', u.map_id)
                                                o.set('area_id', area.area_id)
                                                o.set('ys_id', xt_npcs.id)
                                                o.set('attack_npc', 0)
                                                o.set('is_boss', 0)
                                                o.set('update', 1)
                                                o.set('is_kill', xt_npcs.is_kill)
                                                o.set('map_name', objects_map_o.name)
                                                o.save()
                                                run_event_all_zh(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                                                run_event_npc_zh(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
                                                # get_tishi(request, '重设怪物属性')
                                        else:  # 场景中没有可用怪物则创建一个新怪物
                                            o = GameNpcs.objects.create()  # 场景NPC
                                            o = GameObject(o)
                                            o.set('params', xt_npcs.params)
                                            o.set('area_name', xt_npcs.area_name)
                                            o.set('name', xt_npcs.name)
                                            o.set('desc', xt_npcs.desc)
                                            o.set('is_drop', xt_npcs.is_drop)
                                            o.set('exp_expression', xt_npcs.exp_expression)
                                            o.set('money_expression', xt_npcs.money_expression)
                                            o.set('lingqi_expression', xt_npcs.lingqi_expression)
                                            o.set('map_id', u.map_id)
                                            o.set('area_id', area.area_id)
                                            o.set('ys_id', xt_npcs.id)
                                            o.set('attack_npc', 0)
                                            o.set('is_boss', 0)
                                            o.set('update', 1)
                                            o.set('is_kill', xt_npcs.is_kill)
                                            o.set('map_name', objects_map_o.name)
                                            o.save()
                                            run_event_all_zh(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                                            run_event_npc_zh(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
                                            # get_tishi(request, '创建怪物属性')
                                    else:
                                        if GameNpcs.objects.filter(map_id=u.map_id, area_id=area.area_id, is_boss=1,
                                                                   ys_id=xt_npcs.id).count() > 0:  # 地图中有可用怪物则改变属性
                                            npcs = GameNpcs.objects.filter(map_id=u.map_id, area_id=area.area_id,
                                                                           is_boss=1)[
                                                   0:1]
                                            for o in npcs:
                                                o = GameObject(o)
                                                if int(o.sw_time) + int(xt_npcs.refresh_time) <= int(time.time()):
                                                    o.set('params', xt_npcs.params)
                                                    o.set('area_name', xt_npcs.area_name)
                                                    o.set('name', xt_npcs.name)
                                                    o.set('desc', xt_npcs.desc)
                                                    o.set('is_drop', xt_npcs.is_drop)
                                                    o.set('exp_expression', xt_npcs.exp_expression)
                                                    o.set('money_expression', xt_npcs.money_expression)
                                                    o.set('lingqi_expression', xt_npcs.lingqi_expression)
                                                    o.set('map_id', u.map_id)
                                                    o.set('area_id', area.area_id)
                                                    o.set('ys_id', xt_npcs.id)
                                                    o.set('attack_npc', 0)
                                                    o.set('is_boss', 1)
                                                    o.set('sw_time', int(time.time()))
                                                    o.set('update', 1)
                                                    o.set('is_kill', xt_npcs.is_kill)
                                                    o.set('map_name', objects_map_o.name)
                                                    o.save()
                                                    BossRanking.objects.filter(npc_id=xt_npcs.id,area_id=o.area_id).delete()
                                                    get_tishi(request, 'BOSS排名统计删除成功')
                                                    run_event_all_zh(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                                                    run_event_npc_zh(u, o, request, ut, g, c, t, e, xt_npcs.id,
                                                                     '创建事件')
                                                    # get_tishi(request, 'boss重置属性')
                                                else:
                                                    o.set('update', 1)
                                                    o.save()
                                        else:  # 场景中没有可用怪物则创建一个新怪物
                                            o = GameNpcs.objects.create()  # 场景NPC
                                            o = GameObject(o)
                                            o.set('params', xt_npcs.params)
                                            o.set('area_name', xt_npcs.area_name)
                                            o.set('name', xt_npcs.name)
                                            o.set('desc', xt_npcs.desc)
                                            o.set('is_drop', xt_npcs.is_drop)
                                            o.set('exp_expression', xt_npcs.exp_expression)
                                            o.set('money_expression', xt_npcs.money_expression)
                                            o.set('lingqi_expression', xt_npcs.lingqi_expression)
                                            o.set('map_id', u.map_id)
                                            o.set('area_id', area.area_id)
                                            o.set('ys_id', xt_npcs.id)
                                            o.set('attack_npc', 0)
                                            o.set('is_boss', 1)
                                            o.set('update', 1)
                                            o.set('sw_time', int(time.time()))
                                            o.set('is_kill', xt_npcs.is_kill)
                                            o.set('map_name', objects_map_o.name)
                                            o.save()
                                            BossRanking.objects.filter(npc_id=xt_npcs.id,area_id=o.area_id).delete()
                                            get_tishi(request, 'BOSS排名统计删除成功')
                                            run_event_all_zh(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                                            run_event_npc_zh(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
                                            # get_tishi(request, '创建怪物属性')
                        GameNpcs.objects.filter(map_id=u.map_id, update=0, area_id=area.area_id).delete()  # 删除多余NPC
                # 地图物品刷新
                if ItemMap.objects.filter(map_id=u.map_id, area_id=u.area_id).count() == 0:  # 是否存在场景物品
                    pass
                else:  # 设置场景中物品为未更新
                    items = ItemMap.objects.filter(map_id=u.map_id, area_id=u.area_id).update(update=0)
                    get_tishi(request,
                              '更新地图中存在物品{}个'.format(
                                  ItemMap.objects.filter(map_id=u.map_id, area_id=u.area_id).count()))
                if GameMapPlaceItem.objects.filter(map_id=u.map_id).count() == 0:  # 检测该地图是否有放置物品
                    items = ItemMap.objects.filter(map_id=u.map_id).delete()
                else:
                    placenpcs = GameMapPlaceItem.objects.filter(map_id=u.map_id)
                    placenpcs = placenpcs.iterator()
                    placenpcs = dict.fromkeys(placenpcs)
                    for placenpc in placenpcs:  # 生成场景中物品
                        if Item.objects.filter(id=placenpc.item_id).count() == 0:  # 系统内没有这件物品则删除地图物品和玩家物品
                            item = GameMapPlaceItem.objects.filter(map_id=u.map_id, item_id=placenpc.item_id).delete()
                        else:
                            if ItemMap.objects.filter(map_id=u.map_id, update=0,
                                                      area_id=u.area_id).count() > 0:  # 地图存在时直接更改数据
                                items = ItemMap.objects.filter(map_id=u.map_id, update=0, area_id=u.area_id)[0:1]
                                items = items.iterator()
                                items = dict.fromkeys(items)
                                for o in items:
                                    xt_items = Item.objects.get(id=placenpc.item_id)
                                    o = GameObject(o)  # 场景物品
                                    o.set('params', xt_items.params)
                                    o.set('name', xt_items.name)
                                    o.set('biaoshi', xt_items.biaoshi)
                                    o.set('desc', xt_items.desc)
                                    o.set('duixiang', xt_items.duixiang)
                                    o.set('bangding', xt_items.bangding)
                                    o.set('map_id', u.map_id)
                                    o.set('area_id', u.area_id)
                                    o.set('area_name', xt_items.area_name)
                                    o.set('count', eval(placenpc.item_code))
                                    o.set('player_id', 0)
                                    o.set('type', xt_items.type)
                                    o.set('type_id', xt_items.type_id)
                                    o.set('item_id', xt_items.id)
                                    o.set('time', xt_items.time)
                                    o.set('update', 1)
                                    o.save()
                                    run_event_all(u, o, request, ut, g, c, t, e, 'item', '创建事件')
                                    run_event_item(u, o, request, ut, g, c, t, e, xt_items.id, '创建事件')
                                    # get_tishi(request, '重设物品置属性')
                            else:  # 不存在时则直接创建新物品
                                xt_items = Item.objects.get(id=placenpc.item_id)
                                o = GameObject(ItemMap.objects.create())  # 场景物品
                                o.set('params', xt_items.params)
                                o.set('name', xt_items.name)
                                o.set('biaoshi', xt_items.biaoshi)
                                o.set('desc', xt_items.desc)
                                o.set('bangding', xt_items.bangding)
                                o.set('map_id', u.map_id)
                                o.set('area_id', u.area_id)
                                o.set('duixiang', xt_items.duixiang)
                                o.set('area_name', xt_items.area_name)
                                o.set('count', eval(placenpc.item_code))
                                o.set('player_id', 0)
                                o.set('time', xt_items.time)
                                o.set('type', xt_items.type)
                                o.set('type_id', xt_items.type_id)
                                o.set('item_id', xt_items.id)
                                o.set('update', 1)
                                o.save()
                                run_event_all(u, o, request, ut, g, c, t, e, 'item', '创建事件')
                                run_event_item(u, o, request, ut, g, c, t, e, xt_items.id, '创建事件')
                                # get_tishi(request, '创建物品置属性')
                    ItemMap.objects.filter(map_id=u.map_id, update=0, area_id=u.area_id).delete()  # 删除多余物品
                o = objects_map_o  # 还原记录上次o属性
                npcs = GameNpcs.objects.filter(map_id=u.map_id, is_kill=0, area_id=u.area_id)
                if npcs:
                    cache.set('Npc_list_{}'.format(u.map_id), npcs, None)  # NPC列表记录
                else:
                    cache.set('Npc_list_{}'.format(u.map_id), None)
                para = ['map_upper', 'map_left','map_right','map_lower']
                for map_env in para:
                    try:
                        maps = GameObject(GameMap.objects.get(id=o.get('{}'.format(map_env))))
                        o.set('{}_name'.format(map_env), maps.name)
                    except:
                        o.set('{}'.format(map_env), 0)
                        o.set('{}_name'.format(map_env), 0)
                o.save()
            # BOSS刷新
            if GameNpcs.objects.filter(map_id=u.map_id, area_id=u.area_id, is_boss=1).count() > 0:
                objects_map_o = o  # 记录上次o属性
                npcs = GameNpcs.objects.filter(map_id=u.map_id, area_id=u.area_id, is_boss=1)
                for npc in npcs:
                    o = GameObject(npc)
                    xt_npcs = GameNpc.objects.get(id=o.ys_id)
                    if int(o.sw_time) + int(xt_npcs.refresh_time) <= int(time.time()):
                        o.set('params', xt_npcs.params)
                        o.set('refresh_time', xt_npcs.refresh_time)
                        o.set('name', xt_npcs.name)
                        o.set('desc', xt_npcs.desc)
                        o.set('is_drop', xt_npcs.is_drop)
                        o.set('exp_expression', xt_npcs.exp_expression)
                        o.set('money_expression', xt_npcs.money_expression)
                        o.set('lingqi_expression', xt_npcs.lingqi_expression)
                        o.set('map_id', u.map_id)
                        o.set('area_id', u.area_id)
                        o.set('area_name', xt_npcs.area_name)
                        o.set('ys_id', xt_npcs.id)
                        o.set('attack_npc', 0)
                        o.set('is_boss', 1)
                        o.set('sw_time', int(time.time()))
                        o.set('update', 1)
                        o.set('is_kill', xt_npcs.is_kill)
                        o.set('map_name', objects_map_o.name)
                        o.save()
                        get_tishi(request, '更新BOSS成功')
                        BossRanking.objects.filter(npc_id=xt_npcs.id,area_id=o.area_id).delete()
                        run_event_all_zh(u, o, request, ut, g, c, t, e, 'npc', '创建事件')
                        run_event_npc_zh(u, o, request, ut, g, c, t, e, xt_npcs.id, '创建事件')
                o = objects_map_o  # 还原记录上次o属性
        elif request.session['wap_page_name'] == '点卡设置':
            if request.GET.get('money'):
                for i in range(10):
                    kaihao = ''.join(random.sample(string.ascii_letters + string.digits, 8))
                    if Card.objects.filter(card=kaihao).count() == 0:
                        break
                card = Card.objects.create()
                card.card = kaihao
                card.money = request.GET.get('money')
                card.zhuangtai = 0
                card.save()
                get_tishi(request, '创建点卡成功，卡号{}/金额{}'.format(card.card, card.money))
            if request.GET.get('card'):
                if Card.objects.filter(card=request.GET.get('card')).count() == 0:
                    get_tishi(request, '点卡编号错误')
                else:
                    card = Card.objects.get(card=request.GET.get('card'))
                    if int(card.zhuangtai) == 0:
                        get_tishi(request, '点卡验证成功，金额{}'.format(card.money))
                        card.zhuangtai = 1
                        card.time = '{}-{}-{}'.format(date('年'), date('月'), date('日'))
                        card.player_id = u.id
                        card.save()
                        # 此处给充值后给的属性，充值金额属性为card.money
                    else:
                        get_tishi(request, '卡号已被使用')
            if request.GET.get('ck_card'):
                cards = Card.objects.all()
                for card in cards:
                    if int(card.zhuangtai) == 0:
                        get_tishi(request, '{}.{}[{}RMB] 未使用'.format(card.id, card.card, card.money))
                    else:
                        if card.player_id != '':
                            if Player.objects.filter(id=card.player_id).count() == 0:
                                get_tishi(request,
                                          '{}.{}[{}RMB] 已使用,使用者不存在【{}】'.format(card.id, card.card, card.money,
                                                                                        card.time))
                            else:
                                players = Player.objects.get(id=card.player_id)
                                get_tishi(request,
                                          '{}.{}[{}RMB] 已使用[{}(id:{})]【{}】'.format(card.id, card.card, card.money,
                                                                                      players.name, card.player_id,
                                                                                      card.time))
                        else:
                            get_tishi(request,
                                      '{}.{}[{}RMB] 已使用,使用者不存在【{}】'.format(card.id, card.card, card.money,
                                                                                    card.time))
        elif request.session['wap_page_name'] == '状态':
            pass
        elif request.session['wap_page_name'] == '聊天':
            if request.session.get('chat_area_name') is None:
                request.session['chat_area_name'] = '全部'
            area_names = ChatAreaName.objects.all()
            for area_name in area_names:
                parameter = parameter_create(encryption, value=area_name.id)
                parameter_chat = parameter_create(encryption, value=0)
                chat_area_id = 1 if request.session.get('chat_area_id') is None else int(
                    request.session['chat_area_id'])
                if chat_area_id != int(area_name.id):
                    ls_sx = ls_sx + 1
                    if u.is_ajax == 1:
                        ut.set('chat_area_{}_name'.format(area_name.id),
                               """<button class="wap_link_class"  id="lt{}" value='"chat_area_id":"{}","up_chat":"{}"'>{}</button>""".format(
                                   ls_sx, parameter, parameter_chat, area_name.area_name))
                    else:
                        ut.set('chat_area_{}_name'.format(area_name.id),
                               '<a href="/wap/?chat_area_id={}&up_chat={}">{}</a>'.format(parameter, parameter_chat,
                                                                                          area_name.area_name))
                else:
                    ut.set('chat_area_{}_name'.format(area_name.id), '{}'.format(area_name.area_name))
            page = 0 if request.session.get('chat_page') is None else int(request.session['chat_page'])
            if chat_area_id == 5:
                max_count = ChatMessage.objects.filter(Q(player_id=wap_u_id) | Q(sender_player_id=wap_u_id,area_name='私聊')).count()
            elif chat_area_id == 1:
                max_count = ChatMessage.objects.filter(
                    Q(all_area_name='全部', area_id=u.area_id) | Q(player_id=wap_u_id) | Q(team_id=u.team_id)).count()
            elif chat_area_id == 4:
                max_count = ChatMessage.objects.filter(bangpai_id=u.bangpai_id).count()
            else:
                max_count = ChatMessage.objects.filter(area_name=request.session['chat_area_name'],
                                                       area_id=u.area_id).count()
            max_count = int(max_count)
            max_page = (int(max_count) - 1) / 10
            page_start = page * 10
            page_end = page * 10 + 10 if int(max_count) >= (page * 10 + 10) else int(max_count)
            if chat_area_id == 5:
                chat_lists = ChatMessage.objects.filter(Q(player_id=wap_u_id) | Q(sender_player_id=wap_u_id,area_name='私聊')).order_by('-id')[page_start:page_end]
            elif chat_area_id == 4:
                chat_lists = ChatMessage.objects.filter(bangpai_id=u.bangpai_id).order_by('-id')[page_start:page_end]
            elif chat_area_id == 1:
                chat_lists = ChatMessage.objects.filter(
                    Q(all_area_name='全部', area_id=u.area_id) | Q(player_id=wap_u_id) | Q(team_id=u.team_id)).order_by(
                    '-id')[page_start:page_end]
            else:
                chat_lists = ChatMessage.objects.filter(area_name=request.session['chat_area_name'],
                                                        area_id=u.area_id).order_by('-id')[
                             page_start:page_end]
            if int(max_count) >= 1:
                chat_list = '---<br/>'
                bh = int(page) * 10
                chat_lists = chat_lists.iterator()
                chat_lists = dict.fromkeys(chat_lists)
                for i in chat_lists:
                    # 删除不存在玩家
                    if Player.objects.filter(id=i.sender_player_id).count() == 0 and int(
                            i.sender_player_id) != 0 or Player.objects.filter(id=i.player_id).count() == 0 and int(
                        i.player_id) != 0:
                        i.delete()
                    else:
                        bh = bh + 1
                        if  i.page_name == '交易物品详情':
                            chat_list = '{}[交易]'.format(chat_list)
                        elif i.page_name == '查看物品':
                            chat_list = '{}[赠送]'.format(chat_list)
                        elif i.page_name == '赠送礼物':
                            chat_list = '{}[礼物]'.format(chat_list)
                        elif i.page_name == '山寨':
                            chat_list = '{}[山寨]'.format(chat_list)
                        else:
                            chat_list = '{}[{}]'.format(chat_list, i.area_name)
                        if int(i.sender_player_id) != 0 and int(i.sender_player_id) != u.id and chat_area_id == 5 or chat_area_id != 5 and int(i.sender_player_id) != 0:
                            parameter = parameter_create(encryption, value=i.sender_player_id)
                            parameter_page_name = parameter_create(encryption, value='查看玩家')
                            ls_sx = ls_sx + 1
                            if u.is_ajax == 1:
                                chat_list = """{}<button class="wap_link_class"  id="lt{}" value='"check_player_id":"{}","page_name":"{}"'>{}</button>""".format(
                                    chat_list, ls_sx, parameter, parameter_page_name, i.sender_player_name)
                            else:
                                chat_list = '{}<a href="/wap/?check_player_id={}&page_name={}">{}</a> '.format(
                                    chat_list,
                                    parameter,
                                    parameter_page_name,
                                    i.sender_player_name)
                        elif int(i.sender_player_id) == u.id and chat_area_id == 5:
                            parameter = parameter_create(encryption, value=i.player_id)
                            parameter_page_name = parameter_create(encryption, value='查看玩家')
                            ls_sx = ls_sx + 1
                            try:
                                player = Player.objects.get(id=i.player_id)
                                player_name = player.name
                            except:
                                player_name = ''
                            if u.is_ajax == 1:
                                chat_list = """{}<button class="wap_link_class"  id="lt{}" value='"check_player_id":"{}","page_name":"{}"'>{}</button>""".format(
                                    chat_list, ls_sx, parameter, parameter_page_name, i.sender_player_name)
                            else:
                                chat_list = '{}你对<a href="/wap/?check_player_id={}&page_name={}">{}</a>说: '.format(
                                    chat_list,
                                    parameter,
                                    parameter_page_name,
                                    player_name)
                        chat_list = '{}{}<br/>'.format(chat_list, i.message)
                if page > 0 or page < max_page:
                    chat_list = '{}---<br/>'.format(chat_list)
                if page > 0:
                    wap_a = page - 1
                    parameter = parameter_create(encryption, value=wap_a)
                    ls_sx = ls_sx + 1
                    if u.is_ajax == 1:
                        chat_list = """{}<button class="wap_link_class"  id="lt{}" value='"up_chat":"{}"'>上一页</button>|""".format(
                            chat_list, ls_sx, parameter)
                    else:
                        chat_list = '{}<a href="/wap/?up_chat={}">上一页</a> |'.format(chat_list, parameter)
                if page < int(max_page):
                    wap_a = page + 1
                    parameter = parameter_create(encryption, value=wap_a)
                    ls_sx = ls_sx + 1
                    if u.is_ajax == 1:
                        chat_list = """{}<button class="wap_link_class"  id="lt{}" value='"next_chat":"{}"'>下一页</button>""".format(
                            chat_list, ls_sx, parameter)
                    else:
                        chat_list = '{}<a href="/wap/?next_chat={}">下一页</a>'.format(chat_list, parameter)
                if page > 0 or page < int(max_page):
                    chat_list = '{}<br/>'.format(chat_list)
            else:
                chat_list = '---<br/>暂无聊天内容<br/>'
        elif request.session['wap_page_name'] == '背包':
            pass
        elif request.session['wap_page_name'] == '仓库':
            pass
        elif request.session['wap_page_name'] == '电脑人物':  # o是否变更
            try:
                if int(o.id) != int(request.session['npc_id']) or request.session['pmwap_ls_page_name'] != '电脑人物':
                    o = GameObject(GameNpcs.objects.get(id=request.session['npc_id']))
                purchase_items = ''
                if SellGoods.objects.filter(npc_id=o.ys_id).count() == 0:
                    pass
                else:
                    parameter_page = parameter_create(encryption, value='电脑人物出售列表')
                    purchase_items = '<a href="/wap/?page_name={}" >购买物品</a>'.format(parameter_page)
            except:
                request.session['wap_page_name'] = '场景'
        elif request.session['wap_page_name'] == '宠物列表':
            pets_list = ''
            max_count = Pets.objects.filter(player_id=wap_u_id).count()
            page = int(request.session.get('page_yeshu', 0))
            max_page = (max_count - 1) / 10
            page_start = page * 10
            page_end = page * 10 + 10 if max_count >= page * 10 + 10 else int(max_count)
            pets_lists = Pets.objects.filter(player_id=wap_u_id)[page_start:page_end]
            pets_lists = pets_lists.iterator()
            pets_lists = dict.fromkeys(pets_lists)
            if max_count >= 1:
                pets_list = '---<br/>'
            else:
                pets_list = '---<br/>暂无宠物<br/>'
            bh = page * 10
            for i in pets_lists:
                bh = bh + 1
                parameter_name = parameter_create(encryption, value='查看宠物')
                parameter = parameter_create(encryption, value=i.id)
                pets_list = '{}{}.<a href="/wap/?page_name={}&pets_id={}">{}</a><br/>'.format(pets_list, bh,
                                                                                              parameter_name,
                                                                                              parameter,
                                                                                              i.name)
            if page > 0 or page < int(max_page):
                pets_list = '{}---<br/>'.format(pets_list)
            if page > 0:
                wap_a = page - 1
                parameter = parameter_create(encryption, value=wap_a)
                pets_list = '{}<a href="/wap/?up_pets={}">上一页</a> '.format(pets_list, parameter)
            if page < int(max_page):
                wap_a = page + 1
                parameter = parameter_create(encryption, value=wap_a)
                pets_list = '{}<a href="/wap/?next_pets={}">下一页</a>'.format(pets_list, parameter)
            if page > 0 or page < int(max_page):
                pets_list = '{}<br/>'.format(pets_list)
            pets_list = '{}---<br/>'.format(pets_list)
        elif request.session['wap_page_name'] == '坐骑列表':
            pass
        elif request.session['wap_page_name'] == '查看宠物' or request.session['wap_page_name'] == '查看武将':
            o = GameObject(Pets.objects.get(id=request.session['check_pets_id']))
        elif request.session['wap_page_name'] == '查看士兵' and request.session.get('check_soldier_id', '0') != '0' or \
                request.session['wap_page_name'] == '招募士兵' and request.session.get('check_soldier_id', '0') != '0':
            o = GameObject(Soldier.objects.get(id=request.session['check_soldier_id']))
        elif request.session['wap_page_name'] == '查看坐骑':
            o = GameObject(Mount.objects.get(id=request.session['check_mount_id']))
        elif request.session['wap_page_name'] == '查看玩家':
            o = GameObject(Player.objects.get(id=request.session['check_player_id']))
            oh = GameObject(UhPlayer.objects.get(player_id=o.id))
        elif request.session['wap_page_name'] == '查看装备':
            o = GameObject(ItemPlayer.objects.get(id=request.session['check_item_id']))
        elif request.session['wap_page_name'] == '商城购买':
            o = GameObject(Item.objects.get(id=request.session['shangcheng_item_id']))
        elif request.session['wap_page_name'] == '交易行':
            auctions = AuctionAreaName.objects.all()
        elif request.session['wap_page_name'] == '技能列表':
            skill_list = ''
            max_count = Skills.objects.filter(player_id=wap_u_id).count()
            page = int(u.pets_page)
            max_page = (max_count - 1) / 10
            page_start = page * 10
            page_end = page * 10 + 10 if max_count >= page * 10 + 10 else int(max_count)
            skills_lists = Skills.objects.filter(player_id=wap_u_id)[page_start:page_end]
            skills_lists = skills_lists.iterator()
            skills_lists = dict.fromkeys(skills_lists)
            if max_count >= 1:
                skill_list = '---<br/>'
            else:
                skill_list = '---<br/>暂无技能<br/>'
            bh = page * 10
            for i in skills_lists:
                bh = bh + 1
                parameter_name = parameter_create(encryption, value='查看技能')
                parameter = parameter_create(encryption, value=i.id)
                skill_list = '{}{}.<a href="/wap/?page_name={}&skill_id={}">{}({}级)</a><br/>'.format(skill_list, bh,
                                                                                                      parameter_name,
                                                                                                      parameter,
                                                                                                      i.name, i.lvl)
            if page > 0 or page < int(max_page):
                skill_list = '{}---<br/>'.format(skill_list)
            if page > 0:
                wap_a = page - 1
                parameter = parameter_create(encryption, value=wap_a)
                skill_list = '{}<a href="/wap/?up_skill={}">上一页</a> '.format(skill_list, parameter)
            if page < int(max_page):
                wap_a = page + 1
                parameter = parameter_create(encryption, value=wap_a)
                skill_list = '{}<a href="/wap/?next_skill={}">下一页</a>'.format(skill_list, parameter)
            if page > 0 or page < int(max_page):
                skill_list = '{}<br/>'.format(skill_list)
            skill_list = '{}---<br/>'.format(skill_list)
        elif request.session['wap_page_name'] == '查看技能':
            o = GameObject(Skills.objects.get(id=request.session['check_skill_id']))
            skill_quick = ''
            for i in range(1, 7):
                if QuickSkill.objects.filter(player_id=wap_u_id, skill_id=o.id, quick=i).count() == 0:
                    parameter = parameter_create(encryption, value=i)
                    skill_quick = '{}<a href="/wap/?skill_quick={}">快{}</a> '.format(skill_quick, parameter, i)
                else:
                    skill_quick = '{}快{} '.format(skill_quick, i)
        elif request.session['wap_page_name'] == '交易行物品列表':
            if request.GET.get('auction_name'):
                request.session['auction_name'] = request.GET.get('auction_name')
                request.session['area_name'] = '全部'
                request.session['item_name'] = '0'
                request.session['page_yeshu'] = 0
            if request.GET.get('sosuo'):
                request.session['area_name'] = request.GET.get('sosuo_area_name')
                if request.GET.get('sosuo_name'):
                    request.session['item_name'] = request.GET.get('sosuo_name')
                else:
                    request.session['item_name'] = '0'
                request.session['page_yeshu'] = 0
            page_yeshu = int(request.session['page_yeshu'])
            auction_name = request.session['auction_name']
            area_name = request.session['area_name']
            item_name = request.session['item_name']
            start_count = page_yeshu * 10
            end_count = page_yeshu * 10 + 10
            if item_name == '0' and area_name == '全部':
                auctions = Auction.objects.filter(auction_name=auction_name, area_id=u.area_id)[start_count:end_count]
            elif item_name == '0' and area_name != '全部':
                auctions = Auction.objects.filter(area_name=area_name, auction_name=auction_name, area_id=u.area_id)[
                           start_count:end_count]
            else:
                auctions = Auction.objects.filter(auction_name=auction_name, item_name__contains=item_name,
                                                  area_id=u.area_id)[start_count:end_count]
        elif request.session['wap_page_name'] == '帮派信息' or request.session['wap_page_name'] == '创建堂口':
            o = GameObject(Gangs.objects.get(id=request.session['check_gangs_id']))
        elif request.session['wap_page_name'] == '帮会技能' or request.session['wap_page_name'] == '我的帮会' or request.session['wap_page_name'] == '帮会成员':
            o = GameObject(BangHui.objects.get(id=request.session['check_banghui_id']))
        elif request.session['wap_page_name'] == '帮派成员':
            o = GangsTangkou.objects.get(id=request.session['check_tangkou_id'])
        else:
            pass
        # 地图/NPC/物品操作
        if request.session['wap_page_name'] == '场景' or request.session['wap_page_name'] == '电脑人物' or \
                request.session[
                    'wap_page_name'] == '查看物品':
            if request.session['wap_page_name'] == '场景':
                try:
                    if cache.get('operation_map_{}'.format(o.id)) is None:
                        operations = Operation.objects.filter(map_id=o.id).values('display', 'code', 'event', 'content',
                                                                                  'id')
                        cache.set('operation_map_{}'.format(o.id), operations)
                    else:
                        operations = cache.get('operation_map_{}'.format(o.id))
                except:
                    operations = ''
                    get_tishi(request, '未使用缓存，操作列表忽略')
            elif request.session['wap_page_name'] == '电脑人物':
                try:
                    if cache.get('operation_npc_{}'.format(o.ys_id)) is None:
                        operations = Operation.objects.filter(npc_id=o.ys_id).values('display', 'code', 'event',
                                                                                     'content',
                                                                                     'id')
                        cache.set('operation_npc_{}'.format(o.ys_id), operations)
                    else:
                        operations = cache.get('operation_npc_{}'.format(o.ys_id))
                except:
                    operations = ''
                    get_tishi(request, '未使用缓存，操作列表忽略')
            elif request.session['wap_page_name'] == '查看物品':
                operations = Operation.objects.filter(item_id=o.item_id).values('display', 'code', 'event', 'content',
                                                                                'id')
                use_equip = ''
                use_item = ''
                if request.GET.get('ck_use_equip_id'):
                    pass
                else:
                    if o.duixiang == 0 or o.duixiang == 1:
                        if o.area_name == '装备' or o.area_name == '武将装备' or o.area_name == '士兵装备' or o.area_name == '坐骑装备' or o.area_name == '宠物装备':
                            parameter = parameter_create(encryption, value=o.id)
                            parameter_page = parameter_create(encryption, value='背包')
                            ls_sx = ls_sx + 1
                            if u.is_ajax == 1:
                                use_equip = """<button  class="wap_link_class"  id="{}" value='"use_equip":"{}","use_up_page":"{}"'>穿上装备</button>""".format(
                                    ls_sx, parameter, parameter_page)
                            else:
                                use_equip = '<a href="/wap/?use_equip={}&use_up_page={}">穿上装备</a>'.format(parameter,
                                                                                                              parameter_page)
                        else:
                            parameter = parameter_create(encryption, value=o.id)
                            ls_sx = ls_sx + 1
                            if u.is_ajax == 1:
                                use_item = """<button  class="wap_link_class"  id="{}" value='"use_item":"{}"'>使用物品</button>""".format(
                                    ls_sx, parameter)
                            else:
                                use_item = '<a href="/wap/?use_item={}">使用物品</a>'.format(parameter)
                item_quick = ''
                for i in range(4, 7):
                    if QuickSkill.objects.filter(player_id=wap_u_id, item_id=o.id, quick=i).count() == 0:
                        parameter = parameter_create(encryption, value=i)
                        item_quick = '{}<a href="/wap/?item_quick={}">快{}</a> '.format(item_quick, parameter, i)
                    else:
                        item_quick = '{}快{} '.format(item_quick, i)
            else:
                operations = ''
            operation_list = ''
            # if operations.exists():
            if operations != '':
                try:
                    for i in operations:
                        i_display = i['display']
                        i_event = i['event']
                        i_code = i['code']
                        i_content = i['content']
                        i_id = i['id']
                        exec(i_code)
                        if i_display == '' or i_display == '0' or i_display == 0 or eval(i_display):
                            if i_event == 'None':
                                if operation_list == '':
                                    operation_list = '{}'.format(i_content)
                                else:
                                    operation_list = '{}<br/>{}'.format(operation_list, i_content)
                            else:
                                if request.GET.get('ck_use_equip_id'):
                                    pass
                                else:
                                    parameter = parameter_create(encryption, value=i_id)
                                    if u.is_ajax == 1:
                                        if operation_list == '':
                                            operation_list = """{}<button class="wap_link_class"  id="{}" value='"ck":"{}"'>{}</button>""".format(
                                                operation_list, i_id, parameter, i_content)  # value包含KEY/VALUE
                                        else:
                                            operation_list = """{}<br/><button class="wap_link_class"  id="{}" value='"ck":"{}"'>{}</button>""".format(
                                                operation_list, i_id, parameter, i_content)  # value包含KEY/VALUE
                                    else:
                                        if operation_list == '':
                                            operation_list = '<a href="/wap/?ck={}">{}</a>'.format(parameter, i_content)
                                        else:
                                            operation_list = '{}<br/><a href="/wap/?ck={}" id="ck{}">{}</a>'.format(
                                                operation_list, parameter, i_id, i_content)
                        else:
                            pass
                except:
                    pass
            var = re.findall(r"{{(.*?)}}", operation_list)
            bbb = re.sub(r"{{(.*?)}}", "{}", operation_list)
            operation_list = bbb.format(*map(eval, var))
        zhixing_message = '模板事件执行{}毫秒,'.format(int((time.time() - starttime2) * 1000))
        starttime1 = time.time()
        #get_tishi(request, '前置事件执行后,执行模板前{}'.format(u.map_id))
        # -----------------------返回页面模板内容-------------------
        neyong_css = ''
        neyong_js = ''
        # operation = Operation.objects.filter(page_name=request.session['wap_page_name']).values('id', 'is_input', 'code', 'display', 'event','content', 'position').order_by('position')
        if cache.get('page_name_{}'.format(request.session['wap_page_name'])) is None or int(c.page_update) == 1:
            rediss = PageName.objects.all()
            for i in rediss:
                cache.set('page_name_{}'.format(i.page_name),
                          Operation.objects.filter(page_name=i.page_name).values('id', 'is_input', 'code', 'display',
                                                                                 'event', 'content',
                                                                                 'position').order_by(
                              'position'), None)
            c.set('page_update', 0)
            c.save()
            message(u, '缓存成功')
        operation = cache.get('page_name_{}'.format(request.session['wap_page_name']))
        for i in operation:
            parameter = parameter_create(encryption, value=i['id'])
            #code = re.sub('Card', "", i['code'])
            exec(i['code'])
            is_input = int(i['is_input'])
            content = i['content']
            display = i['display']
            if is_input == 2 or is_input == 3:
                if is_input == 2:
                    if display == '' or eval(display):
                        neyong_css = "{}{}".format(neyong_css, content)
                elif is_input == 3:
                    if display == '' or eval(display):
                        neyong_js = "{}{}".format(neyong_js, content)
                else:
                    pass
            else:
                event = i['event']
                if display == '' or eval(str(display)):
                    if event == 'None' or is_input == 1:
                        if neyong == '':
                            neyong = '{}'.format(content)
                        else:
                            neyong = '{}{}'.format(neyong, content)
                    else:
                        if request.GET.get('ck_use_equip_id'):
                            pass
                        else:
                            if u.is_ajax == 1:
                                if '<br/>' in content:
                                    neyong = """{}<button class="wap_link_class"  id="{}" value='"ck":"{}"'>{}</button><br/>""".format(
                                        neyong, i['id'], parameter, content)  # value包含KEY/VALUE
                                else:
                                    neyong = """{}<button class="wap_link_class"  id="{}" value='"ck":"{}"'>{}</button>""".format(
                                        neyong, i['id'], parameter, content)  # value包含KEY/VALUE
                            else:
                                neyong = '{}<a href="/wap/?ck={}" id="{}">{}</a>'.format(neyong, parameter, i['id'],
                                                                                         content)
                    if is_input == 1:  # 当为输入框时进行解码
                        var = re.findall(r"{{(.*?)}}", neyong)
                        bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
                        neyong = bbb.format(*map(eval, var))
        #get_tishi(request, '前置事件执行后,最后执行{}'.format(u.map_id))
        messages = '{}{}'.format(u.message, request.session['info_message'])
        operation = cache.get('page_name_{}'.format('全局css'))
        for i in operation:
            if True:
                #code = re.sub('Card', "", i['code'])
                #if code != '' and code != '0':
                exec(i['code'])
                if int(i['is_input']) == 2:
                    if i['display'] == '' or eval(i['display']):
                        neyong_css = "{}{}".format(neyong_css, i['content'])
        # operation = cache.get('page_name_{}'.format('全局script'))
        # for i in operation:
        #    if True:
        #        code = re.sub('Card', "", i['code'])
        #        if code != '' and code != '0':
        #            exec(code)
        #        if int(i['is_input']) == 3:
        #            if i['display'] == '' or eval(i['display']):
        #                neyong_js = "{}{}".format(neyong_js, i['content'])
        neyong_css_js = ''
        if neyong_css != '' or neyong_js != '':
            if neyong_css != '':
                neyong_css_js = '{}<style>{}</style>'.format(neyong_css_js, neyong_css)
            if neyong_js != '':
                neyong_css_js = '{}<script>{}</script>'.format(neyong_css_js, neyong_js)
            neyong_css_js = re.sub(r"{{2,}", r"左", neyong_css_js)
            neyong_css_js = re.sub(r"}{2,}", r"右", neyong_css_js)
            neyong_css_js = re.sub(r'{', r"<…", neyong_css_js)
            neyong_css_js = re.sub(r'}', r"…>", neyong_css_js)
            var = re.findall(r"左(.*?)右", neyong_css_js)
            bbb = re.sub(r"左.*?右", r"{}", neyong_css_js)
            neyong_css_js = bbb.format(*map(eval, var))
            neyong_css_js = re.sub(r"<…", "{", neyong_css_js)
            neyong_css_js = re.sub(r"…>", "}", neyong_css_js)
        if request.GET.get('ck_use_equip_id'):
            parameter = parameter_create(encryption, value=u.use_up_page)
            neyong = '{}<br/>---<br/><a href="/wap/?ck_use_equip_up_page={}">返回上级</a>'.format(neyong, parameter)
        u.set('message', '')
        tishi_messages = ''  # request.session['info_message']
        request.session['up_page_name'] = request.session['ls_page_name']
        if not messages:
            pass
        else:
            var = re.findall(r"{{(.*?)}}", messages)
            bbb = re.sub(r"{{(.*?)}}", "{}", messages)
            messages = bbb.format(*map(eval, var))
        # 提示语
        if str(request.session.get('zdzd')) == '0':
            neyong = '{}{}{}'.format(c.html_messages, c.html_tishi_messages, neyong)
        else:
            neyong = '{}{}{}'.format(c.html_messages, c.html_tishi_messages, neyong)
        var = re.findall(r"{{(.*?)}}", neyong)
        bbb = re.sub(r"{{(.*?)}}", "{}", neyong)
        neyong = bbb.format(*map(eval, var))
        if u.is_designer == 'True':
            neyong = '{}<br/>---开发快捷---<br/>'.format(neyong)
        if u.is_designer == 'True' and request.session['wap_page_name'] == '场景' or c.is_backstage == 1 and \
                request.session['wap_page_name'] == '场景':
            neyong = '{}<a href="/wap_area_name_list_see/?object_id={}">设计{}</a><br/>'.format(neyong, o.id, o.name)
        if u.is_designer == 'True':
            neyong = '{}<a href="/wap_page_list/?page_name={}">设计{}模板</a><br/>'.format(neyong,
                                                                                           request.session[
                                                                                               'wap_page_name'],
                                                                                           request.session[
                                                                                               'wap_page_name'])
        # time.sleep(0.3000)
        neyong = '{}{}执行{}ms'.format(neyong, request.session['wap_page_name'],round((time.time() - starttime) * 1000, 2))
        request.session['encryption'] = encryption
        #if request.is_ajax():
        #    return JsonResponse({"neyong": neyong, "neyong_css_js": neyong_css_js}, safe=False)
        if c.time - int(request.session.get('time', 0)) <= 1:
            request.session['time'] = c.time
            request.session['time_cs'] = int(request.session['time_cs']) + 1
        else:
            request.session['time'] = c.time
            request.session['time_cs'] = 0
        # suo.set('update', 0)
        # suo.set('time',time.time())
        # suo.save()
        Suo.objects.filter(player_id=u.id).update(update=0)
        return render(request, "wap.html", locals())
    else:
        Suo.objects.filter(player_id=u.id).update(update=0)
        messages = '请勿重复操作<br/>'
        return render(request, 'wap_rest.html', locals())
# 计算剩余时间
def is_time(n):
    if n > 0:
        day = ''
        if int(n / 86400) >= 1:
            day = '{}天'.format(int(n / 86400))
            n = n - int(n / 86400) * 86400
        if int(n / 3600) >= 1:
            day = '{}{}小时'.format(day, int(n / 3600))
            n = n - int(n / 3600) * 3600
        if int(n / 60) >= 1:
            day = '{}{}分'.format(day, int(n / 60))
            n = n - int(n / 60) * 60
        day = '{}{}秒'.format(day, int(n))
        return day
    else:
        return 0