#添加配置选择
[uwsgi]
#配置和nginx连接的socket连接
socket=127.0.0.1:8000# --nothreading --noreload
#http=127.0.0.1:8000
#uwsgi_pass http://127.0.0.1:8000
#配置项目路径，项目的所在目录
chdir=/www/wwwroot/zhsh/
#配置wsgi接口模块文件路径,也就是wsgi.py这个文件所在的目录
wsgi-file=game/wsgi.py
#配置启动的进程数
processes=32  # 与CPU核数对齐
#配置每个进程的线程数
#workers=36
#workers = 48# 一般为 CPU 核数 * 2
threads = 4  # 充分利用超线程 # 线程比进程开销更小一点。如果没有使用 threads 那么 thread 直接不工作的，必须使用 enable_threads。
worker_connections = 1000  # 控制总连接数# 最大并发数
# cheaper子进程数
cheaper = 8  # 启用动态进程调整，基础进程数
# 调整请求块大小
buffer-chunksize = 16384
#配置启动管理主进程
max-requests = 10000
master=True
#配置存放主进程的进程号文件
pidfile=uwsgi.pid
#配置dump日志记录
daemonize=uwsgi.log`
# 代码修改后自动重启
py-autoreload = 1
enable-threads = true
limit-as = 1024
enable-threads=true
die-on-term=true
wsgi-disable-file-wrapper=true
env= LUFFY_ENV=Production
ignore-sigpipe = true
ignore-write-errors = true
disable-write-exception = true
cache = true
cache2 = name=mycache,items=500
lazy-apps = true
wsgi-disable-file-wrapper = true
memory-report = true
####下面的配置可以有 也可以没有，看个人需求，不建议配置
# 多站模式
#vhost = true
# 多站模式时不设置入口模块和文件
#no-site = true
# 退出、重启时清理文件
vacuum = true
buffer-size = 262144  # 提升头部缓冲区大小 # header 的 buffer 大小，默认是 4 k
thunder-lock = true # 避免惊群效应
listen = 600 # 每个进程排队的请求数量，默认为 100 太小了。并发数 = procsses * threads * listen