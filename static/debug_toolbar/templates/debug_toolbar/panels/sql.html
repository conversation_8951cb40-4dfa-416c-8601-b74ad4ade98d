{% load i18n l10n %}
<ul>
  {% for alias, info in databases %}
    <li>
      <strong><span class="djdt-color" data-djdt-styles="backgroundColor:rgb({{ info.rgb_color|join:', ' }})"></span> {{ alias }}</strong>
      {{ info.time_spent|floatformat:"2" }} ms ({% blocktrans count info.num_queries as num %}{{ num }} query{% plural %}{{ num }} queries{% endblocktrans %}
      {% if info.similar_count %}
        {% blocktrans with count=info.similar_count trimmed %}
          including <abbr title="Similar queries are queries with the same SQL, but potentially different parameters.">{{ count }} similar</abbr>
        {% endblocktrans %}
        {% if info.duplicate_count %}
          {% blocktrans with dupes=info.duplicate_count trimmed %}
            and <abbr title="Duplicate queries are identical to each other: they execute exactly the same SQL and parameters.">{{ dupes }} duplicates</abbr>
          {% endblocktrans %}
        {% endif %}
      {% endif %})
    </li>
  {% endfor %}
</ul>

{% if queries %}
  <table>
    <colgroup>
      <col>
      <col>
      <col>
      <col class="djdt-width-30">
      <col>
      <col>
    </colgroup>
    <thead>
      <tr>
        <th></th>
        <th colspan="2">{% trans "Query" %}</th>
        <th>{% trans "Timeline" %}</th>
        <th>{% trans "Time (ms)" %}</th>
        <th>{% trans "Action" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for query in queries %}
        <tr class="{% if query.is_slow %} djDebugRowWarning{% endif %}" id="sqlMain_{{ forloop.counter }}">
          <td><span class="djdt-color" data-djdt-styles="backgroundColor:rgb({{ query.rgb_color|join:', '}})"></span></td>
          <td class="djdt-toggle">
            <button type="button" class="djToggleSwitch" data-toggle-name="sqlMain" data-toggle-id="{{ forloop.counter }}">+</button>
          </td>
          <td>
            <div class="djDebugSql">{{ query.sql|safe }}</div>
            {% if query.similar_count %}
              <strong>
                <span class="djdt-color" data-djdt-styles="backgroundColor:{{ query.similar_color }}"></span>
                {% blocktrans with count=query.similar_count %}{{ count }} similar queries.{% endblocktrans %}
              </strong>
            {% endif %}
            {% if query.duplicate_count %}
              <strong>
                <span class="djdt-color" data-djdt-styles="backgroundColor:{{ query.duplicate_color }}"></span>
                {% blocktrans with dupes=query.duplicate_count %}Duplicated {{ dupes }} times.{% endblocktrans %}
              </strong>
            {% endif %}
          </td>
          <td>
            <svg class="djDebugLineChart{% if query.is_slow %} djDebugLineChartWarning{% endif %}{% if query.in_trans %} djDebugLineChartInTransaction{% endif %}" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 100 5" preserveAspectRatio="none" aria-label="{{ query.width_ratio }}%">
              <rect x="{{ query.start_offset|unlocalize }}" y="0" height="5" width="{{ query.width_ratio|unlocalize }}" fill="{{ query.trace_color }}" />
              {% if query.starts_trans %}
                <line x1="{{ query.start_offset|unlocalize }}" y1="0" x2="{{ query.start_offset|unlocalize }}" y2="5" />
              {% endif %}
              {% if query.ends_trans %}
                <line x1="{{ query.end_offset|unlocalize }}" y1="0" x2="{{ query.end_offset|unlocalize }}" y2="5" />
              {% endif %}
            </svg>
          </td>
          <td class="djdt-time">
            {{ query.duration|floatformat:"2" }}
          </td>
          <td class="djdt-actions">
            {% if query.params %}
              {% if query.is_select %}
                <form method="post">
                  {{ query.form }}
                  <button formaction="{% url 'djdt:sql_select' %}" class="remoteCall">Sel</button>
                  <button formaction="{% url 'djdt:sql_explain' %}" class="remoteCall">Expl</button>
                  {% if query.vendor == 'mysql' %}
                    <button formaction="{% url 'djdt:sql_profile' %}" class="remoteCall">Prof</button>
                  {% endif %}
                </form>
              {% endif %}
            {% endif %}
          </td>
        </tr>
        <tr class="djUnselected {% if query.is_slow %} djDebugRowWarning{% endif %} djToggleDetails_{{ forloop.counter }}" id="sqlDetails_{{ forloop.counter }}">
          <td colspan="2"></td>
          <td colspan="4">
            <div class="djSQLDetailsDiv">
              <p><strong>{% trans "Connection:" %}</strong> {{ query.alias }}</p>
              {% if query.iso_level %}
                <p><strong>{% trans "Isolation level:" %}</strong> {{ query.iso_level }}</p>
              {% endif %}
              {% if query.trans_status %}
                <p><strong>{% trans "Transaction status:" %}</strong> {{ query.trans_status }}</p>
              {% endif %}
              {% if query.stacktrace %}
                <pre class="djdt-stack">{{ query.stacktrace }}</pre>
              {% endif %}
              {% if query.template_info %}
                <table>
                  {% for line in query.template_info.context %}
                    <tr>
                      <td>{{ line.num }}</td>
                      <td><code {% if line.highlight %}class="djdt-highlighted"{% endif %}>{{ line.content }}</code></td>
                    </tr>
                  {% endfor %}
                </table>
                <p><strong>{{ query.template_info.name|default:_("(unknown)") }}</strong></p>
              {% endif %}
            </div>
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% else %}
  <p>{% trans "No SQL queries were recorded during this request." %}</p>
{% endif %}
