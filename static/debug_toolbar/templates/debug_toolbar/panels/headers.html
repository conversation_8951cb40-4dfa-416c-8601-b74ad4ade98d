{% load i18n %}

<h4>{% trans "Request headers" %}</h4>

<table>
  <thead>
    <tr>
      <th>{% trans "Key" %}</th>
      <th>{% trans "Value" %}</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in request_headers.items %}
      <tr>
        <td>{{ key|escape }}</td>
        <td>{{ value|escape }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>

<h4>{% trans "Response headers" %}</h4>

<table>
  <thead>
    <tr>
      <th>{% trans "Key" %}</th>
      <th>{% trans "Value" %}</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in response_headers.items %}
      <tr>
        <td>{{ key|escape }}</td>
        <td>{{ value|escape }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>

<h4>{% trans "WSGI environ" %}</h4>

<p>{% trans "Since the WSGI environ inherits the environment of the server, only a significant subset is shown below." %}</p>

<table>
  <thead>
    <tr>
      <th>{% trans "Key" %}</th>
      <th>{% trans "Value" %}</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in environ.items %}
      <tr>
        <td>{{ key|escape }}</td>
        <td>{{ value|escape }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>
