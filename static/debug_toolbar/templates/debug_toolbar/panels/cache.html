{% load i18n %}
<h4>{% trans "Summary" %}</h4>
<table>
  <thead>
    <tr>
      <th>{% trans "Total calls" %}</th>
      <th>{% trans "Total time" %}</th>
      <th>{% trans "Cache hits" %}</th>
      <th>{% trans "Cache misses" %}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ total_calls }}</td>
      <td>{{ total_time }} ms</td>
      <td>{{ hits }}</td>
      <td>{{ misses }}</td>
    </tr>
  </tbody>
</table>
<h4>{% trans "Commands" %}</h4>
<table>
  <thead>
    <tr>
      {% for name in counts.keys %}
        <th>{{ name }}</th>
      {% endfor %}
    </tr>
  </thead>
  <tbody>
    <tr>
      {% for value in counts.values %}
        <td>{{ value }}</td>
      {% endfor %}
    </tr>
  </tbody>
</table>
{% if calls %}
  <h4>{% trans "Calls" %}</h4>
  <table>
    <thead>
      <tr>
        <th colspan="2">{% trans "Time (ms)" %}</th>
        <th>{% trans "Type" %}</th>
        <th>{% trans "Arguments" %}</th>
        <th>{% trans "Keyword arguments" %}</th>
        <th>{% trans "Backend" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for call in calls %}
        <tr id="cacheMain_{{ forloop.counter }}">
          <td class="djdt-toggle">
            <button class="djToggleSwitch" data-toggle-name="cacheMain" data-toggle-id="{{ forloop.counter }}">+</button>
          </td>
          <td>{{ call.time|floatformat:"4" }}</td>
          <td>{{ call.name|escape }}</td>
          <td>{{ call.args|escape }}</td>
          <td>{{ call.kwargs|escape }}</td>
          <td>{{ call.backend }}</td>
        </tr>
        <tr class="djUnselected djToggleDetails_{{ forloop.counter }}" id="cacheDetails_{{ forloop.counter }}">
          <td colspan="1"></td>
          <td colspan="5"><pre class="djdt-stack">{{ call.trace }}</pre></td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% endif %}
