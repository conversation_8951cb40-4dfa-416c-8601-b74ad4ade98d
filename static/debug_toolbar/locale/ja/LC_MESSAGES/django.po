# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012,2014,2020
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-08-14 10:25-0500\n"
"PO-Revision-Date: 2021-08-14 15:25+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Japanese (http://www.transifex.com/django-debug-toolbar/django-debug-toolbar/language/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "デバッグツールバー"

#: panels/cache.py:227
msgid "Cache"
msgstr "キャッシュ"

#: panels/cache.py:234
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] ""

#: panels/cache.py:246
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] ""

#: panels/headers.py:33
msgid "Headers"
msgstr "ヘッダー"

#: panels/history/panel.py:20 panels/history/panel.py:21
msgid "History"
msgstr ""

#: panels/logging.py:63
msgid "Logging"
msgstr "ロギング"

#: panels/logging.py:69
#, python-format
msgid "%(count)s message"
msgid_plural "%(count)s messages"
msgstr[0] "%(count)s個のメッセージ"

#: panels/logging.py:73
msgid "Log messages"
msgstr "ログメッセージ"

#: panels/profiling.py:150
msgid "Profiling"
msgstr "プロファイル"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "リダイレクトに割込み"

#: panels/request.py:16
msgid "Request"
msgstr "リクエスト"

#: panels/request.py:36
msgid "<no view>"
msgstr ""

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<利用不可>"

#: panels/settings.py:24
msgid "Settings"
msgstr "設定"

#: panels/settings.py:27
#, python-format
msgid "Settings from %s"
msgstr ""

#: panels/signals.py:58
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] ""

#: panels/signals.py:66
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] ""

#: panels/signals.py:73
msgid "Signals"
msgstr "シグナル"

#: panels/sql/panel.py:24
msgid "Autocommit"
msgstr ""

#: panels/sql/panel.py:25
msgid "Read uncommitted"
msgstr ""

#: panels/sql/panel.py:26
msgid "Read committed"
msgstr ""

#: panels/sql/panel.py:27
msgid "Repeatable read"
msgstr ""

#: panels/sql/panel.py:28
msgid "Serializable"
msgstr ""

#: panels/sql/panel.py:40
msgid "Idle"
msgstr ""

#: panels/sql/panel.py:41
msgid "Active"
msgstr ""

#: panels/sql/panel.py:42
msgid "In transaction"
msgstr ""

#: panels/sql/panel.py:43
msgid "In error"
msgstr ""

#: panels/sql/panel.py:44
msgid "Unknown"
msgstr ""

#: panels/sql/panel.py:109
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:114
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] ""

#: panels/sql/panel.py:127
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""

#: panels/staticfiles.py:85
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr ""

#: panels/staticfiles.py:106
msgid "Static files"
msgstr "静的ファイル"

#: panels/staticfiles.py:112
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] ""

#: panels/templates/panel.py:144
msgid "Templates"
msgstr "テンプレート"

#: panels/templates/panel.py:149
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr ""

#: panels/templates/panel.py:181
msgid "No origin"
msgstr ""

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr ""

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr ""

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/logging.html:7
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "時間"

#: panels/timer.py:44
msgid "User CPU time"
msgstr ""

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr ""

#: panels/timer.py:45
msgid "System CPU time"
msgstr ""

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr ""

#: panels/timer.py:46
msgid "Total CPU time"
msgstr ""

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr ""

#: panels/timer.py:47
msgid "Elapsed time"
msgstr ""

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr ""

#: panels/timer.py:49
msgid "Context switches"
msgstr ""

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr ""

#: panels/versions.py:19
msgid "Versions"
msgstr "バージョン"

#: templates/debug_toolbar/base.html:18
msgid "Hide toolbar"
msgstr "ツールバーを隠す"

#: templates/debug_toolbar/base.html:18
msgid "Hide"
msgstr "隠す"

#: templates/debug_toolbar/base.html:25
msgid "Show toolbar"
msgstr "ツールバーを表示"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr ""

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "時間 (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "引数"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "キーワード引数"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "バックエンド"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "リクエストヘッダー"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "キー"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:11
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "値"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "レスポンスヘッダー"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "パス"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr ""

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr ""

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:10
msgid "Variable"
msgstr "変数"

#: templates/debug_toolbar/panels/logging.html:6
msgid "Level"
msgstr "レベル"

#: templates/debug_toolbar/panels/logging.html:8
msgid "Channel"
msgstr ""

#: templates/debug_toolbar/panels/logging.html:9
msgid "Message"
msgstr "メッセージ"

#: templates/debug_toolbar/panels/logging.html:10
#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr ""

#: templates/debug_toolbar/panels/logging.html:26
msgid "No messages logged"
msgstr "ロギングされたメッセージはありません"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr ""

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "ビューの情報"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "ビュー関数"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "URL名"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "クッキー"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "クッキーはありません"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "セッションデータ"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "セッションデータはありません"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr ""

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "設定項目"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "シグナル"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "レシーバー"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] ""

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "タイムライン"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "実行されたSQL"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "データベース"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr ""

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "エラー"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr ""

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr ""

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "静的ファイルのパス"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr ""

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "ありません"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "静的ファイルを含むアプリケーション"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "静的ファイル"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] ""

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] ""

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "テンプレート"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "コンテキストプロセッサー"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "リソース"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "名前"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "バージョン"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr ""

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""

#: views.py:15
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
