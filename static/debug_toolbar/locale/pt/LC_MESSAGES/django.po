# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# jose<PERSON>raes <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-04-25 21:52+0200\n"
"PO-Revision-Date: 2014-04-25 19:53+0000\n"
"Last-Translator: Aymeric Augustin <<EMAIL>>\n"
"Language-Team: Portuguese (http://www.transifex.com/projects/p/django-debug-toolbar/language/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps.py:11
msgid "Debug Toolbar"
msgstr ""

#: views.py:14
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""

#: panels/cache.py:191
msgid "Cache"
msgstr ""

#: panels/cache.py:196
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] ""
msgstr[1] ""

#: panels/cache.py:204
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] ""
msgstr[1] ""

#: panels/headers.py:35
msgid "Headers"
msgstr ""

#: panels/logging.py:64
msgid "Logging"
msgstr "Registo"

#: panels/logging.py:70
#, python-format
msgid "%(count)s message"
msgid_plural "%(count)s messages"
msgstr[0] ""
msgstr[1] ""

#: panels/logging.py:73
msgid "Log messages"
msgstr ""

#: panels/profiling.py:127
msgid "Profiling"
msgstr ""

#: panels/redirects.py:17
msgid "Intercept redirects"
msgstr "Intercetar redirecionamentos"

#: panels/request.py:18
msgid "Request"
msgstr "Pedido"

#: panels/request.py:35
msgid "<no view>"
msgstr ""

#: panels/request.py:47
msgid "<unavailable>"
msgstr "<indisponível>"

#: panels/settings.py:20
msgid "Settings"
msgstr "Configurações"

#: panels/settings.py:23
#, python-format
msgid "Settings from <code>%s</code>"
msgstr ""

#: panels/signals.py:45
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] ""
msgstr[1] ""

#: panels/signals.py:48
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] ""
msgstr[1] ""

#: panels/signals.py:53
msgid "Signals"
msgstr "Sinais"

#: panels/staticfiles.py:89
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr ""

#: panels/staticfiles.py:107
msgid "Static files"
msgstr "Ficheiros estáticos"

#: panels/staticfiles.py:112
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] ""
msgstr[1] ""

#: panels/timer.py:23
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr ""

#: panels/timer.py:28
#, python-format
msgid "Total: %0.2fms"
msgstr "Total: %0.2fms"

#: panels/timer.py:34 templates/debug_toolbar/panels/logging.html:7
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Tempo"

#: panels/timer.py:42
msgid "User CPU time"
msgstr ""

#: panels/timer.py:42
#, python-format
msgid "%(utime)0.3f msec"
msgstr ""

#: panels/timer.py:43
msgid "System CPU time"
msgstr ""

#: panels/timer.py:43
#, python-format
msgid "%(stime)0.3f msec"
msgstr ""

#: panels/timer.py:44
msgid "Total CPU time"
msgstr ""

#: panels/timer.py:44
#, python-format
msgid "%(total)0.3f msec"
msgstr ""

#: panels/timer.py:45
msgid "Elapsed time"
msgstr ""

#: panels/timer.py:45
#, python-format
msgid "%(total_time)0.3f msec"
msgstr ""

#: panels/timer.py:46
msgid "Context switches"
msgstr ""

#: panels/timer.py:46
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr ""

#: panels/versions.py:25
msgid "Versions"
msgstr "Versões"

#: panels/sql/panel.py:22
msgid "Autocommit"
msgstr ""

#: panels/sql/panel.py:23
msgid "Read uncommitted"
msgstr ""

#: panels/sql/panel.py:24
msgid "Read committed"
msgstr ""

#: panels/sql/panel.py:25
msgid "Repeatable read"
msgstr ""

#: panels/sql/panel.py:26
msgid "Serializable"
msgstr "Variável"

#: panels/sql/panel.py:37
msgid "Idle"
msgstr ""

#: panels/sql/panel.py:38
msgid "Active"
msgstr "Acção"

#: panels/sql/panel.py:39
msgid "In transaction"
msgstr ""

#: panels/sql/panel.py:40
msgid "In error"
msgstr "Erro"

#: panels/sql/panel.py:41
msgid "Unknown"
msgstr "Desconhecido"

#: panels/sql/panel.py:105
msgid "SQL"
msgstr ""

#: panels/templates/panel.py:141
msgid "Templates"
msgstr "Templates"

#: panels/templates/panel.py:146
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Templates (%(num_templates)s renderizados)"

#: templates/debug_toolbar/base.html:19
msgid "Hide toolbar"
msgstr "Ocultar barra"

#: templates/debug_toolbar/base.html:19
msgid "Hide"
msgstr "Ocultar"

#: templates/debug_toolbar/base.html:25
msgid "Disable for next and successive requests"
msgstr "Desactivar para o seguinte e sucessivos pedidos"

#: templates/debug_toolbar/base.html:25
msgid "Enable for next and successive requests"
msgstr "Activar para o próximo e sucessivos pedidos"

#: templates/debug_toolbar/base.html:47
msgid "Show toolbar"
msgstr "Mostrar barra"

#: templates/debug_toolbar/base.html:53
msgid "Close"
msgstr "Fechar"

#: templates/debug_toolbar/redirect.html:8
msgid "Location:"
msgstr "Localização"

#: templates/debug_toolbar/redirect.html:10
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Resumo"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Comandos"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:20
msgid "Time (ms)"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Tipo"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Chave"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/request.html:33
#: templates/debug_toolbar/panels/request.html:59
#: templates/debug_toolbar/panels/request.html:85
#: templates/debug_toolbar/panels/request.html:110
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Valor"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""

#: templates/debug_toolbar/panels/logging.html:6
msgid "Level"
msgstr "Nível"

#: templates/debug_toolbar/panels/logging.html:8
msgid "Channel"
msgstr ""

#: templates/debug_toolbar/panels/logging.html:9
msgid "Message"
msgstr "Mensagem"

#: templates/debug_toolbar/panels/logging.html:10
#: templates/debug_toolbar/panels/staticfiles.html:45
msgid "Location"
msgstr "Localização"

#: templates/debug_toolbar/panels/logging.html:26
msgid "No messages logged"
msgstr "Nenhuma mensagem registada"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr ""

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr ""

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr ""

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr ""

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr ""

#: templates/debug_toolbar/panels/request.html:32
#: templates/debug_toolbar/panels/request.html:58
#: templates/debug_toolbar/panels/request.html:84
#: templates/debug_toolbar/panels/request.html:109
msgid "Variable"
msgstr "Variável"

#: templates/debug_toolbar/panels/request.html:46
msgid "No cookies"
msgstr ""

#: templates/debug_toolbar/panels/request.html:50
msgid "Session data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:72
msgid "No session data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:76
msgid "GET data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:98
msgid "No GET data"
msgstr "Sem dados GET"

#: templates/debug_toolbar/panels/request.html:102
msgid "POST data"
msgstr "dados POST"

#: templates/debug_toolbar/panels/request.html:123
msgid "No POST data"
msgstr "Sem variáveis POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Configurações"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Sinal"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Providing"
msgstr ""

#: templates/debug_toolbar/panels/signals.html:7
msgid "Receivers"
msgstr "Receptores"

#: templates/debug_toolbar/panels/sql.html:7
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/sql.html:18
msgid "Query"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:19
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:21
msgid "Action"
msgstr "Acção"

#: templates/debug_toolbar/panels/sql.html:64
msgid "Connection:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:66
msgid "Isolation level:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:69
msgid "Transaction status:"
msgstr "Estado da transacção:"

#: templates/debug_toolbar/panels/sql.html:83
msgid "(unknown)"
msgstr "(desconhecido)"

#: templates/debug_toolbar/panels/sql.html:92
msgid "No SQL queries were recorded during this request."
msgstr "Nenhuma query SQL foi registada durante este pedido."

#: templates/debug_toolbar/panels/sql_explain.html:3
#: templates/debug_toolbar/panels/sql_profile.html:3
#: templates/debug_toolbar/panels/sql_select.html:3
#: templates/debug_toolbar/panels/template_source.html:3
msgid "Back"
msgstr "Voltar"

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "SQL Executado"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr ""

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr ""

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Erro"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr ""

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Set vazio"

#: templates/debug_toolbar/panels/staticfiles.html:4
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:8
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefixo %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:12
#: templates/debug_toolbar/panels/staticfiles.html:23
#: templates/debug_toolbar/panels/staticfiles.html:35
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:28
#: templates/debug_toolbar/panels/templates.html:43
msgid "None"
msgstr "Nenhum"

#: templates/debug_toolbar/panels/staticfiles.html:15
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:26
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Ficheiro estático"
msgstr[1] "Ficheiros estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:40
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Path"
msgstr ""

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] ""
msgstr[1] "Caminho da Template"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/templates.html:21
#: templates/debug_toolbar/panels/templates.html:37
msgid "Toggle context"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:31
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] ""
msgstr[1] "Processador de Contexto"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Recurso"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:5
msgid "Name"
msgstr "Nome"

#: templates/debug_toolbar/panels/versions.html:6
msgid "Version"
msgstr "Versão"
