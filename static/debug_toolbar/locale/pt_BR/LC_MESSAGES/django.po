# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014
# <PERSON>, 2009
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-04-25 21:52+0200\n"
"PO-Revision-Date: 2014-04-25 19:53+0000\n"
"Last-Translator: Aymeric Augustin <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/projects/p/django-debug-toolbar/language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: apps.py:11
msgid "Debug Toolbar"
msgstr ""

#: views.py:14
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr "Os dados para este painel não está mais disponível. Por favor, recarregue a página e tente novamente."

#: panels/cache.py:191
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:196
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d chamada em %(time).2fms"
msgstr[1] "%(cache_calls)d chamadas em %(time).2fms"

#: panels/cache.py:204
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Chamadas ao cache de %(count)d backend"
msgstr[1] "Chamadas ao cache de %(count)d backends"

#: panels/headers.py:35
msgid "Headers"
msgstr "Cabeçalhos"

#: panels/logging.py:64
msgid "Logging"
msgstr "Logs"

#: panels/logging.py:70
#, python-format
msgid "%(count)s message"
msgid_plural "%(count)s messages"
msgstr[0] "%(count)s mensagem"
msgstr[1] "%(count)s mensagens"

#: panels/logging.py:73
msgid "Log messages"
msgstr "Mensagens de log"

#: panels/profiling.py:127
msgid "Profiling"
msgstr "Profiling"

#: panels/redirects.py:17
msgid "Intercept redirects"
msgstr "Interceptar redirecionamentos"

#: panels/request.py:18
msgid "Request"
msgstr "Requisição"

#: panels/request.py:35
msgid "<no view>"
msgstr "<nenhuma vista>"

#: panels/request.py:47
msgid "<unavailable>"
msgstr "<indisponível>"

#: panels/settings.py:20
msgid "Settings"
msgstr "Configurações"

#: panels/settings.py:23
#, python-format
msgid "Settings from <code>%s</code>"
msgstr "Configurações em: <code>%s</code>"

#: panels/signals.py:45
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d receptor de 1 sinal"
msgstr[1] "%(num_receivers)d receptores de 1 sinal"

#: panels/signals.py:48
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d receptor de %(num_signals)d sinais"
msgstr[1] "%(num_receivers)d receptores de %(num_signals)d sinais"

#: panels/signals.py:53
msgid "Signals"
msgstr "Sinais"

#: panels/staticfiles.py:89
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Arquivos estáticos (%(num_found)s encontrados, %(num_used)s sendo utilizados)"

#: panels/staticfiles.py:107
msgid "Static files"
msgstr "Arquivos estáticos"

#: panels/staticfiles.py:112
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s arquivo utilizado"
msgstr[1] "%(num_used)s arquivos utilizados"

#: panels/timer.py:23
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:28
#, python-format
msgid "Total: %0.2fms"
msgstr "Total: %0.2fms"

#: panels/timer.py:34 templates/debug_toolbar/panels/logging.html:7
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Tempo"

#: panels/timer.py:42
msgid "User CPU time"
msgstr "Tempo de CPU do usuário"

#: panels/timer.py:42
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f ms"

#: panels/timer.py:43
msgid "System CPU time"
msgstr "Tempo de CPU do sistema"

#: panels/timer.py:43
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f ms"

#: panels/timer.py:44
msgid "Total CPU time"
msgstr "Tempo total de CPU"

#: panels/timer.py:44
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f ms"

#: panels/timer.py:45
msgid "Elapsed time"
msgstr "Tempo decorrido"

#: panels/timer.py:45
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f ms"

#: panels/timer.py:46
msgid "Context switches"
msgstr "Mudanças de contexto"

#: panels/timer.py:46
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d voluntário, %(ivcsw)d involuntário"

#: panels/versions.py:25
msgid "Versions"
msgstr "Versões"

#: panels/sql/panel.py:22
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:23
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:24
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:25
msgid "Repeatable read"
msgstr "Leitura repetida"

#: panels/sql/panel.py:26
msgid "Serializable"
msgstr "Variável"

#: panels/sql/panel.py:37
msgid "Idle"
msgstr "Ocioso"

#: panels/sql/panel.py:38
msgid "Active"
msgstr "Ação"

#: panels/sql/panel.py:39
msgid "In transaction"
msgstr "Na transação"

#: panels/sql/panel.py:40
msgid "In error"
msgstr "Erro"

#: panels/sql/panel.py:41
msgid "Unknown"
msgstr "Desconhecido"

#: panels/sql/panel.py:105
msgid "SQL"
msgstr "SQL"

#: panels/templates/panel.py:141
msgid "Templates"
msgstr "Templates"

#: panels/templates/panel.py:146
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Templates (%(num_templates)s renderizados)"

#: templates/debug_toolbar/base.html:19
msgid "Hide toolbar"
msgstr "Ocultar barra de ferramentas"

#: templates/debug_toolbar/base.html:19
msgid "Hide"
msgstr "Esconder"

#: templates/debug_toolbar/base.html:25
msgid "Disable for next and successive requests"
msgstr "Desativar para próximas requisições"

#: templates/debug_toolbar/base.html:25
msgid "Enable for next and successive requests"
msgstr "Habilitar para próximas requisições"

#: templates/debug_toolbar/base.html:47
msgid "Show toolbar"
msgstr "Mostrar barra de ferramentas"

#: templates/debug_toolbar/base.html:53
msgid "Close"
msgstr "Fechar"

#: templates/debug_toolbar/redirect.html:8
msgid "Location:"
msgstr "Localização:"

#: templates/debug_toolbar/redirect.html:10
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr "O Django Debug Toolbar interceptou um redirecionamento para a URL acima para fins de visualização de depuração. Você pode clicar no link acima para continuar com o redirecionamento normalmente."

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Resumo"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Total de chamadas"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Tempo total"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Acessos ao cache"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Falhas de cache"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Comandos"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Chamadas"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:20
msgid "Time (ms)"
msgstr "Tempo (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Tipo"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumentos"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Argumentos"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Cabeçalhos de Requisição"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Chave"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/request.html:33
#: templates/debug_toolbar/panels/request.html:59
#: templates/debug_toolbar/panels/request.html:85
#: templates/debug_toolbar/panels/request.html:110
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Valor"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Cabeçalhos de Resposta"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Ambiente WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr "Uma vez que o ambiente WSGI herda o ambiente do servidor, apenas um subconjunto significativo é mostrado abaixo."

#: templates/debug_toolbar/panels/logging.html:6
msgid "Level"
msgstr "Nível"

#: templates/debug_toolbar/panels/logging.html:8
msgid "Channel"
msgstr "Canal"

#: templates/debug_toolbar/panels/logging.html:9
msgid "Message"
msgstr "Mensagem"

#: templates/debug_toolbar/panels/logging.html:10
#: templates/debug_toolbar/panels/staticfiles.html:45
msgid "Location"
msgstr "Localização"

#: templates/debug_toolbar/panels/logging.html:26
msgid "No messages logged"
msgstr "Nenhuma mensagem logada"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Chamar"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "CumTime"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Per"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "TotTime"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Contagem"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Ver informação"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Função View"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Nome da URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:32
#: templates/debug_toolbar/panels/request.html:58
#: templates/debug_toolbar/panels/request.html:84
#: templates/debug_toolbar/panels/request.html:109
msgid "Variable"
msgstr "Variável"

#: templates/debug_toolbar/panels/request.html:46
msgid "No cookies"
msgstr "Sem Cookies"

#: templates/debug_toolbar/panels/request.html:50
msgid "Session data"
msgstr "Dados de Sessão"

#: templates/debug_toolbar/panels/request.html:72
msgid "No session data"
msgstr "Sem dados de Sessão"

#: templates/debug_toolbar/panels/request.html:76
msgid "GET data"
msgstr "Dados de GET"

#: templates/debug_toolbar/panels/request.html:98
msgid "No GET data"
msgstr "Não há dados de GET"

#: templates/debug_toolbar/panels/request.html:102
msgid "POST data"
msgstr "Dados de POST"

#: templates/debug_toolbar/panels/request.html:123
msgid "No POST data"
msgstr "Não há dados de POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Configuração"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Sinais"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Providing"
msgstr "Fornecendo"

#: templates/debug_toolbar/panels/signals.html:7
msgid "Receivers"
msgstr "Recebedores"

#: templates/debug_toolbar/panels/sql.html:7
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s consulta"
msgstr[1] "%(num)s consultas"

#: templates/debug_toolbar/panels/sql.html:18
msgid "Query"
msgstr "Query"

#: templates/debug_toolbar/panels/sql.html:19
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Linha do tempo"

#: templates/debug_toolbar/panels/sql.html:21
msgid "Action"
msgstr "Ação"

#: templates/debug_toolbar/panels/sql.html:64
msgid "Connection:"
msgstr "Conexão:"

#: templates/debug_toolbar/panels/sql.html:66
msgid "Isolation level:"
msgstr "Nível de isolamento:"

#: templates/debug_toolbar/panels/sql.html:69
msgid "Transaction status:"
msgstr "Status da transação:"

#: templates/debug_toolbar/panels/sql.html:83
msgid "(unknown)"
msgstr "(unknown)"

#: templates/debug_toolbar/panels/sql.html:92
msgid "No SQL queries were recorded during this request."
msgstr "Nenhuma consulta SQL foi registrada durante esta requisição."

#: templates/debug_toolbar/panels/sql_explain.html:3
#: templates/debug_toolbar/panels/sql_profile.html:3
#: templates/debug_toolbar/panels/sql_select.html:3
#: templates/debug_toolbar/panels/template_source.html:3
msgid "Back"
msgstr "Voltar"

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL explicada"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "SQL Executada"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Banco de dados"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL perfilado"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Erro"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL selecionada"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Conjunto vazio"

#: templates/debug_toolbar/panels/staticfiles.html:4
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Caminho do arquivo estático"
msgstr[1] "Caminho dos arquivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:8
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefixo %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:12
#: templates/debug_toolbar/panels/staticfiles.html:23
#: templates/debug_toolbar/panels/staticfiles.html:35
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:28
#: templates/debug_toolbar/panels/templates.html:43
msgid "None"
msgstr "Nenhum"

#: templates/debug_toolbar/panels/staticfiles.html:15
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Arquivo estático de app"
msgstr[1] "Arquivos estáticos de apps"

#: templates/debug_toolbar/panels/staticfiles.html:26
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Arquivo estático"
msgstr[1] "Arquivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:40
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s arquivo"
msgstr[1] "%(payload_count)s arquivos"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Path"
msgstr "Caminho"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Origem do Template:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Caminho do Template"
msgstr[1] "Caminho do Templates"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Template"
msgstr[1] "Templates"

#: templates/debug_toolbar/panels/templates.html:21
#: templates/debug_toolbar/panels/templates.html:37
msgid "Toggle context"
msgstr "Alternar contexto"

#: templates/debug_toolbar/panels/templates.html:31
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] ""
msgstr[1] "Processador do Contexto"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Uso de recursos"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Recurso"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Cronometragem do Navegador"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Atributo de Cronometragem"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Milissegundos desde início de navegação (+length)"

#: templates/debug_toolbar/panels/versions.html:5
msgid "Name"
msgstr "Nome"

#: templates/debug_toolbar/panels/versions.html:6
msgid "Version"
msgstr "Versão"
